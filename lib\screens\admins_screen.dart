import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../utils/app_colors.dart';
import '../models/admin.dart';
import '../services/database_helper.dart';

class AdminsScreen extends StatefulWidget {
  const AdminsScreen({super.key});

  @override
  State<AdminsScreen> createState() => _AdminsScreenState();
}

class _AdminsScreenState extends State<AdminsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  final TextEditingController _fullNameController = TextEditingController();
  
  List<Admin> _admins = [];
  List<Admin> _filteredAdmins = [];
  bool _isLoading = false;
  Admin? _editingAdmin;
  
  // قائمة الصلاحيات المتاحة
  final Map<String, String> _availablePermissions = {
    'booking_receipt': 'وصل الحجز',
    'bookings': 'إدارة الحجوزات',
    'delivery': 'استلام الطلبات',
    'cashbox': 'إدارة الصندوق',
    'services': 'إدارة الخدمات والعروض',
    'customers': 'إدارة العملاء',
    'reports': 'التقارير',
    'notifications': 'الإشعارات',
    'admins': 'إدارة المشرفين',
    'todo': 'قائمة المهام',
    'backup': 'النسخ الاحتياطي',
    'settings': 'إعدادات الشركة',
  };
  
  // الصلاحيات المحددة للمشرف الجديد أو المحرر
  Set<String> _selectedPermissions = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadAdmins();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _fullNameController.dispose();
    super.dispose();
  }

  Future<void> _loadAdmins() async {
    setState(() => _isLoading = true);
    try {
      final adminMaps = await _databaseHelper.getAllAdmins();
      setState(() {
        _admins = adminMaps.map((map) => Admin.fromMap(map)).toList();
        _filteredAdmins = _admins;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _filterAdmins() {
    setState(() {
      _filteredAdmins = _admins.where((admin) {
        final searchTerm = _searchController.text.toLowerCase();
        return admin.fullName.toLowerCase().contains(searchTerm) ||
               admin.username.toLowerCase().contains(searchTerm);
      }).toList();
    });
  }

  Future<void> _saveAdmin() async {
    if (_usernameController.text.isEmpty || 
        _passwordController.text.isEmpty ||
        _confirmPasswordController.text.isEmpty ||
        _fullNameController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى ملء الحقول المطلوبة'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    // التحقق من تطابق كلمات المرور
    if (_passwordController.text != _confirmPasswordController.text) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('كلمة المرور وتأكيد كلمة المرور غير متطابقتين'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    // التحقق من طول كلمة المرور
    if (_passwordController.text.length < 6) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    try {
      if (_editingAdmin == null) {
        // إضافة مشرف جديد
        final admin = Admin(
          username: _usernameController.text.trim(),
          password: _passwordController.text,
          fullName: _fullNameController.text.trim(),
          phone: '', // قيمة افتراضية فارغة
          email: null,
          role: AdminRole.employee, // دور افتراضي
        );

        // إضافة الصلاحيات كـ metadata إضافية
        final adminMap = admin.toMap();
        adminMap['permissions'] = _selectedPermissions.join(',');

        await _databaseHelper.insertAdmin(adminMap);
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة المشرف بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      } else {
        // تحديث مشرف موجود
        final updatedAdmin = _editingAdmin!.copyWith(
          username: _usernameController.text.trim(),
          password: _passwordController.text,
          fullName: _fullNameController.text.trim(),
          updatedAt: DateTime.now(),
        );

        // إضافة الصلاحيات كـ metadata إضافية
        final adminMap = updatedAdmin.toMap();
        adminMap['permissions'] = _selectedPermissions.join(',');

        await _databaseHelper.updateAdmin(adminMap);
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث المشرف بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }

      _clearForm();
      await _loadAdmins();
      
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في حفظ البيانات: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  void _clearForm() {
    _usernameController.clear();
    _passwordController.clear();
    _confirmPasswordController.clear();
    _fullNameController.clear();
    _selectedPermissions.clear();
    _editingAdmin = null;
  }

  void _editAdmin(Admin admin) async {
    setState(() {
      _editingAdmin = admin;
      _usernameController.text = admin.username;
      _passwordController.text = admin.password;
      _confirmPasswordController.text = admin.password;
      _fullNameController.text = admin.fullName;
    });
    
    // تحميل الصلاحيات من قاعدة البيانات
    try {
      final adminMap = await _databaseHelper.getAdminById(admin.id);
      if (adminMap != null && adminMap['permissions'] != null) {
        final permissions = adminMap['permissions'].toString().split(',');
        setState(() {
          _selectedPermissions = permissions.where((p) => p.isNotEmpty).toSet();
        });
      } else {
        setState(() {
          _selectedPermissions.clear();
        });
      }
    } catch (e) {
      setState(() {
        _selectedPermissions.clear();
      });
    }
    
    _tabController.animateTo(0);
  }

  Future<void> _toggleAdminStatus(Admin admin) async {
    try {
      await _databaseHelper.toggleAdminStatus(admin.id, !admin.isActive);
      await _loadAdmins();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(admin.isActive ? 'تم إلغاء تفعيل المشرف' : 'تم تفعيل المشرف'),
          backgroundColor: AppColors.success,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحديث حالة المشرف: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _deleteAdmin(Admin admin) async {
    try {
      await _databaseHelper.deleteAdmin(admin.id);
      await _loadAdmins();
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حذف المشرف بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في حذف المشرف: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Widget _buildAddAdminTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.info, color: AppColors.primary),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'يمكن استخدام حسابات المشرفين هذه لتسجيل الدخول للتطبيق',
                    style: TextStyle(color: AppColors.primary, fontSize: 14),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 16),
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Text(
                    _editingAdmin == null ? 'إضافة مشرف جديد' : 'تحديث بيانات المشرف',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                  const SizedBox(height: 20),
                  TextFormField(
                    controller: _usernameController,
                    decoration: InputDecoration(
                      labelText: 'اسم المستخدم *',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      prefixIcon: const Icon(Icons.person),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _passwordController,
                    obscureText: true,
                    decoration: InputDecoration(
                      labelText: 'كلمة المرور *',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      prefixIcon: const Icon(Icons.lock),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _confirmPasswordController,
                    obscureText: true,
                    decoration: InputDecoration(
                      labelText: 'تأكيد كلمة المرور *',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      prefixIcon: const Icon(Icons.lock_outline),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _fullNameController,
                    decoration: InputDecoration(
                      labelText: 'الاسم الكامل *',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      prefixIcon: const Icon(Icons.badge),
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // قسم الصلاحيات
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.security, color: AppColors.primaryBlue),
                            const SizedBox(width: 8),
                            const Text(
                              'صلاحيات الوصول',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: AppColors.primaryBlue,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        const Text(
                          'حدد النوافذ التي يمكن للمشرف الوصول إليها:',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: _availablePermissions.entries.map((permission) {
                            final isSelected = _selectedPermissions.contains(permission.key);
                            return FilterChip(
                              label: Text(permission.value),
                              selected: isSelected,
                              onSelected: (selected) {
                                setState(() {
                                  if (selected) {
                                    _selectedPermissions.add(permission.key);
                                  } else {
                                    _selectedPermissions.remove(permission.key);
                                  }
                                });
                              },
                              selectedColor: AppColors.primaryBlue.withOpacity(0.2),
                              checkmarkColor: AppColors.primaryBlue,
                            );
                          }).toList(),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            TextButton(
                              onPressed: () {
                                setState(() {
                                  _selectedPermissions = _availablePermissions.keys.toSet();
                                });
                              },
                              child: const Text('تحديد الكل'),
                            ),
                            const SizedBox(width: 16),
                            TextButton(
                              onPressed: () {
                                setState(() {
                                  _selectedPermissions.clear();
                                });
                              },
                              child: const Text('إلغاء تحديد الكل'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    children: [
                      if (_editingAdmin != null) ...[
                        Expanded(
                          child: OutlinedButton(
                            onPressed: _clearForm,
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: const Text('إلغاء'),
                          ),
                        ),
                        const SizedBox(width: 16),
                      ],
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _saveAdmin,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primaryBlue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(
                            _editingAdmin == null ? 'إضافة المشرف' : 'تحديث البيانات',
                            style: const TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdminsListTab() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Card(
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      const Icon(Icons.admin_panel_settings, color: AppColors.primaryBlue),
                      const SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'إجمالي المشرفين',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                          Text(
                            '${_admins.length} مشرف',
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: AppColors.primaryBlue,
                            ),
                          ),
                        ],
                      ),
                      const Spacer(),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          const Text(
                            'المفعلين',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                          Text(
                            '${_admins.where((a) => a.isActive).length}',
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: AppColors.success,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'البحث في المشرفين...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onChanged: (value) => _filterAdmins(),
              ),
            ],
          ),
        ),
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _filteredAdmins.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.admin_panel_settings,
                            size: 64,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'لا توجد مشرفين',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    )
                  : AnimationLimiter(
                      child: ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _filteredAdmins.length,
                        itemBuilder: (context, index) {
                          final admin = _filteredAdmins[index];
                          
                          return AnimationConfiguration.staggeredList(
                            position: index,
                            duration: const Duration(milliseconds: 375),
                            child: SlideAnimation(
                              verticalOffset: 50.0,
                              child: FadeInAnimation(
                                child: Card(
                                  margin: const EdgeInsets.only(bottom: 12),
                                  elevation: 2,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    side: BorderSide(
                                      color: admin.isActive 
                                          ? AppColors.success.withOpacity(0.3)
                                          : AppColors.error.withOpacity(0.3),
                                      width: 1,
                                    ),
                                  ),
                                  child: ListTile(
                                    leading: const CircleAvatar(
                                      backgroundColor: AppColors.primaryBlue,
                                      child: Icon(
                                        Icons.person,
                                        color: Colors.white,
                                      ),
                                    ),
                                    title: Text(
                                      admin.fullName,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text('المستخدم: ${admin.username}'),
                                        const SizedBox(height: 4),
                                        FutureBuilder<Map<String, dynamic>?>(
                                          future: _databaseHelper.getAdminById(admin.id),
                                          builder: (context, snapshot) {
                                            if (snapshot.hasData && snapshot.data?['permissions'] != null) {
                                              final permissions = snapshot.data!['permissions'].toString().split(',');
                                              final validPermissions = permissions.where((p) => p.isNotEmpty && _availablePermissions.containsKey(p)).toList();
                                              
                                              if (validPermissions.isNotEmpty) {
                                                return Wrap(
                                                  spacing: 4,
                                                  runSpacing: 4,
                                                  children: validPermissions.take(3).map((permission) {
                                                    return Container(
                                                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                                      decoration: BoxDecoration(
                                                        color: AppColors.info.withOpacity(0.1),
                                                        borderRadius: BorderRadius.circular(8),
                                                      ),
                                                      child: Text(
                                                        _availablePermissions[permission] ?? permission,
                                                        style: const TextStyle(
                                                          fontSize: 8,
                                                          color: AppColors.info,
                                                        ),
                                                      ),
                                                    );
                                                  }).toList()
                                                    ..addAll(validPermissions.length > 3 ? [
                                                      Container(
                                                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                                        decoration: BoxDecoration(
                                                          color: Colors.grey.withOpacity(0.1),
                                                          borderRadius: BorderRadius.circular(8),
                                                        ),
                                                        child: Text(
                                                          '+${validPermissions.length - 3}',
                                                          style: const TextStyle(
                                                            fontSize: 8,
                                                            color: Colors.grey,
                                                          ),
                                                        ),
                                                      ),
                                                    ] : []),
                                                );
                                              }
                                            }
                                            return const Text(
                                              'لا توجد صلاحيات محددة',
                                              style: TextStyle(
                                                fontSize: 10,
                                                color: Colors.grey,
                                              ),
                                            );
                                          },
                                        ),
                                        const SizedBox(height: 4),
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 8,
                                            vertical: 2,
                                          ),
                                          decoration: BoxDecoration(
                                            color: admin.isActive 
                                                ? AppColors.success.withOpacity(0.1)
                                                : AppColors.error.withOpacity(0.1),
                                            borderRadius: BorderRadius.circular(12),
                                          ),
                                          child: Text(
                                            admin.isActive ? 'مفعل' : 'غير مفعل',
                                            style: TextStyle(
                                              fontSize: 10,
                                              color: admin.isActive 
                                                  ? AppColors.success
                                                  : AppColors.error,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    trailing: PopupMenuButton(
                                      itemBuilder: (context) => [
                                        PopupMenuItem(
                                          value: 'edit',
                                          child: const Row(
                                            children: [
                                              Icon(Icons.edit, size: 16),
                                              SizedBox(width: 8),
                                              Text('تعديل'),
                                            ],
                                          ),
                                        ),
                                        PopupMenuItem(
                                          value: 'toggle',
                                          child: Row(
                                            children: [
                                              Icon(
                                                admin.isActive ? Icons.block : Icons.check_circle,
                                                size: 16,
                                                color: admin.isActive ? AppColors.error : AppColors.success,
                                              ),
                                              const SizedBox(width: 8),
                                              Text(
                                                admin.isActive ? 'إلغاء التفعيل' : 'تفعيل',
                                                style: TextStyle(
                                                  color: admin.isActive ? AppColors.error : AppColors.success,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        if (admin.role != AdminRole.owner)
                                          PopupMenuItem(
                                            value: 'delete',
                                            child: const Row(
                                              children: [
                                                Icon(Icons.delete, size: 16, color: AppColors.error),
                                                SizedBox(width: 8),
                                                Text('حذف', style: TextStyle(color: AppColors.error)),
                                              ],
                                            ),
                                          ),
                                      ],
                                      onSelected: (value) {
                                        switch (value) {
                                          case 'edit':
                                            _editAdmin(admin);
                                            break;
                                          case 'toggle':
                                            _toggleAdminStatus(admin);
                                            break;
                                          case 'delete':
                                            _showDeleteDialog(admin);
                                            break;
                                        }
                                      },
                                    ),
                                    isThreeLine: true,
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
        ),
      ],
    );
  }

  void _showDeleteDialog(Admin admin) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المشرف "${admin.fullName}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteAdmin(admin);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'إدارة المشرفين',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(
              icon: Icon(Icons.add),
              text: 'إضافة',
            ),
            Tab(
              icon: Icon(Icons.people),
              text: 'المشرفين',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAddAdminTab(),
          _buildAdminsListTab(),
        ],
      ),
    );
  }
}
