# نظام إدارة استوديو التصوير

## العملة المستخدمة: الدينار العراقي (د.ع)

### التحديثات المطبقة:

#### 1. مُنسق العملة (CurrencyFormatter)
- تم إنشاء نظام شامل لتنسيق العملة العراقية
- رمز العملة: **د.ع** (دينار عراقي)
- كود العملة الدولي: **IQD**
- مثال على التنسيق: `25,000 د.ع`

#### 2. الأسعار المحدثة:
- **تصوير شخصي**: 25,000 د.ع
- **تصوير عائلي**: 75,000 د.ع  
- **تصوير زفاف**: 250,000 د.ع
- **تصوير تخرج**: 50,000 د.ع
- **تصوير منتجات**: 100,000 د.ع
- **تصوير أطفال**: 40,000 د.ع
- **تصوير عرسان**: 200,000 د.ع

#### 3. الباقات المتاحة:
- **الباقة الأساسية**: 50,000 د.ع
- **باقة العائلة**: 120,000 د.ع
- **باقة الزفاف الذهبية**: 500,000 د.ع
- **باقة التخرج**: 80,000 د.ع

#### 4. الملفات المحدثة:
- `lib/utils/currency_formatter.dart` - مُنسق العملة الجديد
- `lib/utils/service_pricing.dart` - أسعار الخدمات والباقات
- `lib/screens/booking_receipt_screen.dart` - شاشة إيصال الحجز
- `lib/screens/delivery_receipt_screen.dart` - شاشة إيصال التسليم  
- `lib/screens/bookings_screen.dart` - شاشة إدارة الحجوزات

#### 5. المميزات الجديدة:
- تنسيق تلقائي للأرقام بالفواصل (25,000)
- دعم العمليات الحسابية (خصومات، ضرائب، أقساط)
- التحقق من صحة المبالغ المدخلة
- عرض المبالغ بتنسيق موحد في جميع الشاشات

#### 6. أمثلة على الاستخدام:

```dart
// تنسيق مبلغ
String formattedAmount = CurrencyFormatter.format(25000.0);
// النتيجة: "25,000 د.ع"

// الحصول على سعر خدمة
int price = ServicePricing.getServicePrice('تصوير زفاف');
// النتيجة: 250000

// تنسيق سعر خدمة
String formattedPrice = ServicePricing.getFormattedServicePrice('تصوير زفاف');
// النتيجة: "250,000 د.ع"
```

#### 7. ملاحظات مهمة:
- جميع المبالغ في النظام بالدينار العراقي
- الأسعار قابلة للتعديل من ملف `service_pricing.dart`
- النظام يدعم العمليات الحسابية المتقدمة
- تم تحديث جميع البيانات التجريبية لتعكس القيم الواقعية

### للمطورين:
- استخدم `CurrencyFormatter.format()` لعرض المبالغ
- استخدم `ServicePricing.getServicePrice()` للحصول على أسعار الخدمات
- جميع المبالغ مخزنة كأرقام صحيحة (int) أو عشرية (double)
- العملة العراقية لا تحتوي على وحدات فرعية في الاستخدام العملي

---
*تم التحديث: يوليو 2025*
