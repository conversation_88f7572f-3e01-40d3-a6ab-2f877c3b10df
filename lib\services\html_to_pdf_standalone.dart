import 'dart:io';
import 'package:flutter/services.dart';
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';

Future<void> convertHtmlToPdfAndSave() async {
  // Load HTML template from assets
  final htmlContent = await rootBundle.loadString('assets/templates/modern/index.html');
  // Convert HTML to PDF bytes
  final pdfBytes = await Printing.convertHtml(
    format: PdfPageFormat.a4,
    html: htmlContent,
  );
  // Save PDF to specified path
  final timestamp = DateTime.now().millisecondsSinceEpoch;
  final outputPath = 'C:/Users/<USER>/Documents/pdfs/delivery_receipt_$timestamp.pdf';
  final file = File(outputPath);
  await file.writeAsBytes(pdfBytes);
  print('PDF saved to: $outputPath');
}

// To use this function, call convertHtmlToPdfAndSave() from your main() or any widget action.
