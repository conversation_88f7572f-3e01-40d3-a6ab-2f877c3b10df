# 📄 خدمة وصل التسليم - DeliveryReceiptService

## 🎯 الغرض
خدمة مخصصة لإنشاء وطباعة وصولات التسليم مع قالب HTML مصمم خصيصاً لعملية التسليم.

## ✨ الميزات الجديدة

### 🎨 تصميم مخصص لوصل التسليم:
- لون أخضر للدلالة على التسليم الناجح ✅
- قسم مخصص لمعلومات التسليم (التاريخ، الطريقة، الوقت)
- تفاصيل الدفع النهائية مع الرسوم الإضافية
- مساحات للتوقيع (العميل والمُسلِّم)
- نظام ألوان متدرج أخضر للدلالة على إتمام العملية

### 💰 حساب متقدم للمبالغ:
- المبلغ الأصلي للحجز
- الرسوم الإضافية (توصيل، أخرى)
- العربون المدفوع مسبقاً
- المبلغ المدفوع عند التسليم
- المبلغ المتبقي (إن وجد)
- إجمالي المبلغ النهائي

### 📋 معلومات تفصيلية:
- معلومات الطلب الأصلي
- طريقة التسليم (استلام من الاستوديو، توصيل، أخرى)
- ملاحظات التسليم
- ملاحظات الحجز الأصلي
- تاريخ ووقت التسليم الفعلي
- اسم الموظف المُسلِّم

## 🛠️ طريقة الاستخدام

### إنشاء وصل تسليم:
```dart
final htmlPath = await DeliveryReceiptService.generateDeliveryReceipt(
  booking: booking,
  templateName: 'modern',
  deliveryMethod: 'توصيل منزلي',
  deliveryNotes: 'تم التسليم للعميل شخصياً',
  additionalCost: 5000.0, // رسوم التوصيل
  paidOnDelivery: 10000.0, // المبلغ المدفوع عند التسليم
  customData: companySettings, // إعدادات الشركة
);
```

### طباعة مباشرة:
```dart
await DeliveryReceiptService.printDeliveryReceiptDirect(
  booking: booking,
  templateName: 'modern',
  deliveryMethod: 'استلام من الاستوديو',
  deliveryNotes: '',
  additionalCost: 0.0,
  paidOnDelivery: 0.0,
  customData: companySettings,
);
```

## 🗂️ هيكل الملفات

### الخدمة الرئيسية:
- `lib/services/delivery_receipt_service.dart` - الخدمة المخصصة لوصولات التسليم

### الملفات المدعومة:
- `lib/screens/delivery_receipt_print_screen.dart` - شاشة طباعة وصل التسليم

### مجلدات التخزين:
- `delivery_templates/` - قوالب وصولات التسليم المخصصة
- `delivery_receipts_html/` - وصولات التسليم المحفوظة

## 🎨 القالب المدمج

### التصميم:
- **الألوان**: نظام أخضر متدرج (#28a745, #20923a)
- **الرموز**: ✅ للتسليم الناجح، 💰 لتفاصيل الدفع
- **التخطيط**: مُحسَّن للطباعة على ورق A4

### الأقسام:
1. **رأس الوصل**: شعار واسم الشركة مع معلومات الاتصال
2. **معلومات التسليم**: تاريخ ووقت التسليم مع طريقة التسليم
3. **معلومات العميل والطلب**: بيانات العميل وتفاصيل الحجز الأصلي
4. **جدول الخدمات**: الخدمات المُسلَّمة مع الأسعار والرسوم الإضافية
5. **تفاصيل الدفع**: حساب شامل للمبالغ المدفوعة والمتبقية
6. **الملاحظات**: ملاحظات التسليم والحجز
7. **التوقيعات**: مساحات لتوقيع العميل والمُسلِّم
8. **التذييل**: معلومات الشركة وتأكيد التسليم

## 📊 الفروق عن خدمة الحجز العادية

| الميزة | وصل الحجز | وصل التسليم |
|--------|-----------|-------------|
| **اللون الأساسي** | أزرق (#007bff) | أخضر (#28a745) |
| **التركيز** | معلومات الحجز | تأكيد التسليم |
| **الحسابات** | العربون والمتبقي | الدفع النهائي والرسوم |
| **التوقيعات** | لا يوجد | العميل والمُسلِّم |
| **طريقة التسليم** | لا يوجد | مطلوب |
| **ملاحظات التسليم** | لا يوجد | اختياري |

## 🔧 التخصيص والصيانة

### تنظيف الملفات القديمة:
```dart
await DeliveryReceiptService.cleanupOldDeliveryReceipts(daysToKeep: 30);
```

### فتح الوصل في المتصفح:
```dart
await DeliveryReceiptService.openHtmlFile(htmlPath);
```

### صيغ الصور المدعومة:
- PNG, JPG, JPEG, GIF, SVG

## 📝 ملاحظات التطوير

### تمرير البيانات:
- إذا لم يتم تمرير `customData`، سيتم تحميل إعدادات الشركة تلقائياً من `CompanySettingsService`
- جميع المعاملات الاختيارية لها قيم افتراضية مناسبة

### معالجة الأخطاء:
- معالجة شاملة للأخطاء مع رسائل واضحة
- نظام fallback للقوالب والصور

### الأداء:
- تحويل الصور إلى base64 لسهولة المشاركة
- تنظيف تلقائي للملفات القديمة لتوفير المساحة

## 🚀 المزايا

1. **انفصال الخدمات**: خدمة منفصلة تماماً عن وصولات الحجز
2. **تصميم متخصص**: قالب مُحسَّن لعملية التسليم
3. **حسابات دقيقة**: نظام حساب شامل للمبالغ والرسوم
4. **سهولة الاستخدام**: واجهة برمجية بسيطة ومرنة
5. **التوثيق**: توثيق شامل وأمثلة واضحة

---

> 📅 **تاريخ الإنشاء**: يوليو 2025  
> 🏗️ **المطور**: نظام إدارة استوديو التصوير  
> 📄 **الإصدار**: 1.0.0
