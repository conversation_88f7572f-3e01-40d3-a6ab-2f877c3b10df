import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:intl/intl.dart';
import '../utils/app_colors.dart';
import '../utils/currency_formatter.dart';
import '../models/booking.dart';
import '../services/database_helper.dart';
import 'delivery_receipt_print_screen.dart';

class DeliveryReceiptScreen extends StatefulWidget {
  const DeliveryReceiptScreen({super.key});

  @override
  State<DeliveryReceiptScreen> createState() => _DeliveryReceiptScreenState();
}

class _DeliveryReceiptScreenState extends State<DeliveryReceiptScreen>
    with SingleTickerProviderStateMixin {
  final _searchController = TextEditingController();
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  late AnimationController _animationController;
  List<Booking> _bookings = [];
  List<Booking> _filteredBookings = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _loadBookings();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadBookings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load from database - only in-progress bookings
      _bookings = await _databaseHelper.getBookingsByStatus(BookingStatus.inProgress);
    } catch (e) {
      // If database fails, use demo data
      _bookings = [
      ];
    }

    _filteredBookings = _bookings
        .where((booking) => booking.status == BookingStatus.inProgress)
        .toList();

    setState(() {
      _isLoading = false;
    });
  }

  void _filterBookings(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredBookings = _bookings
            .where((booking) => booking.status == BookingStatus.inProgress)
            .toList();
      } else {
        _filteredBookings = _bookings
            .where((booking) =>
                booking.status == BookingStatus.inProgress &&
                (booking.customerName.toLowerCase().contains(query.toLowerCase()) ||
                 booking.customerPhone.contains(query) ||
                 booking.id.contains(query)))
            .toList();
      }
    });
  }

  Future<void> _markAsDelivered(Booking booking) async {
    _showDeliveryMethodDialog(booking);
  }

  void _showDeliveryMethodDialog(Booking booking) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 700,
          constraints: const BoxConstraints(maxHeight: 600),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(20),
                decoration: const BoxDecoration(
                  color: AppColors.primaryBlue,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(4),
                    topRight: Radius.circular(4),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.assignment_turned_in, color: Colors.white),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'طريقة التسليم',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close, color: Colors.white),
                    ),
                  ],
                ),
              ),
              
              // Content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Left side - Delivery method buttons
                      Expanded(
                        flex: 1,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'اختر طريقة التسليم:',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: AppColors.primaryText,
                              ),
                            ),
                            const SizedBox(height: 20),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                onPressed: () {
                                  Navigator.pop(context);
                                  _showFlashDeliveryDialog(booking);
                                },
                                icon: const Icon(Icons.usb, size: 24),
                                label: const Text(
                                  'تسليم بالفلاش',
                                  style: TextStyle(fontSize: 16),
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.primaryBlue,
                                  foregroundColor: AppColors.white,
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                ),
                              ),
                            ),
                            const SizedBox(height: 12),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                onPressed: () {
                                  Navigator.pop(context);
                                  _showDiscDeliveryDialog(booking);
                                },
                                icon: const Icon(Icons.album, size: 24),
                                label: const Text(
                                  'تسليم بالأقراص',
                                  style: TextStyle(fontSize: 16),
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.success,
                                  foregroundColor: AppColors.white,
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                ),
                              ),
                            ),
                            const SizedBox(height: 20),
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: AppColors.lightGray,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: AppColors.mediumGray),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      const Icon(Icons.info_outline, color: AppColors.info, size: 20),
                                      const SizedBox(width: 8),
                                      const Text(
                                        'ملاحظات التسليم',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.info,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  const Text(
                                    '• الفلاش: يمكن استخدام فلاش العميل أو فلاش الاستوديو',
                                    style: TextStyle(fontSize: 12, color: AppColors.secondaryText),
                                  ),
                                  const Text(
                                    '• الأقراص: مجانية مع إمكانية تحديد العدد',
                                    style: TextStyle(fontSize: 12, color: AppColors.secondaryText),
                                  ),
                                  const Text(
                                    '• يمكن دفع المبلغ المتبقي عند التسليم',
                                    style: TextStyle(fontSize: 12, color: AppColors.secondaryText),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      const SizedBox(width: 24),
                      
                      // Right side - Financial details
                      Expanded(
                        flex: 1,
                        child: Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [AppColors.veryLightBlue, AppColors.offWhite],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            ),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: AppColors.primaryBlue.withOpacity(0.3)),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  const Icon(Icons.receipt_long, color: AppColors.primaryBlue),
                                  const SizedBox(width: 8),
                                  const Text(
                                    'تفاصيل الطلب المالية',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.primaryBlue,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              
                              // Customer info
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: AppColors.lightGray),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        const Icon(Icons.person, size: 16, color: AppColors.secondaryText),
                                        const SizedBox(width: 8),
                                        Text(
                                          'العميل: ${booking.customerName}',
                                          style: const TextStyle(fontWeight: FontWeight.bold),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    Row(
                                      children: [
                                        const Icon(Icons.phone, size: 16, color: AppColors.secondaryText),
                                        const SizedBox(width: 8),
                                        Text(booking.customerPhone),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    Row(
                                      children: [
                                        const Icon(Icons.camera_alt, size: 16, color: AppColors.secondaryText),
                                        const SizedBox(width: 8),
                                        Expanded(child: Text(booking.serviceType)),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              
                              const SizedBox(height: 16),
                              
                              // Financial breakdown
                              _buildFinancialRow('المبلغ الأساسي', booking.totalAmount, isMain: true),
                              
                              if (booking.discount > 0) ...[
                                const SizedBox(height: 8),
                                _buildFinancialRow(
                                  'الخصم المطبق', 
                                  booking.discount, 
                                  isDiscount: true,
                                  icon: Icons.discount,
                                ),
                                const SizedBox(height: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(vertical: 8),
                                  decoration: const BoxDecoration(
                                    border: Border(top: BorderSide(color: AppColors.lightGray)),
                                  ),
                                  child: _buildFinancialRow(
                                    'المبلغ بعد الخصم', 
                                    booking.totalAmount - booking.discount, 
                                    isHighlight: true,
                                  ),
                                ),
                              ],
                              
                              const SizedBox(height: 8),
                              _buildFinancialRow(
                                'العربون المدفوع', 
                                booking.paidAmount, 
                                isPaid: true,
                                icon: Icons.paid,
                              ),
                              
                              const SizedBox(height: 12),
                              Container(
                                width: double.infinity,
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: booking.calculatedRemainingAmount > 0 
                                    ? AppColors.warning.withOpacity(0.1)
                                    : AppColors.success.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: booking.calculatedRemainingAmount > 0 
                                      ? AppColors.warning
                                      : AppColors.success,
                                  ),
                                ),
                                child: Column(
                                  children: [
                                    Text(
                                      'المبلغ المتبقي',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: booking.calculatedRemainingAmount > 0 
                                          ? AppColors.warning
                                          : AppColors.success,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      CurrencyFormatter.format(booking.calculatedRemainingAmount),
                                      style: TextStyle(
                                        fontSize: 13,
                                        fontWeight: FontWeight.bold,
                                        color: booking.calculatedRemainingAmount > 0 
                                          ? AppColors.warning
                                          : AppColors.success,
                                      ),
                                    ),
                                    if (booking.calculatedRemainingAmount <= 0)
                                      const Text(
                                        '✓ تم السداد كاملاً',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: AppColors.success,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              // Footer
              Container(
                padding: const EdgeInsets.all(20),
                decoration: const BoxDecoration(
                  color: AppColors.lightGray,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(4),
                    bottomRight: Radius.circular(4),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('إلغاء'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showFlashDeliveryDialog(Booking booking) {
    bool isFlashFromCustomer = true;
    double flashPrice = 15000; // سعر الفلاش الافتراضي
    double paidOnDelivery = 0.0; // المبلغ المدفوع عند التسليم
    final TextEditingController paidController = TextEditingController();
    
    // خيارات المحتوى
    bool photosOnly = false;
    bool videosOnly = false;
    bool photosAndVideos = true; // الافتراضي

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setStateDialog) => Dialog(
          child: Container(
            width: 600,
            constraints: const BoxConstraints(maxHeight: 700),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: const BoxDecoration(
                    color: AppColors.primaryBlue,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(4),
                      topRight: Radius.circular(4),
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.usb, color: Colors.white),
                      const SizedBox(width: 12),
                      const Expanded(
                        child: Text(
                          'تسليم بالفلاش',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close, color: Colors.white),
                      ),
                    ],
                  ),
                ),
                
                // Content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'تفاصيل التسليم بالفلاش:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primaryText,
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        // خيارات المحتوى
                        const Text(
                          'نوع المحتوى:',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.lightGray),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            children: [
                              CheckboxListTile(
                                title: const Text('صور فقط'),
                                subtitle: const Text('نسخ الصور فقط على الفلاش'),
                                value: photosOnly,
                                onChanged: (value) {
                                  setStateDialog(() {
                                    photosOnly = value!;
                                    if (photosOnly) {
                                      videosOnly = false;
                                      photosAndVideos = false;
                                    }
                                  });
                                },
                                secondary: const Icon(Icons.photo, color: AppColors.info),
                              ),
                              const Divider(height: 1),
                              CheckboxListTile(
                                title: const Text('فيديوات فقط'),
                                subtitle: const Text('نسخ الفيديوات فقط على الفلاش'),
                                value: videosOnly,
                                onChanged: (value) {
                                  setStateDialog(() {
                                    videosOnly = value!;
                                    if (videosOnly) {
                                      photosOnly = false;
                                      photosAndVideos = false;
                                    }
                                  });
                                },
                                secondary: const Icon(Icons.videocam, color: AppColors.warning),
                              ),
                              const Divider(height: 1),
                              CheckboxListTile(
                                title: const Text('صور وفيديوات'),
                                subtitle: const Text('نسخ جميع المحتويات على الفلاش'),
                                value: photosAndVideos,
                                onChanged: (value) {
                                  setStateDialog(() {
                                    photosAndVideos = value!;
                                    if (photosAndVideos) {
                                      photosOnly = false;
                                      videosOnly = false;
                                    }
                                  });
                                },
                                secondary: const Icon(Icons.perm_media, color: AppColors.success),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 20),
                        
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [AppColors.veryLightBlue, AppColors.offWhite],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            ),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: AppColors.primaryBlue.withOpacity(0.3)),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  const Icon(Icons.person, size: 16, color: AppColors.secondaryText),
                                  const SizedBox(width: 8),
                                  Text(
                                    'العميل: ${booking.customerName}',
                                    style: const TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text('المبلغ الأساسي: ${CurrencyFormatter.format(booking.totalAmount)}'),
                              if (booking.discount > 0) ...[
                                const SizedBox(height: 4),
                                Text(
                                  'الخصم: ${CurrencyFormatter.format(booking.discount)}',
                                  style: const TextStyle(color: AppColors.success, fontWeight: FontWeight.bold),
                                ),
                              ],
                              const SizedBox(height: 4),
                              Text(
                                'العربون المدفوع: ${CurrencyFormatter.format(booking.paidAmount)}',
                                style: const TextStyle(color: AppColors.info, fontWeight: FontWeight.bold),
                              ),
                              if (!isFlashFromCustomer) ...[
                                const SizedBox(height: 4),
                                Text(
                                  'سعر الفلاش: ${CurrencyFormatter.format(flashPrice)}',
                                  style: const TextStyle(color: AppColors.warning, fontWeight: FontWeight.bold),
                                ),
                              ],
                              const Divider(height: 20),
                              Text(
                                'المبلغ المتبقي: ${CurrencyFormatter.format(((booking.totalAmount - booking.discount) + (isFlashFromCustomer ? 0 : flashPrice)) - booking.paidAmount)}',
                                style: const TextStyle(color: AppColors.warning, fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 20),
                        const Text(
                          'مصدر الفلاش:',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.lightGray),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            children: [
                              RadioListTile<bool>(
                                title: const Text('فلاش العميل'),
                                subtitle: const Text('استخدام فلاش العميل الشخصي'),
                                value: true,
                                groupValue: isFlashFromCustomer,
                                onChanged: (value) {
                                  setStateDialog(() {
                                    isFlashFromCustomer = value!;
                                  });
                                },
                              ),
                              const Divider(height: 1),
                              RadioListTile<bool>(
                                title: const Text('فلاش الاستوديو'),
                                subtitle: const Text('استخدام فلاش من الاستوديو (برسوم إضافية)'),
                                value: false,
                                groupValue: isFlashFromCustomer,
                                onChanged: (value) {
                                  setStateDialog(() {
                                    isFlashFromCustomer = value!;
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                        if (!isFlashFromCustomer) ...[
                          const SizedBox(height: 16),
                          TextField(
                            decoration: const InputDecoration(
                              labelText: 'سعر الفلاش',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.attach_money),
                            ),
                            keyboardType: TextInputType.number,
                            onChanged: (value) {
                              final price = double.tryParse(value);
                              if (price != null) {
                                setStateDialog(() {
                                  flashPrice = price;
                                });
                              }
                            },
                          ),
                        ],
                        const SizedBox(height: 20),
                        const Text(
                          'المبلغ المدفوع عند التسليم:',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        TextField(
                          controller: paidController,
                          decoration: InputDecoration(
                            labelText: 'المبلغ المدفوع',
                            border: const OutlineInputBorder(),
                            prefixIcon: const Icon(Icons.payment),
                          ),
                          keyboardType: TextInputType.number,
                          onChanged: (value) {
                            final amount = double.tryParse(value);
                            if (amount != null) {
                              setStateDialog(() {
                                paidOnDelivery = amount;
                              });
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                
                // Footer
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: const BoxDecoration(
                    color: AppColors.lightGray,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(4),
                      bottomRight: Radius.circular(4),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: const Text('إلغاء'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () async {
                            // التحقق من اختيار نوع المحتوى
                            if (!photosOnly && !videosOnly && !photosAndVideos) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('يرجى اختيار نوع المحتوى'),
                                  backgroundColor: AppColors.error,
                                ),
                              );
                              return;
                            }
                            // تحقق إلزامي من المبلغ المدفوع عند التسليم
                            final paidText = paidController.text.trim();
                            final paidValue = double.tryParse(paidText);
                            if (paidText.isEmpty || paidValue == null || paidValue <= 0) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('يرجى إدخال المبلغ المدفوع عند التسليم (إجباري)'),
                                  backgroundColor: AppColors.error,
                                ),
                              );
                              return;
                            }
                            paidOnDelivery = paidValue;
                            String contentType;
                            if (photosOnly) {
                              contentType = 'صور فقط';
                            } else if (videosOnly) {
                              contentType = 'فيديوات فقط';
                            } else {
                              contentType = 'صور وفيديوات';
                            }
                            Navigator.pop(context);
                            await _processDelivery(
                              booking,
                              deliveryMethod: 'فلاش',
                              additionalCost: isFlashFromCustomer ? 0 : flashPrice,
                              deliveryNotes: '${isFlashFromCustomer ? 'فلاش العميل' : 'فلاش الاستوديو'}\nنوع المحتوى: $contentType',
                              paidOnDelivery: paidOnDelivery,
                            );
                          },
                          icon: const Icon(Icons.check),
                          label: const Text('تأكيد التسليم'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primaryBlue,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showDiscDeliveryDialog(Booking booking) {
    int discCount = 1;
    double paidOnDelivery = 0.0; // المبلغ المدفوع عند التسليم
    final TextEditingController discCountController = TextEditingController(text: '1');
    final TextEditingController paidController = TextEditingController();
    
    // خيارات المحتوى
    bool photosOnly = false;
    bool videosOnly = false;
    bool photosAndVideos = true; // الافتراضي

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setStateDialog) => Dialog(
          child: Container(
            width: 600,
            constraints: const BoxConstraints(maxHeight: 650),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: const BoxDecoration(
                    color: AppColors.success,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(4),
                      topRight: Radius.circular(4),
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.album, color: Colors.white),
                      const SizedBox(width: 12),
                      const Expanded(
                        child: Text(
                          'تسليم بالأقراص',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close, color: Colors.white),
                      ),
                    ],
                  ),
                ),
                
                // Content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'تفاصيل التسليم بالأقراص:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primaryText,
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        // خيارات المحتوى
                        const Text(
                          'نوع المحتوى:',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.lightGray),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            children: [
                              CheckboxListTile(
                                title: const Text('صور فقط'),
                                subtitle: const Text('نسخ الصور فقط على الأقراص'),
                                value: photosOnly,
                                onChanged: (value) {
                                  setStateDialog(() {
                                    photosOnly = value!;
                                    if (photosOnly) {
                                      videosOnly = false;
                                      photosAndVideos = false;
                                    }
                                  });
                                },
                                secondary: const Icon(Icons.photo, color: AppColors.info),
                              ),
                              const Divider(height: 1),
                              CheckboxListTile(
                                title: const Text('فيديوات فقط'),
                                subtitle: const Text('نسخ الفيديوات فقط على الأقراص'),
                                value: videosOnly,
                                onChanged: (value) {
                                  setStateDialog(() {
                                    videosOnly = value!;
                                    if (videosOnly) {
                                      photosOnly = false;
                                      photosAndVideos = false;
                                    }
                                  });
                                },
                                secondary: const Icon(Icons.videocam, color: AppColors.warning),
                              ),
                              const Divider(height: 1),
                              CheckboxListTile(
                                title: const Text('صور وفيديوات'),
                                subtitle: const Text('نسخ جميع المحتويات على الأقراص'),
                                value: photosAndVideos,
                                onChanged: (value) {
                                  setStateDialog(() {
                                    photosAndVideos = value!;
                                    if (photosAndVideos) {
                                      photosOnly = false;
                                      videosOnly = false;
                                    }
                                  });
                                },
                                secondary: const Icon(Icons.perm_media, color: AppColors.success),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 20),
                        
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [AppColors.veryLightBlue, AppColors.offWhite],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            ),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: AppColors.success.withOpacity(0.3)),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  const Icon(Icons.person, size: 16, color: AppColors.secondaryText),
                                  const SizedBox(width: 8),
                                  Text(
                                    'العميل: ${booking.customerName}',
                                    style: const TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text('المبلغ الإجمالي: ${CurrencyFormatter.format(booking.totalAmount)}'),
                              if (booking.discount > 0) ...[
                                const SizedBox(height: 4),
                                Text(
                                  'الخصم المطبق: ${CurrencyFormatter.format(booking.discount)}',
                                  style: const TextStyle(color: AppColors.success, fontWeight: FontWeight.bold),
                                ),

                              ],
                              const SizedBox(height: 4),
                              Text(
                                'العربون المدفوع: ${CurrencyFormatter.format(booking.paidAmount)}',
                                style: const TextStyle(color: AppColors.info, fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'المبلغ المتبقي: ${CurrencyFormatter.format(booking.calculatedRemainingAmount)}',
                                style: const TextStyle(color: AppColors.warning, fontWeight: FontWeight.bold),
                              ),
                              const Divider(height: 20),
                              Row(
                                children: [
                                  const Icon(Icons.check_circle, color: AppColors.success, size: 16),
                                  const SizedBox(width: 8),
                                  const Text(
                                    'الأقراص مجانية',
                                    style: TextStyle(color: AppColors.success, fontWeight: FontWeight.bold),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 20),
                        const Text(
                          'عدد الأقراص:',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        TextField(
                          controller: discCountController,
                          decoration: const InputDecoration(
                            labelText: 'عدد الأقراص',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.album),
                            helperText: 'أدخل عدد الأقراص المعطاة للعميل',
                          ),
                          keyboardType: TextInputType.number,
                          onChanged: (value) {
                            final count = int.tryParse(value);
                            if (count != null && count > 0) {
                              discCount = count;
                            }
                          },
                        ),
                        const SizedBox(height: 20),
                        const Text(
                          'المبلغ المدفوع عند التسليم:',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        TextField(
                          controller: paidController,
                          decoration: InputDecoration(
                            labelText: 'المبلغ المدفوع',
                            border: const OutlineInputBorder(),
                            prefixIcon: const Icon(Icons.payment),
                          ),
                          keyboardType: TextInputType.number,
                          onChanged: (value) {
                            final amount = double.tryParse(value);
                            if (amount != null) {
                              setStateDialog(() {
                                paidOnDelivery = amount;
                              });
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                
                // Footer
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: const BoxDecoration(
                    color: AppColors.lightGray,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(4),
                      bottomRight: Radius.circular(4),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: const Text('إلغاء'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () async {
                            // التحقق من اختيار نوع المحتوى
                            if (!photosOnly && !videosOnly && !photosAndVideos) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('يرجى اختيار نوع المحتوى'),
                                  backgroundColor: AppColors.error,
                                ),
                              );
                              return;
                            }
                            // تحقق إلزامي من المبلغ المدفوع عند التسليم
                            final paidText = paidController.text.trim();
                            final paidValue = double.tryParse(paidText);
                            if (paidText.isEmpty || paidValue == null || paidValue <= 0) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('يرجى إدخال المبلغ المدفوع عند التسليم (إجباري)'),
                                  backgroundColor: AppColors.error,
                                ),
                              );
                              return;
                            }
                            paidOnDelivery = paidValue;
                            String contentType;
                            if (photosOnly) {
                              contentType = 'صور فقط';
                            } else if (videosOnly) {
                              contentType = 'فيديوات فقط';
                            } else {
                              contentType = 'صور وفيديوات';
                            }
                            Navigator.pop(context);
                            await _processDelivery(
                              booking,
                              deliveryMethod: 'أقراص',
                              additionalCost: 0,
                              deliveryNotes: 'عدد الأقراص: $discCount\nنوع المحتوى: $contentType',
                              paidOnDelivery: paidOnDelivery,
                            );
                          },
                          icon: const Icon(Icons.check),
                          label: const Text('تأكيد التسليم'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.success,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _processDelivery(
    Booking booking, {
    required String deliveryMethod,
    required double additionalCost,
    required String deliveryNotes,
    double paidOnDelivery = 0.0,
  }) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Calculate new paid amount: current paid amount + paid on delivery
      final newPaidAmount = booking.paidAmount + paidOnDelivery;
      
      // Update booking status in database with the new amounts
      final updatedBooking = booking.copyWith(
        status: BookingStatus.completed,
        totalAmount: booking.totalAmount + additionalCost, // إضافة التكلفة الإضافية للمبلغ الكلي
        paidAmount: newPaidAmount,
        updatedAt: DateTime.now(),
        notes: '${booking.notes ?? ''}\nطريقة التسليم: $deliveryMethod\n$deliveryNotes\nمدفوع عند التسليم: ${CurrencyFormatter.format(paidOnDelivery)}',
      );
      
      await _databaseHelper.updateBooking(updatedBooking);

      setState(() {
        final index = _bookings.indexWhere((b) => b.id == booking.id);
        if (index != -1) {
          _bookings[index] = updatedBooking;
        }
        _filteredBookings.removeWhere((b) => b.id == booking.id);
      });

      _showSuccessDialog(updatedBooking, deliveryMethod, deliveryNotes, additionalCost, paidOnDelivery);
    } catch (e) {
      _showErrorDialog('حدث خطأ أثناء تسليم الطلب: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSuccessDialog(
    Booking booking,
    String deliveryMethod,
    String deliveryNotes,
    double additionalCost,
    double paidOnDelivery,
  ) {
    // حساب المبالغ الصحيحة بناءً على البيانات المحدثة
    final originalAmount = booking.totalAmount - additionalCost; // المبلغ الأصلي قبل التكاليف الإضافية
    final discountedAmount = originalAmount - booking.discount; // المبلغ بعد الخصم
    final finalTotalAmount = booking.totalAmount; // المبلغ النهائي (يتضمن التكاليف الإضافية)
    final previouslyPaid = booking.paidAmount - paidOnDelivery; // المدفوع سابقاً (العربون)
    final totalPaid = booking.paidAmount; // إجمالي المدفوع
    final remainingAmount = finalTotalAmount - totalPaid; // المتبقي
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: const Icon(
          Icons.check_circle,
          color: AppColors.success,
          size: 64,
        ),
        title: const Text('تم التسليم بنجاح'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('تم تسليم طلب ${booking.customerName} بنجاح'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.veryLightBlue,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('طريقة التسليم: $deliveryMethod'),
                  Text(deliveryNotes),
                  const SizedBox(height: 12),
                  
                  // تفاصيل مالية محسوبة بشكل صحيح
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: AppColors.lightGray),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'التفاصيل المالية:',
                          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                        ),
                        const SizedBox(height: 8),
                        
                        _buildReceiptRow('المبلغ الأصلي', originalAmount),
                        
                        if (booking.discount > 0) ...[
                          _buildReceiptRow(
                            'الخصم المطبق',
                            booking.discount,
                            valueColor: AppColors.success,
                            isNegative: true,
                          ),
                          _buildReceiptRow(
                            'المبلغ بعد الخصم',
                            discountedAmount,
                            isBold: true,
                          ),
                        ],
                        
                        if (additionalCost > 0) ...[
                          _buildReceiptRow(
                            'تكلفة إضافية ($deliveryMethod)',
                            additionalCost,
                            valueColor: AppColors.warning,
                          ),
                        ],
                        
                        const Divider(height: 16),
                        _buildReceiptRow(
                          'المبلغ الإجمالي النهائي',
                          finalTotalAmount,
                          isBold: true,
                          valueColor: AppColors.primaryBlue,
                        ),
                        
                        const SizedBox(height: 8),
                        _buildReceiptRow(
                          'العربون المدفوع سابقاً',
                          previouslyPaid,
                          valueColor: AppColors.info,
                        ),
                        
                        if (paidOnDelivery > 0) ...[
                          _buildReceiptRow(
                            'مدفوع عند التسليم',
                            paidOnDelivery,
                            valueColor: AppColors.primaryBlue,
                          ),
                        ],
                        
                        const Divider(height: 16),
                        _buildReceiptRow(
                          'إجمالي المدفوع',
                          totalPaid,
                          isBold: true,
                          valueColor: AppColors.primaryBlue,
                        ),
                        
                        if (remainingAmount > 0)
                          _buildReceiptRow(
                            'المتبقي',
                            remainingAmount,
                            isBold: true,
                            valueColor: AppColors.warning,
                          )
                        else
                          const Padding(
                            padding: EdgeInsets.only(top: 8.0),
                            child: Text(
                              '✓ تم سداد المبلغ كاملاً',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: AppColors.success,
                                fontSize: 14,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              _navigateToDeliveryReceiptPrint(
                booking,
                deliveryMethod,
                deliveryNotes,
                additionalCost,
                paidOnDelivery,
              );
            },
            icon: const Icon(Icons.print),
            label: const Text('طباعة الوصل'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  // دالة مساعدة لعرض الصفوف المالية
  Widget _buildReceiptRow(
    String label,
    double amount, {
    bool isBold = false,
    Color? valueColor,
    bool isNegative = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '$label:',
            style: TextStyle(
              fontSize: 12,
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              color: AppColors.primaryText,
            ),
          ),
          Text(
            '${isNegative ? '- ' : ''}${CurrencyFormatter.format(amount)}',
            style: TextStyle(
              fontSize: 12,
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              color: valueColor ?? AppColors.primaryText,
            ),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: const Icon(
          Icons.error,
          color: AppColors.error,
          size: 64,
        ),
        title: const Text('خطأ'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _navigateToDeliveryReceiptPrint(
    Booking booking,
    String deliveryMethod,
    String deliveryNotes,
    double additionalCost,
    double paidOnDelivery,
  ) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DeliveryReceiptPrintScreen(
          booking: booking,
          deliveryMethod: deliveryMethod,
          deliveryNotes: deliveryNotes,
          additionalCost: additionalCost,
          paidOnDelivery: paidOnDelivery,
        ),
      ),
    );
  }


  Widget _buildFinancialRow(String label, double amount, {
    bool isMain = false,
    bool isDiscount = false,
    bool isHighlight = false,
    bool isPaid = false,
    IconData? icon,
  }) {
    Color textColor = AppColors.primaryText;
    if (isDiscount) textColor = AppColors.success;
    if (isHighlight) textColor = AppColors.primaryBlue;
    if (isPaid) textColor = AppColors.info;
    if (isMain) textColor = AppColors.primaryText;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          if (icon != null) ...[
            Icon(icon, size: 16, color: textColor),
            const SizedBox(width: 8),
          ],
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: isMain || isHighlight ? 14 : 13,
                fontWeight: isMain || isHighlight ? FontWeight.bold : FontWeight.normal,
                color: textColor,
              ),
            ),
          ),
          Text(
            '${isDiscount ? '- ' : ''}${CurrencyFormatter.format(amount)}',
            style: TextStyle(
              fontSize: isMain || isHighlight ? 14 : 13,
              fontWeight: isMain || isHighlight ? FontWeight.bold : FontWeight.w500,
              color: textColor,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('وصل تسليم'),
        elevation: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.veryLightBlue,
              AppColors.offWhite,
            ],
          ),
        ),
        child: Column(
          children: [
            // Search section
            Padding(
              padding: const EdgeInsets.all(16),
              child: AnimationConfiguration.staggeredList(
                position: 0,
                duration: const Duration(milliseconds: 375),
                child: SlideAnimation(
                  verticalOffset: -50.0,
                  child: FadeInAnimation(
                    child: Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(
                                  Icons.assignment_turned_in,
                                  color: AppColors.success,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'البحث عن الطلبات الجاهزة للتسليم',
                                  style: Theme.of(context).textTheme.titleMedium,
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            TextField(
                              controller: _searchController,
                              decoration: const InputDecoration(
                                hintText: 'ابحث بالاسم، رقم الهاتف، أو رقم الطلب',
                                prefixIcon: Icon(Icons.search),
                              ),
                              onChanged: _filterBookings,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),

            // Bookings list
            Expanded(
              child: _isLoading
                  ? const Center(
                      child: CircularProgressIndicator(),
                    )
                  : _filteredBookings.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.assignment_late,
                                size: 64,
                                color: Colors.grey[400],
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'لا توجد طلبات جاهزة للتسليم',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        )
                      : AnimationLimiter(
                          child: ListView.builder(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            itemCount: _filteredBookings.length,
                            itemBuilder: (context, index) {
                              final booking = _filteredBookings[index];
                              return AnimationConfiguration.staggeredList(
                                position: index,
                                duration: const Duration(milliseconds: 375),
                                child: SlideAnimation(
                                  verticalOffset: 50.0,
                                  child: FadeInAnimation(
                                    child: BookingCard(
                                      booking: booking,
                                      onDelivery: () => _markAsDelivered(booking),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
            ),
          ],
        ),
      ),
    );
  }
}

class BookingCard extends StatefulWidget {
  final Booking booking;
  final VoidCallback onDelivery;

  const BookingCard({
    super.key,
    required this.booking,
    required this.onDelivery,
  });

  @override
  State<BookingCard> createState() => _BookingCardState();
}

class _BookingCardState extends State<BookingCard> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final isOverdue = widget.booking.deliveryDate.isBefore(DateTime.now());
    
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        children: [
          ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isOverdue ? AppColors.error : AppColors.success,
                shape: BoxShape.circle,
              ),
              child: Icon(
                isOverdue ? Icons.warning : Icons.schedule,
                color: AppColors.white,
                size: 20,
              ),
            ),
            title: Text(
              widget.booking.customerName,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(widget.booking.serviceType),
                Text(
                  'التسليم: ${DateFormat('dd/MM/yyyy').format(widget.booking.deliveryDate)}',
                  style: TextStyle(
                    color: isOverdue ? AppColors.error : AppColors.secondaryText,
                    fontWeight: isOverdue ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (widget.booking.calculatedRemainingAmount > 0)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                    decoration: BoxDecoration(
                      color: AppColors.warning,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      'متبقي ${CurrencyFormatter.format(widget.booking.calculatedRemainingAmount)}',
                      style: const TextStyle(
                        color: AppColors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                const SizedBox(width: 8),
                IconButton(
                  icon: Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                  ),
                  onPressed: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
                ),
              ],
            ),
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
          ),
          if (_isExpanded)
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Divider(),
                  FutureBuilder<String>(
                    future: widget.booking.formattedReceiptNumber,
                    builder: (context, snapshot) {
                      return _buildInfoRow('رقم الطلب', snapshot.data ?? "جاري التحميل...");
                    },
                  ),
                  _buildInfoRow('رقم الهاتف', widget.booking.customerPhone),
                  if (widget.booking.bookedBy != null && widget.booking.bookedBy!.isNotEmpty)
                    _buildInfoRow('حجز بواسطة', widget.booking.bookedBy!),
                  _buildInfoRow('تاريخ الحجز', DateFormat('dd/MM/yyyy').format(widget.booking.bookingDate)),
                  _buildInfoRow('المبلغ الكلي', CurrencyFormatter.format(widget.booking.totalAmount)),
                  if (widget.booking.discount > 0) ...[
                    _buildInfoRow(
                      'الخصم',
                      CurrencyFormatter.format(widget.booking.discount),
                      valueColor: AppColors.success,
                    ),
                  ],
                  _buildInfoRow('العربون', CurrencyFormatter.format(widget.booking.paidAmount)),
                  if (widget.booking.calculatedRemainingAmount > 0)
                    _buildInfoRow(
                      'المبلغ المتبقي',
                      CurrencyFormatter.format(widget.booking.calculatedRemainingAmount),
                      valueColor: AppColors.warning,
                    ),
                  if (widget.booking.notes != null && widget.booking.notes!.isNotEmpty)
                    _buildInfoRow('ملاحظات', widget.booking.notes!),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: widget.onDelivery,
                      icon: const Icon(Icons.check),
                      label: const Text('تأكيد التسليم'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.success,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                color: AppColors.secondaryText,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: valueColor ?? AppColors.primaryText,
                fontWeight: valueColor != null ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
