# إعدادات الجلسة - Session Configuration

## الإعدادات الحالية

### ✅ **الجلسة لا تنتهي أبداً**
- تم تعطيل انتهاء صلاحية الجلسة
- المستخدم يبقى مسجلاً للدخول حتى يقوم بتسجيل الخروج يدوياً
- لا توجد مهلة زمنية لانتهاء الجلسة

## التغييرات المطبقة

### 1. في ملف `auth.js`:
```javascript
isSessionExpired() {
    // Always return false - session never expires
    return false;
}

checkSession() {
    // Always return true if user is logged in - session never expires
    if (this.currentUser) {
        this.extendSession();
        return true;
    }
    return false;
}
```

### 2. في ملف `app.js`:
```javascript
// Session check interval - Disabled (sessions never expire)
// تم تعطيل فحص انتهاء الجلسة
```

## المزايا

✅ **سهولة الاستخدام**: لا حاجة لإعادة تسجيل الدخول  
✅ **استمرارية العمل**: لا انقطاع في العمل  
✅ **توفير الوقت**: عدم الحاجة لإدخال بيانات الدخول مرة أخرى  

## الأمان

⚠️ **ملاحظة أمنية**: 
- تأكد من تسجيل الخروج عند الانتهاء من العمل
- لا تترك التطبيق مفتوحاً على أجهزة مشتركة
- يمكن إعادة تفعيل انتهاء الجلسة إذا لزم الأمر

## كيفية إعادة تفعيل انتهاء الجلسة (إذا لزم الأمر)

إذا كنت تريد إعادة تفعيل انتهاء الجلسة، قم بتغيير الكود في `auth.js`:

```javascript
isSessionExpired() {
    if (!this.currentUser || !this.currentUser.lastActivity) {
        return true;
    }

    const lastActivity = new Date(this.currentUser.lastActivity);
    const now = new Date();
    const diffHours = (now - lastActivity) / (1000 * 60 * 60);

    // Session expires after X hours of inactivity
    return diffHours > 8; // غير الرقم 8 للمدة المطلوبة بالساعات
}

checkSession() {
    if (this.isSessionExpired()) {
        this.logout();
        return false;
    }
    
    this.extendSession();
    return true;
}
```

وإعادة تفعيل الفحص في `app.js`:

```javascript
// Session check interval
setInterval(() => {
    if (auth.isLoggedIn() && !auth.checkSession()) {
        Utils.showNotification('انتهت جلسة العمل. يرجى تسجيل الدخول مرة أخرى.', 'warning');
    }
}, 60000); // Check every minute
```

## الحالة الحالية

🟢 **الجلسة نشطة دائماً**  
🟢 **لا انتهاء للصلاحية**  
🟢 **تسجيل خروج يدوي فقط**  

---

**تاريخ التحديث**: $(date)  
**الإصدار**: 1.0  
**الحالة**: مفعل
