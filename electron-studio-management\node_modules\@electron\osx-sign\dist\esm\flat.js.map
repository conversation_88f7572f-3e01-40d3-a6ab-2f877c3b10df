{"version": 3, "file": "flat.js", "sourceRoot": "", "sources": ["../../src/flat.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,eAAe,EAAE,oBAAoB,EAAE,MAAM,QAAQ,CAAC;AAEnG,OAAO,EAAY,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAI7D,MAAM,UAAU,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC,OAAiB,CAAC;AAEnE;;;;;GAKG;AACH,KAAK,UAAU,gBAAgB,CAAE,IAAiB;IAChD,MAAM,eAAe,CAAC,IAAI,CAAC,CAAC;IAE5B,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;IACnB,IAAI,GAAG,EAAE;QACP,IAAI,OAAO,GAAG,KAAK,QAAQ;YAAE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QACxE,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,MAAM,EAAE;YAChC,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;SAChE;KACF;SAAM;QACL,SAAS,CACP,6FAA6F,CAC9F,CAAC;QACF,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;KACnF;IAED,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;IAC3B,IAAI,OAAO,EAAE;QACX,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAC/B,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAC;SACjE;KACF;SAAM;QACL,SAAS,CAAC,6EAA6E,CAAC,CAAC;QACzF,OAAO,GAAG,eAAe,CAAC;KAC3B;IAED,uCACK,IAAI,KACP,GAAG;QACH,OAAO,EACP,QAAQ,EAAE,MAAM,oBAAoB,CAAC,IAAI,CAAC,IAC1C;AACJ,CAAC;AAED;;;;;GAKG;AACH,KAAK,UAAU,mBAAmB,CAAE,IAA0B,EAAE,QAAkB;IAChF,MAAM,IAAI,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IACxF,IAAI,IAAI,CAAC,QAAQ,EAAE;QACjB,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC3C;IACD,IAAI,IAAI,CAAC,OAAO,EAAE;QAChB,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;KACzC;IAED,QAAQ,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;IACtC,MAAM,aAAa,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AAC5C,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,QAAQ,CAAE,KAAkB;IAChD,QAAQ,CAAC,uBAAuB,EAAE,UAAU,CAAC,CAAC;IAC9C,MAAM,gBAAgB,GAAG,MAAM,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACvD,IAAI,UAAU,GAAe,EAAE,CAAC;IAChC,IAAI,aAAa,GAAoB,IAAI,CAAC;IAE1C,IAAI,gBAAgB,CAAC,QAAQ,EAAE;QAC7B,QAAQ,CAAC,iCAAiC,CAAC,CAAC;QAC5C,IAAI,gBAAgB,CAAC,kBAAkB,KAAK,KAAK,EAAE;YACjD,aAAa;SACd;aAAM;YACL,UAAU,GAAG,MAAM,cAAc,CAAC,gBAAgB,CAAC,QAAQ,IAAI,IAAI,EAAE,gBAAgB,CAAC,QAAQ,CAAC,CAAC;SACjG;KACF;SAAM;QACL,SAAS,CAAC,sCAAsC,CAAC,CAAC;QAClD,IAAI,gBAAgB,CAAC,QAAQ,KAAK,KAAK,EAAE;YACvC,QAAQ,CACN,iHAAiH,CAClH,CAAC;YACF,UAAU,GAAG,MAAM,cAAc,CAC/B,gBAAgB,CAAC,QAAQ,IAAI,IAAI,EACjC,oCAAoC,CACrC,CAAC;SACH;aAAM;YACL,QAAQ,CACN,8FAA8F,CAC/F,CAAC;YACF,UAAU,GAAG,MAAM,cAAc,CAAC,gBAAgB,CAAC,QAAQ,IAAI,IAAI,EAAE,yBAAyB,CAAC,CAAC;SACjG;KACF;IAED,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;QACzB,gCAAgC;QAChC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,SAAS,CAAC,2DAA2D,CAAC,CAAC;SACxE;aAAM;YACL,QAAQ,CAAC,mBAAmB,CAAC,CAAC;SAC/B;QACD,aAAa,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;KAC/B;SAAM;QACL,oBAAoB;QACpB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;KACnD;IAED,QAAQ,CACN,2BAA2B,EAC3B,IAAI,EACJ,gBAAgB,EAChB,gBAAgB,CAAC,GAAG,EACpB,IAAI,EACJ,mBAAmB,EACnB,gBAAgB,CAAC,GAAG,EACpB,IAAI,EACJ,iBAAiB,EACjB,gBAAgB,CAAC,OAAO,EACxB,IAAI,EACJ,aAAa,EACb,gBAAgB,CAAC,QAAQ,EACzB,IAAI,EACJ,YAAY,EACZ,gBAAgB,CAAC,OAAO,CACzB,CAAC;IACF,MAAM,mBAAmB,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;IAE3D,QAAQ,CAAC,wBAAwB,CAAC,CAAC;AACrC,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,IAAiB,EAAE,EAA4B,EAAE,EAAE;IACtE,QAAQ,CAAC,IAAI,CAAC;SACX,IAAI,CAAC,GAAG,EAAE;QACT,QAAQ,CAAC,mCAAmC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QACzD,IAAI,EAAE;YAAE,EAAE,EAAE,CAAC;IACf,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;QACb,QAAQ,CAAC,cAAc,CAAC,CAAC;QACzB,IAAI,GAAG,CAAC,OAAO;YAAE,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;aAClC,IAAI,GAAG,CAAC,KAAK;YAAE,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;;YACnC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACnB,IAAI,EAAE;YAAE,EAAE,CAAC,GAAG,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC,CAAC"}