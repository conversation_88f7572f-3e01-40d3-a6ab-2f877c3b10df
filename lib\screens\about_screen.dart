import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../utils/app_colors.dart';

class AboutScreen extends StatefulWidget {
  const AboutScreen({super.key});

  @override
  State<AboutScreen> createState() => _AboutScreenState();
}

class _AboutScreenState extends State<AboutScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.offWhite,
      appBar: AppBar(
        title: const Text('حول البرنامج'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: AnimationLimiter(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: AnimationConfiguration.toStaggeredList(
              duration: const Duration(milliseconds: 375),
              childAnimationBuilder: (widget) => SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(child: widget),
              ),
              children: [
                // شعار البرنامج
                Container(
                  margin: const EdgeInsets.only(bottom: 20),
                  child: Card(
                    elevation: 8,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            AppColors.primaryBlue,
                            AppColors.primaryBlue.withOpacity(0.8),
                          ],
                        ),
                      ),
                      child: Column(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.camera_alt,
                              size: 48,
                              color: AppColors.primaryBlue,
                            ),
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'نظام إدارة استوديو التصوير',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'نظام شامل لإدارة استوديوهات التصوير',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.white70,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // معلومات النسخة
                _buildInfoCard(
                  title: 'معلومات النسخة',
                  icon: Icons.info_outline,
                  iconColor: AppColors.info,
                  children: [
                    _buildInfoRow('رقم النسخة', '1.0.0'),
                    _buildInfoRow('تاريخ الإصدار', '2025/07/18'),
                    _buildInfoRow('نوع النسخة', 'إصدار تجريبي'),
                    _buildInfoRow('الحالة', 'مستقر'),
                  ],
                ),

                const SizedBox(height: 16),

                // معلومات المطور
                _buildInfoCard(
                  title: 'معلومات المطور',
                  icon: Icons.code,
                  iconColor: AppColors.success,
                  children: [
                    _buildInfoRow('تم التطوير من قبل', 'فريق تطوير البرمجيات'),
                    _buildInfoRow('الشركة', 'شركة التقنيات المتقدمة'),
                    _buildInfoRow('سنة التأسيس', '2020'),
                    _buildInfoRow('الخبرة', '5+ سنوات'),
                  ],
                ),

                const SizedBox(height: 16),

                // معلومات التواصل
                _buildInfoCard(
                  title: 'معلومات التواصل',
                  icon: Icons.contact_support,
                  iconColor: AppColors.warning,
                  children: [
                    _buildContactRow(
                      'رقم الهاتف',
                      '+966 50 123 4567',
                      Icons.phone,
                      () => _copyToClipboard('+966 50 123 4567', 'رقم الهاتف'),
                    ),
                    _buildContactRow(
                      'البريد الإلكتروني',
                      '<EMAIL>',
                      Icons.email,
                      () => _copyToClipboard('<EMAIL>', 'البريد الإلكتروني'),
                    ),
                    _buildContactRow(
                      'الموقع الإلكتروني',
                      'www.studio-app.com',
                      Icons.web,
                      () => _copyToClipboard('www.studio-app.com', 'الموقع الإلكتروني'),
                    ),
                    _buildContactRow(
                      'الدعم الفني',
                      '<EMAIL>',
                      Icons.support_agent,
                      () => _copyToClipboard('<EMAIL>', 'الدعم الفني'),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // المميزات
                _buildInfoCard(
                  title: 'مميزات البرنامج',
                  icon: Icons.star,
                  iconColor: AppColors.error,
                  children: [
                    _buildFeatureRow('إدارة الحجوزات والعملاء'),
                    _buildFeatureRow('نظام الصندوق والتقارير المالية'),
                    _buildFeatureRow('تصدير البيانات إلى Excel'),
                    _buildFeatureRow('طباعة الوصولات والفواتير'),
                    _buildFeatureRow('إدارة الخدمات والعروض'),
                    _buildFeatureRow('نظام التنبيهات والمهام'),
                    _buildFeatureRow('إدارة المستخدمين والصلاحيات'),
                    _buildFeatureRow('نسخ احتياطي للبيانات'),
                  ],
                ),

                const SizedBox(height: 16),

                // الترخيص
                _buildInfoCard(
                  title: 'الترخيص والحقوق',
                  icon: Icons.gavel,
                  iconColor: AppColors.primaryBlue,
                  children: [
                    _buildInfoRow('نوع الترخيص', 'ترخيص تجاري'),
                    _buildInfoRow('الحقوق', 'محفوظة للمطور'),
                    _buildInfoRow('الضمان', 'سنة واحدة'),
                    _buildInfoRow('التحديثات', 'مجانية لمدة عام'),
                  ],
                ),

                const SizedBox(height: 24),

                // زر العودة
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryBlue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    icon: const Icon(Icons.arrow_back),
                    label: const Text(
                      'العودة للصفحة الرئيسية',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required Color iconColor,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: iconColor, size: 24),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: AppColors.secondaryText,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactRow(String label, String value, IconData icon, VoidCallback onTap) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: AppColors.secondaryText,
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: onTap,
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      value,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: AppColors.primaryBlue,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    icon,
                    size: 18,
                    color: AppColors.primaryBlue,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureRow(String feature) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          const Icon(
            Icons.check_circle,
            color: AppColors.success,
            size: 18,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              feature,
              style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _copyToClipboard(String text, String label) async {
    await Clipboard.setData(ClipboardData(text: text));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم نسخ $label: $text'),
          backgroundColor: AppColors.success,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}
