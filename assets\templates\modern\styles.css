body {
  font-family: 'Cairo', 'NotoSansArabic', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
  background: #f7f7fa;
  color: #222;
  margin: 0;
  padding: 0;
}
.receipt {
  background: #fff;
  max-width: 800px;
  margin: 30px auto;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08);
  border-radius: 16px;
  padding: 32px 24px;
}
.header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 32px;
  border-bottom: 2px solid #1976d2;
  padding-bottom: 16px;
  margin-bottom: 24px;
  position: relative;
}
.company-logo {
  font-size: 12px;
  margin-left: 0;
  margin-bottom: 0;
  margin-right: 16px;
  order: 0;
}
.company-logo img {
  max-width: 80px;
  max-height: 80px;
  width: 80px;
  height: 80px;
}
.left-logo {
  margin-left: 0;
  margin-right: auto;
  order: 1;
}
.company-name {
  text-align: center;
  font-size: 22px;
  font-weight: bold;
  color: #1976d2;
  margin-right: 16px;
  order: 2;
}
.main-company-name {
  margin-bottom: 0px;
}
.main-company-address {
  text-align: center;
  font-size: 16px;
  color: #666;
  margin-bottom: 12px;
  margin-top: 2px;
  text-align: center;
  font-size: 28px;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 0px;
  margin-top: 10px;
}
.website-name {
  text-align: center;
  font-size: 16px;
  color: #666;
  margin-bottom: 12px;
  margin-top: 2px;
}
.company-address {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  margin-right: 16px;
  order: 2;
}
.receipt-title {
  font-size: 18px;
  color: #1976d2;
  font-weight: 600;
  margin-bottom: 0;
  margin-right: 16px;
  order: 2;
}
.receipt-number {
  font-size: 15px;
  color: #444;
  background: #e3f2fd;
  padding: 6px 14px;
  border-radius: 18px;
  display: inline-block;
  margin-top: 0;
  order: 3;
}
.main-content {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
}
.section {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 8px;
  flex: 1;
  border-right: 4px solid #1976d2;
}
.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 10px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 4px;
}
.info-row {
  display: flex;
  justify-content: space-between;
  padding: 7px 0;
  border-bottom: 1px solid #eee;
  font-size: 15px;
}
.info-row:last-child {
  border-bottom: none;
}
.label {
  font-weight: 600;
  color: #444;
  min-width: 90px;
}
.value {
  color: #1976d2;
  text-align: left;
  flex: 1;
}
.services-section {
  margin: 18px 0;
}
.services-table {
  width: 100%;
  border-collapse: collapse;
  margin: 10px 0;
  font-size: 15px;
}
.services-table th, .services-table td {
  border: 1px solid #ddd;
  padding: 10px;
  text-align: center;
}
.services-table th {
  background: #1976d2;
  color: white;
  font-weight: bold;
  font-size: 16px;
}
.services-table th:nth-child(3), .services-table td:nth-child(3) {
  text-align: left;
  font-weight: bold;
  color: #1976d2;
  background-color: #e3f2fd;
}
.services-table tr:nth-child(even) {
  background: #f9f9f9;
}
.services-table tr:hover {
  background: #f5f5f5;
}
.services-table .total-row {
  font-weight: bold;
  background: #e3f2fd !important;
}
.services-table .total-row td {
  font-size: 17px;
  color: #1976d2;
}
.total-section {
  background: linear-gradient(135deg, #1976d2, #1565c0);
  color: white;
  padding: 18px;
  margin: 24px 0;
  text-align: center;
  border-radius: 10px;
  box-shadow: 0 3px 6px rgba(25,118,210,0.15);
}
.total-label {
  font-size: 16px;
  margin-bottom: 10px;
  opacity: 0.9;
}
.total-amount {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 12px;
}
.financial-details {
  display: flex;
  gap: 18px;
  font-size: 15px;
  margin-top: 12px;
  justify-content: center;
}
.financial-item {
  text-align: center;
  padding: 7px;
  background: rgba(255,255,255,0.15);
  border-radius: 6px;
}
.notes-section {
  background: #fffde7;
  border: 1px solid #ffe082;
  border-radius: 8px;
  padding: 16px;
  margin: 18px 0;
}
.notes-title {
  font-weight: bold;
  color: #fbc02d;
  margin-bottom: 8px;
  font-size: 16px;
}
.notes-content {
  color: #fbc02d;
  font-size: 15px;
  line-height: 1.6;
}
.footer {
  text-align: center;
  margin-top: auto;
  padding-top: 24px;
  border-top: 1px solid #eee;
  color: #888;
  font-size: 14px;
}
.footer-line {
  margin: 5px 0;
}
.thank-you {
  font-weight: bold;
  color: #1976d2;
  margin: 10px 0;
  font-size: 17px;
}
@media print {
  body { background: white !important; -webkit-print-color-adjust: exact; print-color-adjust: exact; }
  .receipt { box-shadow: none !important; min-height: auto; }
  @page { margin: 10mm; }
}
@media screen and (max-width: 768px) {
  .main-content { flex-direction: column; gap: 12px; }
  .financial-details { flex-direction: column; gap: 8px; }
}
