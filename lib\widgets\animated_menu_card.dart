import 'package:flutter/material.dart';
import '../utils/app_colors.dart';
import '../screens/home_screen.dart';

class AnimatedMenuCard extends StatefulWidget {
  final MenuOption option;
  final Duration delay;

  const AnimatedMenuCard({
    super.key,
    required this.option,
    this.delay = Duration.zero,
  });

  @override
  State<AnimatedMenuCard> createState() => _AnimatedMenuCardState();
}

class _AnimatedMenuCardState extends State<AnimatedMenuCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });
    
    if (isHovered) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: MouseRegion(
            onEnter: (_) => _onHover(true),
            onExit: (_) => _onHover(false),
            child: GestureDetector(
                onTap: () {
                  // Add haptic feedback
                  _animationController.forward().then((_) {
                    _animationController.reverse();
                  });
                  widget.option.onTap();
                },
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: _isHovered
                          ? [
                              widget.option.color,
                              widget.option.color.withOpacity(0.8),
                            ]
                          : [
                              AppColors.white,
                              AppColors.white,
                            ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: _isHovered
                            ? widget.option.color.withOpacity(0.3)
                            : AppColors.cardShadow,
                        blurRadius: _isHovered ? 20 : 10,
                        offset: Offset(0, _isHovered ? 10 : 5),
                      ),
                    ],
                    border: Border.all(
                      color: _isHovered
                          ? widget.option.color.withOpacity(0.3)
                          : AppColors.mediumGray.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Stack(
                    children: [
                      // Main content centered
                      Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Icon container - centered
                            AnimatedContainer(
                              duration: const Duration(milliseconds: 200),
                              padding: const EdgeInsets.all(3),
                              decoration: BoxDecoration(
                                color: _isHovered
                                    ? AppColors.white.withOpacity(0.2)
                                    : widget.option.color.withOpacity(0.1),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                widget.option.icon,
                                size: 48,
                                color: _isHovered
                                    ? AppColors.white
                                    : widget.option.color,
                              ),
                            ),
                            
                            const SizedBox(height: 8),
                            
                            // Title
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              child: Text(
                                widget.option.title,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: _isHovered
                                      ? AppColors.white
                                      : AppColors.primaryText,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            
                            const SizedBox(height: 4),
                            
                            // Animated indicator
                            AnimatedContainer(
                              duration: const Duration(milliseconds: 200),
                              height: 2,
                              width: _isHovered ? 24 : 12,
                              decoration: BoxDecoration(
                                color: _isHovered
                                    ? AppColors.white
                                    : widget.option.color,
                                borderRadius: BorderRadius.circular(1),
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Badge for notifications count - positioned at top right corner
                      if (widget.option.badgeCount != null && widget.option.badgeCount! > 0)
                        Positioned(
                          top: 8,
                          right: 8,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.error,
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.error.withOpacity(0.3),
                                  blurRadius: 6,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            constraints: const BoxConstraints(
                              minWidth: 20,
                              minHeight: 20,
                            ),
                            child: Text(
                              widget.option.badgeCount! > 99 
                                  ? '99+' 
                                  : widget.option.badgeCount.toString(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 11,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
        );
      },
    );
  }
}
