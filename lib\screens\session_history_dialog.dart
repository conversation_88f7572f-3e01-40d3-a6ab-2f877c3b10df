import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../utils/app_colors.dart';
import '../utils/currency_formatter.dart';
import '../services/database_helper.dart';

class SessionHistoryDialog extends StatefulWidget {
  final DatabaseHelper databaseHelper;

  const SessionHistoryDialog({
    super.key,
    required this.databaseHelper,
  });

  @override
  State<SessionHistoryDialog> createState() => _SessionHistoryDialogState();
}

class _SessionHistoryDialogState extends State<SessionHistoryDialog> {
  List<Map<String, dynamic>> _sessions = [];
  bool _isLoading = true;
  String _filterPeriod = 'الكل';
  Map<String, dynamic>? _statistics;

  @override
  void initState() {
    super.initState();
    _loadSessions();
    _loadStatistics();
  }

  Future<void> _loadSessions() async {
    setState(() => _isLoading = true);
    
    try {
      DateTime? startDate;
      DateTime? endDate;
      
      final now = DateTime.now();
      switch (_filterPeriod) {
        case 'اليوم':
          startDate = DateTime(now.year, now.month, now.day);
          endDate = now;
          break;
        case 'الأسبوع':
          startDate = now.subtract(Duration(days: now.weekday - 1));
          endDate = now;
          break;
        case 'الشهر':
          startDate = DateTime(now.year, now.month, 1);
          endDate = now;
          break;
        case 'الكل':
        default:
          // No date filter
          break;
      }
      
      final sessions = await widget.databaseHelper.getAllCashboxSessions(
        startDate: startDate,
        endDate: endDate,
        limit: 50,
      );
      
      setState(() {
        _sessions = sessions;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading sessions: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadStatistics() async {
    try {
      final stats = await widget.databaseHelper.getSessionStatistics();
      setState(() {
        _statistics = stats;
      });
    } catch (e) {
      print('Error loading statistics: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Header
        Container(
          padding: const EdgeInsets.all(20),
          decoration: const BoxDecoration(
            color: AppColors.primaryBlue,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(4),
              topRight: Radius.circular(4),
            ),
          ),
          child: Row(
            children: [
              const Icon(Icons.history, color: Colors.white, size: 28),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'تاريخ الجلسات',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.close, color: Colors.white),
              ),
            ],
          ),
        ),

        // Statistics
        if (_statistics != null)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: AppColors.veryLightBlue,
            ),
            child: Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'إجمالي الجلسات',
                    '${_statistics!['totalSessions']}',
                    Icons.all_inclusive,
                    AppColors.primaryBlue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'الجلسات النشطة',
                    '${_statistics!['activeSessions']}',
                    Icons.play_circle,
                    AppColors.success,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'جلسات بفروقات',
                    '${_statistics!['sessionsWithDifferences']}',
                    Icons.warning,
                    AppColors.warning,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'متوسط المدة',
                    '${(_statistics!['avgDurationHours'] as num? ?? 0.0).toStringAsFixed(1)}س',
                    Icons.access_time,
                    AppColors.info,
                  ),
                ),
              ],
            ),
          ),

        // Filter
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          decoration: const BoxDecoration(
            color: Colors.white,
            border: Border(
              bottom: BorderSide(color: AppColors.lightGray),
            ),
          ),
          child: Row(
            children: [
              const Text(
                'فلترة:',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: ['الكل', 'اليوم', 'الأسبوع', 'الشهر']
                        .map((period) => Padding(
                              padding: const EdgeInsets.only(right: 8),
                              child: FilterChip(
                                label: Text(period),
                                selected: _filterPeriod == period,
                                onSelected: (selected) {
                                  if (selected) {
                                    setState(() {
                                      _filterPeriod = period;
                                    });
                                    _loadSessions();
                                  }
                                },
                                selectedColor: AppColors.primaryBlue.withOpacity(0.2),
                                checkmarkColor: AppColors.primaryBlue,
                              ),
                            ))
                        .toList(),
                  ),
                ),
              ),
              IconButton(
                onPressed: _loadSessions,
                icon: const Icon(Icons.refresh),
                tooltip: 'تحديث',
              ),
            ],
          ),
        ),

        // Sessions List
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _sessions.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.inbox,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'لا توجد جلسات للعرض',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: _sessions.length,
                      itemBuilder: (context, index) {
                        return _buildSessionCard(_sessions[index]);
                      },
                    ),
        ),
      ],
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          title,
          style: const TextStyle(
            color: AppColors.secondaryText,
            fontSize: 12,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSessionCard(Map<String, dynamic> session) {
    final startDate = DateTime.parse(session['start_date']);
    final endDate = session['end_date'] != null 
        ? DateTime.parse(session['end_date']) 
        : null;
    final isActive = (session['is_active'] ?? 0) == 1;
    final startingBalance = (session['starting_balance'] as num? ?? 0.0).toDouble();
    final endingBalance = session['ending_balance'] != null 
        ? (session['ending_balance'] as num).toDouble() 
        : null;
    final actualBalance = session['actual_balance'] != null 
        ? (session['actual_balance'] as num).toDouble() 
        : null;
    final difference = session['difference'] != null 
        ? (session['difference'] as num).toDouble() 
        : null;
    final openedBy = session['opened_by'] ?? '';
    final closedBy = session['closed_by'];
    final notes = session['notes'];

    Color statusColor;
    String statusText;
    IconData statusIcon;

    if (isActive) {
      statusColor = AppColors.success;
      statusText = 'نشطة';
      statusIcon = Icons.play_circle;
    } else if (difference == null || difference == 0) {
      statusColor = AppColors.primaryBlue;
      statusText = 'متوازنة';
      statusIcon = Icons.check_circle;
    } else if (difference > 0) {
      statusColor = AppColors.warning;
      statusText = 'زيادة';
      statusIcon = Icons.trending_up;
    } else {
      statusColor = AppColors.error;
      statusText = 'نقص';
      statusIcon = Icons.trending_down;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withOpacity(0.3)),
        boxShadow: const [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(statusIcon, color: statusColor, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            statusText,
                            style: TextStyle(
                              color: statusColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: statusColor.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              session['id'],
                              style: TextStyle(
                                color: statusColor,
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        DateFormat('dd/MM/yyyy - HH:mm').format(startDate),
                        style: const TextStyle(
                          color: AppColors.secondaryText,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                if (endDate != null) ...[
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      const Text(
                        'المدة',
                        style: TextStyle(
                          color: AppColors.secondaryText,
                          fontSize: 11,
                        ),
                      ),
                      Text(
                        _formatDuration(endDate.difference(startDate)),
                        style: TextStyle(
                          color: statusColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 13,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Financial details
                Row(
                  children: [
                    Expanded(
                      child: _buildDetailItem(
                        'رصيد البداية',
                        CurrencyFormatter.format(startingBalance),
                        Icons.play_arrow,
                        AppColors.info,
                      ),
                    ),
                    if (endingBalance != null) ...[
                      Expanded(
                        child: _buildDetailItem(
                          'الرصيد المحسوب',
                          CurrencyFormatter.format(endingBalance),
                          Icons.calculate,
                          AppColors.primaryBlue,
                        ),
                      ),
                    ],
                    if (actualBalance != null) ...[
                      Expanded(
                        child: _buildDetailItem(
                          'الرصيد الفعلي',
                          CurrencyFormatter.format(actualBalance),
                          Icons.account_balance,
                          statusColor,
                        ),
                      ),
                    ],
                  ],
                ),

                if (difference != null && difference != 0) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: statusColor.withOpacity(0.3)),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          difference > 0 ? Icons.add_circle : Icons.remove_circle,
                          color: statusColor,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          difference > 0 
                              ? 'زيادة في الصندوق: ${CurrencyFormatter.format(difference)}'
                              : 'نقص في الصندوق: ${CurrencyFormatter.format(difference.abs())}',
                          style: TextStyle(
                            color: statusColor,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],

                // Staff info
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildDetailItem(
                        'فُتحت بواسطة',
                        openedBy,
                        Icons.person,
                        AppColors.secondaryText,
                      ),
                    ),
                    if (closedBy != null) ...[
                      Expanded(
                        child: _buildDetailItem(
                          'أُغلقت بواسطة',
                          closedBy,
                          Icons.person_outline,
                          AppColors.secondaryText,
                        ),
                      ),
                    ],
                  ],
                ),

                // Notes
                if (notes != null && notes.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColors.veryLightBlue,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'ملاحظات:',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 12,
                            color: AppColors.secondaryText,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          notes,
                          style: const TextStyle(
                            color: AppColors.primaryText,
                            fontSize: 13,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            color: AppColors.secondaryText,
            fontSize: 11,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.w500,
            fontSize: 12,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    
    if (hours > 0) {
      return '${hours}س ${minutes}د';
    } else {
      return '${minutes}د';
    }
  }
}
