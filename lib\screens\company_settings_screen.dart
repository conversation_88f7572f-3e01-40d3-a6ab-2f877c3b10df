import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import '../utils/app_colors.dart';
import '../services/html_receipt_enhanced_service.dart';
import '../services/company_settings_service.dart';

class CompanySettingsScreen extends StatefulWidget {
  const CompanySettingsScreen({super.key});

  @override
  State<CompanySettingsScreen> createState() => _CompanySettingsScreenState();
}

class _CompanySettingsScreenState extends State<CompanySettingsScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  
  // Form controllers
  final _companyNameController = TextEditingController();
  final _companyPhoneController = TextEditingController();
  final _companyEmailController = TextEditingController();
  final _companyAddressController = TextEditingController();
  
  // Logo settings
  String? _logoPath;
  String _logoEmoji = '📸';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _loadCompanySettings();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _companyNameController.dispose();
    _companyPhoneController.dispose();
    _companyEmailController.dispose();
    _companyAddressController.dispose();
    super.dispose();
  }

  Future<void> _loadCompanySettings() async {
    setState(() => _isLoading = true);
    
    try {
      final settings = await CompanySettingsService.getCompanySettings();
      setState(() {
        _companyNameController.text = settings['companyName'] ?? 'استوديو الذكريات الجميلة';
        _companyPhoneController.text = settings['companyPhone'] ?? '0555123456';
        _companyEmailController.text = settings['companyEmail'] ?? '<EMAIL>';
        _companyAddressController.text = settings['companyAddress'] ?? '';
        _logoPath = settings['companyLogoPath'];
        _logoEmoji = settings['companyLogo'] ?? '📸';
      });
    } catch (e) {
      _showSnackBar('خطأ في تحميل إعدادات الشركة: $e', AppColors.error);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _saveCompanySettings() async {
    setState(() => _isLoading = true);
    
    try {
      final success = await CompanySettingsService.saveCompanySettings(
        companyName: _companyNameController.text,
        companyPhone: _companyPhoneController.text,
        companyEmail: _companyEmailController.text,
        companyAddress: _companyAddressController.text,
        companyLogoEmoji: _logoEmoji,
        companyLogoPath: _logoPath,
      );
      
      if (success) {
        _showSnackBar('تم حفظ إعدادات الشركة بنجاح', AppColors.success);
      } else {
        _showSnackBar('فشل في حفظ إعدادات الشركة', AppColors.error);
      }
    } catch (e) {
      _showSnackBar('خطأ في حفظ إعدادات الشركة: $e', AppColors.error);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _selectCompanyLogo() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: HtmlReceiptService.getSupportedImageFormats(),
        dialogTitle: 'اختر شعار الشركة',
      );

      if (result != null && result.files.single.path != null) {
        final filePath = result.files.single.path!;
        
        // Validate file
        if (HtmlReceiptService.isValidImageFile(filePath)) {
          setState(() {
            _logoPath = filePath;
          });
          _showSnackBar('تم اختيار الشعار بنجاح', AppColors.success);
        } else {
          _showSnackBar('نوع الملف غير مدعوم. الرجاء اختيار صورة بصيغة PNG أو JPG', AppColors.error);
        }
      }
    } catch (e) {
      _showSnackBar('خطأ في اختيار الشعار: $e', AppColors.error);
    }
  }

  void _clearLogo() async {
    setState(() {
      _logoPath = null;
    });
    
    // Clear from storage as well
    await CompanySettingsService.clearCompanyLogoPath();
    _showSnackBar('تم حذف الشعار', AppColors.info);
  }

  Future<void> _testReceiptWithSettings() async {
    if (_companyNameController.text.isEmpty) {
      _showSnackBar('الرجاء إدخال اسم الشركة أولاً', AppColors.warning);
      return;
    }

    _showSnackBar('سيتم إضافة معاينة الوصل قريباً', AppColors.info);
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.offWhite,
      appBar: AppBar(
        title: const Text('إعدادات الشركة'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _testReceiptWithSettings,
            icon: const Icon(Icons.preview),
            tooltip: 'معاينة الوصل',
          ),
          IconButton(
            onPressed: _isLoading ? null : _saveCompanySettings,
            icon: _isLoading 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.save),
            tooltip: 'حفظ الإعدادات',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : AnimationLimiter(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: AnimationConfiguration.toStaggeredList(
                    duration: const Duration(milliseconds: 375),
                    childAnimationBuilder: (widget) => SlideAnimation(
                      verticalOffset: 50.0,
                      child: FadeInAnimation(child: widget),
                    ),
                    children: [
                      // Logo Section
                      _buildLogoSection(),
                      const SizedBox(height: 24),
                      
                      // Company Info Section
                      _buildCompanyInfoSection(),
                      const SizedBox(height: 24),
                      
                      // Contact Info Section
                      _buildContactInfoSection(),
                      const SizedBox(height: 24),
                      
                      // Action Buttons
                      _buildActionButtons(),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildLogoSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.image, color: AppColors.primaryBlue),
                const SizedBox(width: 12),
                const Text(
                  'شعار الشركة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryText,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Logo Preview
            Center(
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.grey[50],
                ),
                child: _logoPath != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(11),
                        child: Image.file(
                          File(_logoPath!),
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(Icons.error, color: AppColors.error),
                                  const SizedBox(height: 8),
                                  Text(
                                    'خطأ في الصورة',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      )
                    : Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              _logoEmoji,
                              style: const TextStyle(fontSize: 48),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'شعار افتراضي',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
              ),
            ),
            const SizedBox(height: 16),
            
            // Logo Actions
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _selectCompanyLogo,
                    icon: const Icon(Icons.photo_library),
                    label: const Text('اختيار صورة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryBlue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                if (_logoPath != null)
                  ElevatedButton.icon(
                    onPressed: _clearLogo,
                    icon: const Icon(Icons.clear),
                    label: const Text('حذف'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.error,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Emoji Selector
            const Text(
              'أو اختر رمز تعبيري:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: ['📸', '🎥', '🖼️', '📷', '🎨', '✨', '⭐', '💎']
                  .map((emoji) => GestureDetector(
                        onTap: () => setState(() => _logoEmoji = emoji),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: _logoEmoji == emoji
                                ? AppColors.primaryBlue.withOpacity(0.2)
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: _logoEmoji == emoji
                                  ? AppColors.primaryBlue
                                  : Colors.grey.shade300,
                            ),
                          ),
                          child: Text(emoji, style: const TextStyle(fontSize: 24)),
                        ),
                      ))
                  .toList(),
            ),
            
            if (HtmlReceiptService.getSupportedImageFormats().isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                'الصيغ المدعومة: ${HtmlReceiptService.getSupportedImageFormats().join(', ').toUpperCase()}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCompanyInfoSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.business, color: AppColors.primaryBlue),
                const SizedBox(width: 12),
                const Text(
                  'معلومات الشركة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryText,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            TextField(
              controller: _companyNameController,
              decoration: const InputDecoration(
                labelText: 'اسم الشركة *',
                hintText: 'مثال: استوديو الذكريات الجميلة',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.business_center),
              ),
            ),
            const SizedBox(height: 16),
            
            TextField(
              controller: _companyAddressController,
              decoration: const InputDecoration(
                labelText: 'عنوان الشركة',
                hintText: 'مثال: الرياض - المملكة العربية السعودية',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.location_on),
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactInfoSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.contact_phone, color: AppColors.primaryBlue),
                const SizedBox(width: 12),
                const Text(
                  'معلومات الاتصال',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryText,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            TextField(
              controller: _companyPhoneController,
              decoration: const InputDecoration(
                labelText: 'رقم الهاتف *',
                hintText: '0555123456',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.phone),
              ),
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: 16),
            
            TextField(
              controller: _companyEmailController,
              decoration: const InputDecoration(
                labelText: 'البريد الإلكتروني',
                hintText: '<EMAIL>',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _isLoading ? null : _saveCompanySettings,
            icon: _isLoading 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.save),
            label: Text(_isLoading ? 'جاري الحفظ...' : 'حفظ الإعدادات'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.success,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
        ),
        const SizedBox(height: 12),
        
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _testReceiptWithSettings,
            icon: const Icon(Icons.preview),
            label: const Text('معاينة الوصل'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.primaryBlue,
              padding: const EdgeInsets.symmetric(vertical: 16),
              side: const BorderSide(color: AppColors.primaryBlue),
              textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
        ),
      ],
    );
  }
}
