import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../utils/app_colors.dart';
import '../utils/currency_formatter.dart';
import '../utils/date_formatter.dart';
import '../models/booking.dart';
import '../services/html_receipt_enhanced_service.dart';
import '../services/company_settings_service.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';

class DeliveryReceiptPrintScreen extends StatelessWidget {
  final Booking booking;
  final String deliveryMethod;
  final String deliveryNotes;
  final double additionalCost;
  final double paidOnDelivery;

  const DeliveryReceiptPrintScreen({
    super.key,
    required this.booking,
    required this.deliveryMethod,
    required this.deliveryNotes,
    required this.additionalCost,
    required this.paidOnDelivery,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('وصل التسليم'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save_alt),
            onPressed: () async {
              try {
                // الحصول على إعدادات الشركة
                final companySettings = await CompanySettingsService.getCompanySettings();
                
                // إنشاء وطباعة وصل التسليم كملف HTML
                await HtmlReceiptService.printHtmlReceiptDirect(
                  booking: booking,
                  templateName: 'modern',
                  receiptType: 'delivery',
                  customData: {
                    ...companySettings,
                    'deliveryMethod': deliveryMethod,
                    'deliveryNotes': deliveryNotes,
                    'additionalCost': additionalCost,
                    'paidOnDelivery': paidOnDelivery,
                  },
                );
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم إنشاء وطباعة وصل التسليم بنجاح'),
                      backgroundColor: AppColors.success,
                      duration: Duration(seconds: 3),
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  // Check if it's a template-related error
                  final errorMessage = e.toString();
                  if (errorMessage.contains('يجب إنشاء قالب طباعة أولاً')) {
                    _showTemplateRequiredDialog(context);
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('خطأ في الطباعة: $e'),
                        backgroundColor: AppColors.error,
                      ),
                    );
                  }
                }
              }
            },
          ),
        ],
      ),
      body: Container(
        color: Colors.white,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              _buildReceiptContent(),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton.icon(
                    onPressed: () async {
                      try {
                        // الحصول على إعدادات الشركة
                        final companySettings = await CompanySettingsService.getCompanySettings();
                        final htmlPath = await HtmlReceiptService.generateHtmlReceipt(
                          booking: booking,
                          templateName: 'modern',
                          receiptType: 'delivery',
                          customData: {
                            ...companySettings,
                            'deliveryMethod': deliveryMethod,
                            'deliveryNotes': deliveryNotes,
                            'additionalCost': additionalCost,
                            'paidOnDelivery': paidOnDelivery,
                          },
                        );
                        // إنشاء PDF باستخدام printing.convertHtml
                        final pdfBytes = await HtmlReceiptService.convertHtmlReceiptToPdf(
                          booking: booking,
                          templateName: 'modern',
                          receiptType: 'delivery',
                          customData: {
                            ...companySettings,
                            'deliveryMethod': deliveryMethod,
                            'deliveryNotes': deliveryNotes,
                            'additionalCost': additionalCost,
                            'paidOnDelivery': paidOnDelivery,
                          },
                        );
                        final directory = await getApplicationDocumentsDirectory();
                        final filePath = '${directory.path}/delivery_receipt_${DateTime.now().millisecondsSinceEpoch}.pdf';
                        final file = File(filePath);
                        await file.writeAsBytes(pdfBytes);
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Row(
                                children: [
                                  const Icon(Icons.check_circle, color: Colors.white),
                                  const SizedBox(width: 8),
                                  Expanded(child: Text('تم حفظ وصل التسليم كملف HTML و PDF بنجاح في المستندات')),
                                  TextButton(
                                    onPressed: () async {
                                      // فتح ملف PDF مباشرة
                                      await Process.run('explorer', [filePath]);
                                    },
                                    child: const Text('فتح PDF', style: TextStyle(color: Colors.white)),
                                  ),
                                ],
                              ),
                              backgroundColor: AppColors.success,
                              duration: const Duration(seconds: 6),
                            ),
                          );
                        }
                      } catch (e) {
                        if (context.mounted) {
                          // Check if it's a template-related error
                          final errorMessage = e.toString();
                          if (errorMessage.contains('يجب إنشاء قالب طباعة أولاً')) {
                            _showTemplateRequiredDialog(context);
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('خطأ في حفظ الوصل: $e'),
                                backgroundColor: AppColors.error,
                              ),
                            );
                          }
                        }
                      }
                    },
                    icon: const Icon(Icons.save_alt),
                    label: const Text('حفظ كصورة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryBlue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                    label: const Text('إغلاق'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.secondaryText,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildReceiptContent() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.black, width: 2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Center(
            child: Column(
              children: [
                const Icon(
                  Icons.camera_alt,
                  size: 48,
                  color: AppColors.primaryBlue,
                ),
                const SizedBox(height: 8),
                const Text(
                  'استوديو التصوير',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryBlue,
                  ),
                ),
                const SizedBox(height: 4),
                const Text(
                  'وصل تسليم',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryText,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'التاريخ: ${DateFormat('dd/MM/yyyy').format(DateTime.now())}',
                  style: const TextStyle(fontSize: 16),
                ),
                Text(
                  'الوقت: ${DateFormatter.formatTimeShort12Hour(DateTime.now())}',
                  style: const TextStyle(fontSize: 16),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          const Divider(thickness: 2),
          const SizedBox(height: 16),
          
          // Customer Information
          _buildSectionTitle('بيانات العميل'),
          _buildReceiptRow('اسم العميل', booking.customerName),
          _buildReceiptRow('رقم الهاتف', booking.customerPhone),
          FutureBuilder<String>(
            future: booking.formattedReceiptNumber,
            builder: (context, snapshot) {
              return _buildReceiptRow('رقم الطلب', snapshot.data ?? "جاري التحميل...");
            },
          ),
          _buildReceiptRow('تاريخ الحجز', DateFormat('dd/MM/yyyy').format(booking.bookingDate)),
          _buildReceiptRow('تاريخ التسليم المتوقع', DateFormat('dd/MM/yyyy').format(booking.deliveryDate)),
          
          const SizedBox(height: 16),
          const Divider(),
          const SizedBox(height: 16),
          
          // Service Information
          _buildSectionTitle('تفاصيل الخدمة'),
          _buildReceiptRow('نوع الخدمة', booking.serviceType),
          if (booking.bookedBy != null && booking.bookedBy!.isNotEmpty)
            _buildReceiptRow('حجز بواسطة', booking.bookedBy!),
          
          const SizedBox(height: 16),
          const Divider(),
          const SizedBox(height: 16),
          
          // Delivery Information
          _buildSectionTitle('تفاصيل التسليم'),
          _buildReceiptRow('طريقة التسليم', deliveryMethod),
          _buildReceiptRow('تفاصيل التسليم', deliveryNotes),
          _buildReceiptRow('تاريخ التسليم الفعلي', DateFormat('dd/MM/yyyy').format(DateTime.now())),
          
          const SizedBox(height: 16),
          const Divider(),
          const SizedBox(height: 16),
          
          // Financial Information
          _buildSectionTitle('التفاصيل المالية'),
          
          // المبلغ الأساسي هو المبلغ الأصلي بدون التكلفة الإضافية
          _buildReceiptRow('المبلغ الأساسي', CurrencyFormatter.format(booking.totalAmount - additionalCost)),
          
          if (booking.discount > 0) ...[
            _buildReceiptRow(
              'الخصم',
              '- ${CurrencyFormatter.format(booking.discount)}',
              valueColor: AppColors.success,
            ),
            _buildReceiptRow(
              'المبلغ بعد الخصم',
              CurrencyFormatter.format((booking.totalAmount - additionalCost) - booking.discount),
              isBold: true,
              valueColor: AppColors.primaryBlue,
            ),
          ],
          
          if (additionalCost > 0) ...[
            _buildReceiptRow(
              'تكلفة إضافية ($deliveryMethod)',
              '+ ${CurrencyFormatter.format(additionalCost)}',
              valueColor: AppColors.warning,
            ),
          ],
          
          const Divider(),
          _buildReceiptRow(
            'المبلغ الإجمالي النهائي',
            CurrencyFormatter.format(booking.totalAmount),
            isBold: true,
            valueColor: AppColors.primaryText,
          ),
          
          const SizedBox(height: 8),
          _buildReceiptRow(
            'العربون المدفوع سابقاً',
            CurrencyFormatter.format(booking.paidAmount - paidOnDelivery),
            valueColor: AppColors.success,
          ),
          
          if (paidOnDelivery > 0) ...[
            _buildReceiptRow(
              'مدفوع عند التسليم',
              CurrencyFormatter.format(paidOnDelivery),
              valueColor: AppColors.primaryBlue,
            ),
          ],
          
          const Divider(thickness: 2),
          _buildReceiptRow(
            'إجمالي المدفوع',
            CurrencyFormatter.format(booking.paidAmount),
            isBold: true,
            valueColor: AppColors.primaryBlue,
          ),
          
          if (booking.calculatedRemainingAmount > 0) ...[
            _buildReceiptRow(
              'المبلغ المتبقي',
              CurrencyFormatter.format(booking.calculatedRemainingAmount),
              isBold: true,
              valueColor: AppColors.warning,
            ),
          ] else ...[
            _buildReceiptRow(
              'حالة الدفع',
              'مسدد بالكامل ✓',
              isBold: true,
              valueColor: AppColors.success,
            ),
          ],
          
          if (booking.notes != null && booking.notes!.isNotEmpty) ...[
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            _buildSectionTitle('ملاحظات'),
            Text(
              booking.notes!,
              style: const TextStyle(fontSize: 14),
            ),
          ],
          
          const SizedBox(height: 32),
          
          // Footer
          const Divider(thickness: 2),
          const SizedBox(height: 16),
          
          const Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'توقيع العميل',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 30),
                  Text('_________________'),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'توقيع الموظف',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 30),
                  Text('_________________'),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          const Center(
            child: Text(
              'شكراً لاختياركم استوديو التصوير',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
          ),
          
          const SizedBox(height: 8),
          
          const Center(
            child: Text(
              'نتمنى لكم تجربة ممتعة',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.secondaryText,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: AppColors.primaryBlue,
        ),
      ),
    );
  }

  Widget _buildReceiptRow(
    String label,
    String value, {
    bool isBold = false,
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '$label:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              color: valueColor ?? AppColors.primaryText,
            ),
          ),
        ],
      ),
    );
  }

  static void _showTemplateRequiredDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: const Icon(
          Icons.warning_amber_rounded,
          color: Colors.orange,
          size: 64,
        ),
        title: const Text('مطلوب قالب طباعة'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'لا يمكن طباعة الوصل بدون قالب طباعة.',
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 12),
            Text(
              'يجب إنشاء قالب طباعة أولاً من إعدادات القوالب.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }
}
