import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../utils/app_colors.dart';
import '../services/database_helper.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _messageController = TextEditingController();
  
  List<Map<String, dynamic>> _notifications = [];
  List<Map<String, dynamic>> _filteredNotifications = [];
  List<Map<String, dynamic>> _upcomingBookings = [];
  String _selectedType = 'تذكير';
  DateTime _selectedDateTime = DateTime.now().add(const Duration(hours: 1));
  bool _isLoading = false;

  final List<String> _notificationTypes = [
    'تذكير',
    'موعد',
    'تنبيه',
    'إعلان',
    'متابعة',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _titleController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      await _loadNotifications();
      await _loadUpcomingBookings();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadNotifications() async {
    final notifications = await _databaseHelper.getAllNotifications();
    setState(() {
      _notifications = notifications;
      _filteredNotifications = notifications;
    });
  }

  Future<void> _loadUpcomingBookings() async {
    final allBookings = await _databaseHelper.getAllBookings();
    final now = DateTime.now();
    final threeDaysFromNow = now.add(const Duration(days: 3));
    
    // فلترة الحجوزات القريبة من تاريخ التسليم
    final upcomingDeliveries = allBookings.where((booking) {
      // فقط الحجوزات قيد العمل
      if (booking.status.name != 'inProgress') return false;
      
      // الحجوزات التي تاريخ تسليمها خلال الثلاثة أيام القادمة أو متأخرة
      final deliveryDate = booking.deliveryDate;
      return deliveryDate.isBefore(threeDaysFromNow) || deliveryDate.isBefore(now);
    }).toList();
    
    setState(() {
      _upcomingBookings = upcomingDeliveries.map((booking) => {
        'id': booking.id,
        'customer_name': booking.customerName,
        'service_name': booking.serviceType,
        'booking_date': booking.bookingDate.toIso8601String(),
        'delivery_date': booking.deliveryDate.toIso8601String(),
        'is_overdue': booking.deliveryDate.isBefore(now),
      }).toList();
      
      // ترتيب الحجوزات: المتأخرة أولاً، ثم حسب تاريخ التسليم
      _upcomingBookings.sort((a, b) {
        final aOverdue = a['is_overdue'] as bool;
        final bOverdue = b['is_overdue'] as bool;
        
        if (aOverdue && !bOverdue) return -1;
        if (!aOverdue && bOverdue) return 1;
        
        final aDate = DateTime.parse(a['delivery_date']);
        final bDate = DateTime.parse(b['delivery_date']);
        return aDate.compareTo(bDate);
      });
    });
  }

  Future<void> _addNotification() async {
    if (_titleController.text.isEmpty || _messageController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى ملء الحقول المطلوبة'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    try {
      await _databaseHelper.insertNotification({
        'title': _titleController.text,
        'message': _messageController.text,
        'type': _selectedType,
        'scheduled_time': _selectedDateTime.toIso8601String(),
        'is_read': false,
        'created_at': DateTime.now().toIso8601String(),
      });

      _titleController.clear();
      _messageController.clear();
      _selectedType = 'تذكير';
      _selectedDateTime = DateTime.now().add(const Duration(hours: 1));

      await _loadNotifications();
      
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة الإشعار بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة الإشعار: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _markAsRead(String notificationId) async {
    try {
      await _databaseHelper.markNotificationAsRead(notificationId);
      await _loadNotifications();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث الإشعار: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _deleteNotification(String notificationId) async {
    try {
      await _databaseHelper.deleteNotification(notificationId);
      await _loadNotifications();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف الإشعار بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف الإشعار: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Widget _buildAddNotificationTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const Text(
                    'إضافة إشعار جديد',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                  const SizedBox(height: 20),
                  TextFormField(
                    controller: _titleController,
                    decoration: InputDecoration(
                      labelText: 'العنوان *',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      prefixIcon: const Icon(Icons.title),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _messageController,
                    maxLines: 3,
                    decoration: InputDecoration(
                      labelText: 'الرسالة *',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      prefixIcon: const Icon(Icons.message),
                    ),
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    value: _selectedType,
                    decoration: InputDecoration(
                      labelText: 'نوع الإشعار',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      prefixIcon: const Icon(Icons.category),
                    ),
                    items: _notificationTypes.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(type),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedType = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  ListTile(
                    leading: const Icon(Icons.schedule),
                    title: const Text('وقت الإشعار'),
                    subtitle: Text(
                      '${_selectedDateTime.day}/${_selectedDateTime.month}/${_selectedDateTime.year} - ${_selectedDateTime.hour.toString().padLeft(2, '0')}:${_selectedDateTime.minute.toString().padLeft(2, '0')}',
                    ),
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: _selectedDateTime,
                        firstDate: DateTime.now(),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                      );
                      if (date != null) {
                        final time = await showTimePicker(
                          context: context,
                          initialTime: TimeOfDay.fromDateTime(_selectedDateTime),
                        );
                        if (time != null) {
                          setState(() {
                            _selectedDateTime = DateTime(
                              date.year,
                              date.month,
                              date.day,
                              time.hour,
                              time.minute,
                            );
                          });
                        }
                      }
                    },
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: _addNotification,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryBlue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'إضافة الإشعار',
                      style: TextStyle(fontSize: 16),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationsListTab() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: Card(
                  elevation: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        const Icon(Icons.notifications, color: AppColors.info),
                        const SizedBox(height: 8),
                        Text(
                          '${_notifications.length}',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: AppColors.info,
                          ),
                        ),
                        const Text(
                          'إجمالي الإشعارات',
                          style: TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Card(
                  elevation: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        const Icon(Icons.mark_email_unread, color: AppColors.warning),
                        const SizedBox(height: 8),
                        Text(
                          '${_notifications.where((n) => n['is_read'] == 0).length}',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: AppColors.warning,
                          ),
                        ),
                        const Text(
                          'غير مقروءة',
                          style: TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _filteredNotifications.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.notifications_none,
                            size: 64,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'لا توجد إشعارات',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    )
                  : AnimationLimiter(
                      child: ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _filteredNotifications.length,
                        itemBuilder: (context, index) {
                          final notification = _filteredNotifications[index];
                          final scheduledTime = DateTime.parse(notification['scheduled_time']);
                          final isRead = notification['is_read'] == 1;
                          final isOverdue = scheduledTime.isBefore(DateTime.now());
                          
                          return AnimationConfiguration.staggeredList(
                            position: index,
                            duration: const Duration(milliseconds: 375),
                            child: SlideAnimation(
                              verticalOffset: 50.0,
                              child: FadeInAnimation(
                                child: Card(
                                  margin: const EdgeInsets.only(bottom: 12),
                                  elevation: 2,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    side: BorderSide(
                                      color: isRead 
                                          ? Colors.grey.withOpacity(0.3)
                                          : isOverdue 
                                              ? AppColors.error.withOpacity(0.5)
                                              : AppColors.info.withOpacity(0.5),
                                      width: 1,
                                    ),
                                  ),
                                  child: ListTile(
                                    leading: CircleAvatar(
                                      backgroundColor: isRead 
                                          ? Colors.grey.withOpacity(0.2)
                                          : isOverdue 
                                              ? AppColors.error.withOpacity(0.1)
                                              : AppColors.info.withOpacity(0.1),
                                      child: Icon(
                                        isRead 
                                            ? Icons.notifications_none
                                            : isOverdue 
                                                ? Icons.notification_important
                                                : Icons.notifications_active,
                                        color: isRead 
                                            ? Colors.grey
                                            : isOverdue 
                                                ? AppColors.error
                                                : AppColors.info,
                                      ),
                                    ),
                                    title: Text(
                                      notification['title'],
                                      style: TextStyle(
                                        fontWeight: isRead ? FontWeight.normal : FontWeight.bold,
                                        color: isRead ? Colors.grey : null,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          notification['message'],
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: isRead ? Colors.grey : null,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Row(
                                          children: [
                                            Container(
                                              padding: const EdgeInsets.symmetric(
                                                horizontal: 8,
                                                vertical: 2,
                                              ),
                                              decoration: BoxDecoration(
                                                color: _getTypeColor(notification['type']).withOpacity(0.1),
                                                borderRadius: BorderRadius.circular(12),
                                              ),
                                              child: Text(
                                                notification['type'],
                                                style: TextStyle(
                                                  fontSize: 10,
                                                  color: _getTypeColor(notification['type']),
                                                ),
                                              ),
                                            ),
                                            const SizedBox(width: 8),
                                            Text(
                                              '${scheduledTime.day}/${scheduledTime.month} - ${scheduledTime.hour.toString().padLeft(2, '0')}:${scheduledTime.minute.toString().padLeft(2, '0')}',
                                              style: TextStyle(
                                                fontSize: 10,
                                                color: isOverdue ? AppColors.error : Colors.grey,
                                                fontWeight: isOverdue ? FontWeight.bold : FontWeight.normal,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    trailing: PopupMenuButton(
                                      itemBuilder: (context) => [
                                        if (!isRead)
                                          PopupMenuItem(
                                            value: 'read',
                                            child: const Row(
                                              children: [
                                                Icon(Icons.mark_email_read, size: 16),
                                                SizedBox(width: 8),
                                                Text('تم القراءة'),
                                              ],
                                            ),
                                          ),
                                        PopupMenuItem(
                                          value: 'delete',
                                          child: const Row(
                                            children: [
                                              Icon(Icons.delete, size: 16, color: AppColors.error),
                                              SizedBox(width: 8),
                                              Text('حذف', style: TextStyle(color: AppColors.error)),
                                            ],
                                          ),
                                        ),
                                      ],
                                      onSelected: (value) {
                                        if (value == 'read') {
                                          _markAsRead(notification['id']);
                                        } else if (value == 'delete') {
                                          _showDeleteDialog(notification['id']);
                                        }
                                      },
                                    ),
                                    isThreeLine: true,
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
        ),
      ],
    );
  }

  Widget _buildUpcomingBookingsTab() {
    return _isLoading
        ? const Center(child: CircularProgressIndicator())
        : _upcomingBookings.isEmpty
            ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.event_note,
                      size: 64,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'لا توجد حجوزات قادمة',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              )
            : Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Card(
                      elevation: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            Column(
                              children: [
                                const Icon(Icons.schedule, color: AppColors.warning),
                                const SizedBox(height: 4),
                                Text(
                                  '${_upcomingBookings.where((b) => b['is_overdue'] == true).length}',
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.warning,
                                  ),
                                ),
                                const Text(
                                  'متأخرة',
                                  style: TextStyle(fontSize: 12),
                                ),
                              ],
                            ),
                            Column(
                              children: [
                                const Icon(Icons.event_available, color: AppColors.success),
                                const SizedBox(height: 4),
                                Text(
                                  '${_upcomingBookings.where((b) => b['is_overdue'] == false).length}',
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.success,
                                  ),
                                ),
                                const Text(
                                  'قادمة',
                                  style: TextStyle(fontSize: 12),
                                ),
                              ],
                            ),
                            Column(
                              children: [
                                const Icon(Icons.list, color: AppColors.info),
                                const SizedBox(height: 4),
                                Text(
                                  '${_upcomingBookings.length}',
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.info,
                                  ),
                                ),
                                const Text(
                                  'المجموع',
                                  style: TextStyle(fontSize: 12),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: AnimationLimiter(
                      child: ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: _upcomingBookings.length,
                        itemBuilder: (context, index) {
                          final booking = _upcomingBookings[index];
                          final deliveryDate = DateTime.parse(booking['delivery_date']);
                          final isOverdue = booking['is_overdue'] as bool;
                          final now = DateTime.now();
                          final daysUntil = deliveryDate.difference(now).inDays;
                          
                          return AnimationConfiguration.staggeredList(
                            position: index,
                            duration: const Duration(milliseconds: 375),
                            child: SlideAnimation(
                              verticalOffset: 50.0,
                              child: FadeInAnimation(
                                child: Card(
                                  margin: const EdgeInsets.only(bottom: 12),
                                  elevation: 2,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    side: BorderSide(
                                      color: isOverdue 
                                          ? AppColors.error.withOpacity(0.5)
                                          : daysUntil <= 1 
                                              ? AppColors.warning.withOpacity(0.5)
                                              : Colors.transparent,
                                      width: isOverdue || daysUntil <= 1 ? 2 : 0,
                                    ),
                                  ),
                                  child: ListTile(
                                    leading: CircleAvatar(
                                      backgroundColor: isOverdue 
                                          ? AppColors.error.withOpacity(0.1)
                                          : daysUntil <= 1 
                                              ? AppColors.warning.withOpacity(0.1)
                                              : AppColors.success.withOpacity(0.1),
                                      child: Icon(
                                        isOverdue 
                                            ? Icons.warning
                                            : daysUntil <= 1 
                                                ? Icons.schedule
                                                : Icons.event,
                                        color: isOverdue 
                                            ? AppColors.error
                                            : daysUntil <= 1 
                                                ? AppColors.warning
                                                : AppColors.success,
                                      ),
                                    ),
                                    title: Text(
                                      booking['customer_name'],
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: isOverdue ? AppColors.error : null,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          booking['service_name'] ?? 'خدمة عامة',
                                          style: const TextStyle(fontSize: 12),
                                        ),
                                        const SizedBox(height: 4),
                                        Row(
                                          children: [
                                            Icon(
                                              Icons.event_note,
                                              size: 14,
                                              color: isOverdue ? AppColors.error : Colors.grey,
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              'التسليم: ${deliveryDate.day}/${deliveryDate.month}/${deliveryDate.year}',
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: isOverdue ? AppColors.error : Colors.grey,
                                                fontWeight: isOverdue ? FontWeight.bold : FontWeight.normal,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    trailing: Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: isOverdue 
                                            ? AppColors.error.withOpacity(0.1)
                                            : daysUntil <= 1 
                                                ? AppColors.warning.withOpacity(0.1)
                                                : AppColors.info.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        isOverdue 
                                            ? 'متأخر ${(-daysUntil)} يوم'
                                            : daysUntil == 0 
                                                ? 'اليوم'
                                                : daysUntil == 1 
                                                    ? 'غداً'
                                                    : '$daysUntil يوم',
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                          color: isOverdue 
                                              ? AppColors.error
                                              : daysUntil <= 1 
                                                  ? AppColors.warning
                                                  : AppColors.info,
                                        ),
                                      ),
                                    ),
                                    isThreeLine: true,
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ],
              );
  }

  Color _getTypeColor(String type) {
    switch (type) {
      case 'تذكير':
        return AppColors.info;
      case 'موعد':
        return AppColors.success;
      case 'تنبيه':
        return AppColors.warning;
      case 'إعلان':
        return AppColors.primaryBlue;
      case 'متابعة':
        return AppColors.error;
      default:
        return Colors.grey;
    }
  }

  void _showDeleteDialog(String notificationId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا الإشعار؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteNotification(notificationId);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'الإشعارات',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(
              icon: Icon(Icons.event),
              text: 'المواعيد',
            ),
            Tab(
              icon: Icon(Icons.notifications),
              text: 'الإشعارات',
            ),
            Tab(
              icon: Icon(Icons.add_alert),
              text: 'إضافة',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildUpcomingBookingsTab(),
          _buildNotificationsListTab(),
          _buildAddNotificationTab(),
        ],
      ),
    );
  }
}
