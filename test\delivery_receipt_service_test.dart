import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:studio_managment/services/delivery_receipt_service.dart';
import 'package:studio_managment/models/booking.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('Delivery Receipt Service Tests', () {
    setUp(() async {
      // Mock SharedPreferences for testing
      SharedPreferences.setMockInitialValues({
        'company_name': 'ايزو ستوديو',
        'company_phone': '07712345678',
        'company_email': '<EMAIL>',
        'company_address': 'موصل - الغابات - قاعة دريم هاوس',
        'company_logo_emoji': '💎',
      });
    });

    test('should generate delivery receipt HTML with correct content', () async {
      // Arrange
      final booking = Booking(
        id: '1',
        customerName: 'أحمد محمد علي',
        customerPhone: '07701234567',
        serviceType: 'تصوير زفاف',
        eventDate: DateTime.now().add(Duration(days: 7)),
        deliveryDate: DateTime.now().add(Duration(days: 14)),
        totalAmount: 250000.0,
        deposit: 100000.0,
        status: BookingStatus.completed,
        selectedOffers: [
          'تصوير زفاف - 200000 د.ع',
          'فيديو قصير - 50000 د.ع'
        ],
        notes: 'حفل في قاعة دريم هاوس',
        eventDescription: 'حفل زفاف مساء الخميس',
        bookingDate: DateTime.now(),
        bookedBy: 'موظف الاستقبال',
      );

      // Act
      final htmlPath = await DeliveryReceiptService.generateDeliveryReceipt(
        booking: booking,
        templateName: 'modern',
        deliveryMethod: 'توصيل منزلي',
        deliveryNotes: 'تم التسليم للعميل شخصياً في المنزل',
        additionalCost: 5000.0,
        paidOnDelivery: 155000.0, // المبلغ المتبقي + رسوم التوصيل
      );

      // Assert
      expect(htmlPath, isNotNull);
      expect(htmlPath, contains('delivery_receipt_'));
      expect(htmlPath, contains('أحمد_محمد_علي'));
      expect(htmlPath, endsWith('.html'));
      
      // تحقق من وجود الملف
      final file = File(htmlPath);
      final fileExists = await file.exists();
      expect(fileExists, isTrue);
    });

    test('should generate HTML with company settings integration', () async {
      // Arrange
      final booking = Booking(
        id: '2',
        customerName: 'فاطمة أحمد',
        customerPhone: '07909876543',
        serviceType: 'تصوير عائلي',
        eventDate: DateTime.now(),
        deliveryDate: DateTime.now().add(Duration(days: 3)),
        totalAmount: 75000.0,
        deposit: 30000.0,
        status: BookingStatus.completed,
        selectedOffers: ['تصوير عائلي - 75000 د.ع'],
        bookingDate: DateTime.now(),
      );

      // Act
      final htmlPath = await DeliveryReceiptService.generateDeliveryReceipt(
        booking: booking,
        templateName: 'modern',
        deliveryMethod: 'استلام من الاستوديو',
        deliveryNotes: '',
        additionalCost: 0.0,
        paidOnDelivery: 45000.0, // المبلغ المتبقي
      );

      // Assert
      expect(htmlPath, isNotNull);
      
      // قراءة محتوى HTML للتحقق من إعدادات الشركة
      final file = File(htmlPath);
      final htmlContent = await file.readAsString();
      
      expect(htmlContent, contains('ايزو ستوديو'));
      expect(htmlContent, contains('موصل - الغابات - قاعة دريم هاوس'));
      expect(htmlContent, contains('07712345678'));
      expect(htmlContent, contains('💎'));
      expect(htmlContent, contains('وصل تسليم'));
      expect(htmlContent, contains('✅ تم التسليم بنجاح'));
    });

    test('should handle additional costs correctly', () async {
      // Arrange
      final booking = Booking(
        id: '3',
        customerName: 'علي حسن',
        customerPhone: '07801234567',
        serviceType: 'تصوير تخرج',
        eventDate: DateTime.now(),
        deliveryDate: DateTime.now().add(Duration(days: 5)),
        totalAmount: 100000.0,
        deposit: 50000.0,
        status: BookingStatus.completed,
        selectedOffers: ['تصوير تخرج - 100000 د.ع'],
        bookingDate: DateTime.now(),
      );

      final additionalCost = 10000.0; // رسوم توصيل
      final paidOnDelivery = 60000.0; // المتبقي + الرسوم

      // Act
      final htmlPath = await DeliveryReceiptService.generateDeliveryReceipt(
        booking: booking,
        templateName: 'modern',
        deliveryMethod: 'توصيل خارج المدينة',
        deliveryNotes: 'توصيل لمدينة أربيل',
        additionalCost: additionalCost,
        paidOnDelivery: paidOnDelivery,
      );

      // Assert
      expect(htmlPath, isNotNull);
      
      final file = File(htmlPath);
      final htmlContent = await file.readAsString();
      
      // التحقق من وجود الرسوم الإضافية
      expect(htmlContent, contains('رسوم إضافية'));
      expect(htmlContent, contains('10000 د.ع'));
      expect(htmlContent, contains('110000 د.ع')); // المبلغ النهائي
    });
  });
}
