import 'dart:io';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import '../models/booking.dart';
import 'company_settings_service.dart';

import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';

class HtmlReceiptService {
  /// Convert HTML receipt to PDF and return bytes
  static Future<List<int>> convertHtmlReceiptToPdf({
    required Booking booking,
    required String templateName,
    String receiptType = 'booking',
    double? paidAmount,
    Map<String, dynamic>? customData,
  }) async {
    Map<String, dynamic> finalCustomData = await CompanySettingsService.getCompanySettings();
    if (customData != null && customData.isNotEmpty) {
      finalCustomData.addAll(customData);
    }
    final htmlTemplate = await _loadTemplate(templateName);
    final htmlContent = await _replacePlaceholders(
      htmlTemplate,
      booking,
      receiptType: receiptType,
      paidAmount: paidAmount,
      customData: finalCustomData,
    );
    // Use A4 format for PDF
    return await Printing.convertHtml(
      format: PdfPageFormat.a4,
      html: htmlContent,
    );
  }
  static const String _templatesDir = 'html_templates';
  static const String _receiptsDir = 'receipts_html';

  /// Print HTML receipt directly without saving file
  static Future<void> printHtmlReceiptDirect({
    required Booking booking,
    required String templateName,
    String receiptType = 'booking',
    double? paidAmount,
    Map<String, dynamic>? customData,
  }) async {
    // Always load company settings first
    Map<String, dynamic> finalCustomData = await CompanySettingsService.getCompanySettings();
    
    // Override with customData if provided
    if (customData != null && customData.isNotEmpty) {
      finalCustomData.addAll(customData);
    }
    
    final htmlTemplate = await _loadTemplate(templateName);
    final htmlContent = await _replacePlaceholders(
      htmlTemplate,
      booking,
      receiptType: receiptType,
      paidAmount: paidAmount,
      customData: finalCustomData,
    );
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async {
        return await Printing.convertHtml(
          format: format,
          html: htmlContent,
        );
      },
    );
  }

  /// Generate HTML receipt from booking data using index.html and styles.css
  static Future<String> generateHtmlReceipt({
    required Booking booking,
    required String templateName,
    String receiptType = 'booking',
    double? paidAmount,
    Map<String, dynamic>? customData,
  }) async {
    try {
      // Always load company settings first
      Map<String, dynamic> finalCustomData = await CompanySettingsService.getCompanySettings();
      
      // Override with customData if provided
      if (customData != null && customData.isNotEmpty) {
        finalCustomData.addAll(customData);
      }
      
      final htmlTemplate = await _loadTemplate(templateName);
      final htmlContent = await _replacePlaceholders(
        htmlTemplate,
        booking,
        receiptType: receiptType,
        paidAmount: paidAmount,
        customData: finalCustomData,
      );
      final htmlFilePath = await _saveHtmlFile(htmlContent, booking, receiptType);
      return htmlFilePath;
    } catch (e) {
      print('Error generating HTML receipt: $e');
      rethrow;
    }
  }

  static Future<String> _loadTemplate(String templateName) async {
    try {
      final assetPath = 'assets/templates/$templateName/index.html';
      final htmlContent = await rootBundle.loadString(assetPath);
      final cssPath = 'assets/templates/$templateName/styles.css';
      final cssContent = await rootBundle.loadString(cssPath);
      final updatedHtml = htmlContent.replaceFirst(
        '<link rel="stylesheet" href="styles.css">',
        '<style>$cssContent</style>',
      );
      return updatedHtml;
    } catch (e) {
      print('Failed to load template from assets: $e');
      try {
        final directory = await getApplicationDocumentsDirectory();
        final templatePath = '${directory.path}/$_templatesDir/$templateName.html';
        final templateFile = File(templatePath);
        if (await templateFile.exists()) {
          return await templateFile.readAsString();
        }
      } catch (e) {
        print('Failed to load custom template: $e');
      }
      return await _getBuiltInTemplate(templateName);
    }
  }

  static Future<String> _getBuiltInTemplate(String templateName) async {
    return _getBasicTemplate();
  }

  static Future<String> _replacePlaceholders(
    String htmlTemplate,
    Booking booking, {
    String receiptType = 'booking',
    double? paidAmount,
    Map<String, dynamic>? customData,
  }) async {
    final now = DateTime.now();
    final dateFormat = DateFormat('yyyy/MM/dd', 'ar');
    final timeFormat = DateFormat('HH:mm', 'ar');
    
    // Always load company settings from CompanySettingsService first
    Map<String, dynamic> companySettings = await CompanySettingsService.getCompanySettings();
    print('===== HTML RECEIPT SERVICE DEBUG =====');
    print('Company settings loaded from service: $companySettings');
    
    // Override with customData if provided
    if (customData != null && customData.isNotEmpty) {
      companySettings.addAll(customData);
      print('Custom data merged: $companySettings');
    }
    
    final companyName = companySettings['companyName'] ?? 'استوديو الذكريات الجميلة';
    final companyLogo = companySettings['companyLogo'] ?? '📸';
    final companyLogoPath = companySettings['companyLogoPath'];
    final companyAddress = companySettings['companyAddress'] ?? 'شارع الرئيسي، المدينة، العراق';
    final companyPhone = companySettings['companyPhone'] ?? '0555123456';
    final companyEmail = companySettings['companyEmail'] ?? '<EMAIL>';
    final bookedBy = booking.bookedBy ?? customData?['bookedBy'] ?? 'غير محدد';
    
    // Delivery-specific data
    final deliveryMethod = customData?['deliveryMethod'] ?? 'استلام من الاستوديو';
    final deliveryNotes = customData?['deliveryNotes'] ?? '';
    final additionalCost = (customData?['additionalCost'] as num?)?.toDouble() ?? 0.0;
    final paidOnDelivery = (customData?['paidOnDelivery'] as num?)?.toDouble() ?? 0.0;
    
    // Debug: طباعة القيم المستخرجة
    print('Company name extracted: "$companyName"');
    print('Company address extracted: "$companyAddress"');
    if (receiptType == 'delivery') {
      print('Delivery method: "$deliveryMethod"');
      print('Additional cost: $additionalCost');
      print('Paid on delivery: $paidOnDelivery');
    }
    print('==========================================');
    
    // Generate logo HTML based on whether we have an image path or not
    final logoHtml = await _generateLogoHtml(companyLogoPath, companyLogo);
    
    final totalAmount = booking.totalAmount;
    final deposit = booking.deposit;
    
    // Calculate amounts based on receipt type
    double finalAmount, remainingAmount, totalPaid;
    if (receiptType == 'delivery') {
      finalAmount = totalAmount + additionalCost;
      totalPaid = deposit + paidOnDelivery;
      remainingAmount = finalAmount - totalPaid;
    } else {
      finalAmount = totalAmount;
      remainingAmount = totalAmount - deposit;
      totalPaid = deposit;
    }
    
    final receiptNumber = receiptType == 'delivery' 
        ? await booking.formattedReceiptNumber // استخدام رقم الحجز الأصلي
        : await booking.formattedReceiptNumber; // استخدام رقم الحجز الأصلي
    
    final receiptTitle = receiptType == 'delivery' 
        ? 'وصل تسليم' 
        : receiptType == 'payment' 
            ? 'وصل دفع' 
            : 'وصل حجز';
    
    final totalLabel = receiptType == 'delivery' 
        ? 'المبلغ النهائي'
        : receiptType == 'payment' 
            ? 'المبلغ المدفوع' 
            : 'إجمالي المبلغ';
  final statusInfoRaw = _getStatusInfo(booking.status);
  final statusInfo = statusInfoRaw.map((k, v) => MapEntry(k, v.toString()));
    
    // Format offers and additions based on receipt type
    final selectedOffers = receiptType == 'delivery' 
        ? _formatDeliveryOffers(booking.selectedOffers ?? [], additionalCost)
        : _formatListAsTable(booking.selectedOffers ?? []);
    
    final selectedOffersText = (booking.selectedOffers ?? []).join(', ');
    final additions = _formatListAsTable(booking.additions ?? []);
    final additionsText = (booking.additions ?? []).join(', ');
    final replacements = <String, String>{
      '{{COMPANY_NAME}}': companyName,
      '{{COMPANY_LOGO}}': logoHtml,
      '{{COMPANY_ADDRESS}}': companyAddress,
      '{{COMPANY_PHONE}}': companyPhone,
      '{{COMPANY_EMAIL}}': companyEmail,
      '{{BOOKED_BY}}': bookedBy,
      '{{DELIVERED_BY}}': bookedBy, // For delivery receipts
      '{{RECEIPT_TITLE}}': receiptTitle,
      '{{RECEIPT_NUMBER}}': receiptNumber,
      '{{PRINT_DATE}}': dateFormat.format(now),
      '{{PRINT_TIME}}': timeFormat.format(now),
      '{{DELIVERY_DATE}}': dateFormat.format(now), // For delivery receipts
      '{{DELIVERY_TIME}}': timeFormat.format(now), // For delivery receipts
      '{{DELIVERY_METHOD}}': deliveryMethod,
      '{{DELIVERY_NOTES}}': deliveryNotes,
      '{{CUSTOMER_NAME}}': booking.customerName,
      '{{CUSTOMER_PHONE}}': booking.customerPhone,
      '{{SERVICE_TYPE}}': booking.serviceType,
      '{{HALL_NAME}}': booking.hallName ?? '',
      '{{BOOKING_DATE}}': dateFormat.format(booking.eventDate ?? booking.bookingDate),
      '{{ORIGINAL_DELIVERY_DATE}}': dateFormat.format(booking.deliveryDate),
      '{{EVENT_DESCRIPTION}}': booking.eventDescription ?? '',
      '{{STATUS_TEXT}}': statusInfo['text']!,
      '{{STATUS_CLASS}}': statusInfo['class']!,
      '{{STATUS_BADGE}}': '<span class="status-badge ${statusInfo['class']}">${statusInfo['text']}</span>',
      '{{SELECTED_OFFERS}}': selectedOffers,
      '{{SELECTED_OFFERS_TEXT}}': selectedOffersText,
      '{{ADDITIONS}}': additions,
      '{{ADDITIONS_TEXT}}': additionsText,
      '{{TOTAL_AMOUNT}}': totalAmount.toStringAsFixed(2),
      '{{ORIGINAL_AMOUNT}}': totalAmount.toStringAsFixed(2), // For delivery receipts
      '{{ADDITIONAL_COST}}': additionalCost.toStringAsFixed(2),
      '{{FINAL_AMOUNT}}': finalAmount.toStringAsFixed(2),
      '{{TOTAL_LABEL}}': totalLabel,
      '{{DEPOSIT}}': deposit.toStringAsFixed(2),
      '{{DEPOSIT_PAID}}': deposit.toStringAsFixed(2), // For delivery receipts
      '{{PAID_ON_DELIVERY}}': paidOnDelivery.toStringAsFixed(2),
      '{{TOTAL_PAID}}': totalPaid.toStringAsFixed(2),
      '{{REMAINING_AMOUNT}}': remainingAmount.toStringAsFixed(2),
      '{{NOTES}}': booking.notes ?? '',
      '{{BOOKING_NOTES}}': booking.notes ?? '', // For delivery receipts
    };
    String result = htmlTemplate;
    replacements.forEach((placeholder, value) {
      result = result.replaceAll(placeholder, value);
    });
    result = _handleConditionalBlocks(result, {
      'DEPOSIT': deposit > 0,
      'NOTES': (booking.notes ?? '').isNotEmpty,
      'EVENT_DESCRIPTION': (booking.eventDescription ?? '').isNotEmpty,
      'HALL_NAME': (booking.hallName ?? '').isNotEmpty,
      'SELECTED_OFFERS': (booking.selectedOffers ?? []).isNotEmpty,
      'ADDITIONS': (booking.additions ?? []).isNotEmpty,
    });
    return result;
  }

  static String _handleConditionalBlocks(String html, Map<String, bool> conditions) {
    conditions.forEach((condition, show) {
      final pattern = RegExp(
        r'\{\{#if\s+' + condition + r'\}\}(.*?)\{\{/if\}\}',
        multiLine: true,
        dotAll: true,
      );
      if (show) {
        html = html.replaceAllMapped(pattern, (match) => match.group(1) ?? '');
      } else {
        html = html.replaceAll(pattern, '');
      }
    });
    return html;
  }

  /// Format delivery offers with additional costs
  static String _formatDeliveryOffers(List<String> items, double additionalCost) {
    if (items.isEmpty && additionalCost == 0) return '';
    
    double total = 0;
    final tableRows = <String>[];
    
    // Add original items
    for (int i = 0; i < items.length; i++) {
      final index = i + 1;
      final item = items[i];
      
      String serviceName = item;
      String description = '';
      String price = '-';
      
      // Extract price using enhanced pattern - New format: "service name - price د.ع"
      final newFormatPattern = RegExp(r'^(.+?)\s*-\s*(\d+(?:\.\d+)?)\s*(د\.ع)\s*$');
      final newFormatMatch = newFormatPattern.firstMatch(item);
      
      if (newFormatMatch != null) {
        serviceName = newFormatMatch.group(1)?.trim() ?? item;
        final priceValue = newFormatMatch.group(2)?.trim() ?? '0';
        final currency = newFormatMatch.group(3)?.trim() ?? 'د.ع';
        price = '$priceValue $currency';
        
        final priceNum = double.tryParse(priceValue);
        if (priceNum != null) {
          total += priceNum;
        }
      } else {
        // Try old format
        final parts = item.split(' - ');
        
        if (parts.length >= 2) {
          serviceName = parts[0].trim();
          
          final lastPart = parts.last.trim();
          final pricePattern = RegExp(r'^(\d+(?:\.\d+)?)\s*(د\.ع|ريال|ر\.س|دينار)?\s*$');
          final priceMatch = pricePattern.firstMatch(lastPart);
          
          if (priceMatch != null) {
            final priceValue = priceMatch.group(1)?.trim() ?? '0';
            final currency = priceMatch.group(2)?.trim() ?? 'د.ع';
            price = '$priceValue $currency';
            
            if (parts.length > 2) {
              description = parts.sublist(1, parts.length - 1).join(' - ').trim();
            }
            
            final priceNum = double.tryParse(priceValue);
            if (priceNum != null) {
              total += priceNum;
            }
          } else {
            description = parts.sublist(1).join(' - ').trim();
          }
        }
      }
      
      if (price != '-' && !price.contains('د.ع') && !price.contains('ريال') && !price.contains('ر.س')) {
        price = '$price د.ع';
      }
      
      tableRows.add('''
                        <tr>
                            <td style="text-align:right">$index</td>
                            <td style="text-align:right">$serviceName</td>
                            <td style="text-align:left; font-weight:bold; color:#28a745;">$price</td>
                        </tr>''');
    }
    
    // Add additional cost if present
    if (additionalCost > 0) {
      final index = items.length + 1;
      total += additionalCost;
      tableRows.add('''
                        <tr style="background-color: #fff3cd;">
                            <td style="text-align:right">$index</td>
                            <td style="text-align:right">رسوم إضافية (توصيل/أخرى)</td>
                            <td style="text-align:left; font-weight:bold; color:#856404;">${additionalCost.toStringAsFixed(0)} د.ع</td>
                        </tr>''');
    }
    
    final totalRow = '''
                        <tr class="total-row">
                            <td colspan="2" style="text-align:center; font-weight:bold;">إجمالي مبلغ التسليم</td>
                            <td style="text-align:left; font-weight:bold; color:#28a745; font-size:14px;">${total.toStringAsFixed(0)} د.ع</td>
                        </tr>''';
    
    return '${tableRows.join('\n')}\n$totalRow';
  }

  /// Format list items as table rows with description and total
  static String _formatListAsTable(List<String> items) {
    if (items.isEmpty) return '';
    
    double total = 0;
    final tableRows = items.asMap().entries.map((entry) {
      final index = entry.key + 1;
      final item = entry.value;
      
      // Debug: طباعة النص الأصلي للمراجعة
      print('Processing item: "$item"');
      
      String serviceName = item;
      String description = '';
      String price = '-';
      
      // نمط محسن لاستخراج السعر - التنسيق الجديد: "اسم الخدمة - رقم د.ع"
      final newFormatPattern = RegExp(r'^(.+?)\s*-\s*(\d+(?:\.\d+)?)\s*(د\.ع)\s*$');
      final newFormatMatch = newFormatPattern.firstMatch(item);
      
      if (newFormatMatch != null) {
        // التنسيق الجديد: "اسم الخدمة - 250000 د.ع"
        serviceName = newFormatMatch.group(1)?.trim() ?? item;
        final priceValue = newFormatMatch.group(2)?.trim() ?? '0';
        final currency = newFormatMatch.group(3)?.trim() ?? 'د.ع';
        price = '$priceValue $currency';
        
        print('New format detected - Service: "$serviceName", Price: "$price"');
        
        // حساب الإجمالي
        final priceNum = double.tryParse(priceValue);
        if (priceNum != null) {
          total += priceNum;
        }
      } else {
        // محاولة التنسيق القديم أو أنماط أخرى
        final parts = item.split(' - ');
        
        if (parts.length >= 2) {
          serviceName = parts[0].trim();
          
          // فحص الجزء الأخير للسعر
          final lastPart = parts.last.trim();
          final pricePattern = RegExp(r'^(\d+(?:\.\d+)?)\s*(د\.ع|ريال|ر\.س|دينار)?\s*$');
          final priceMatch = pricePattern.firstMatch(lastPart);
          
          if (priceMatch != null) {
            // وجد سعر في الجزء الأخير
            final priceValue = priceMatch.group(1)?.trim() ?? '0';
            final currency = priceMatch.group(2)?.trim() ?? 'د.ع';
            price = '$priceValue $currency';
            
            // الوصف هو كل شيء بين الاسم والسعر
            if (parts.length > 2) {
              description = parts.sublist(1, parts.length - 1).join(' - ').trim();
            }
            
            print('Price found in last part - Service: "$serviceName", Description: "$description", Price: "$price"');
            
            // حساب الإجمالي
            final priceNum = double.tryParse(priceValue);
            if (priceNum != null) {
              total += priceNum;
            }
          } else {
            // لا يوجد سعر في الجزء الأخير - اعتبر كل شيء بعد الاسم وصف
            description = parts.sublist(1).join(' - ').trim();
            print('No price found - Service: "$serviceName", Description: "$description"');
          }
        } else {
          // جزء واحد فقط - اعتبره اسم الخدمة فقط
          serviceName = item.trim();
          print('Single part - Service: "$serviceName"');
        }
      }
      
      // تأكد من وجود وحدة العملة في السعر
      if (price != '-' && !price.contains('د.ع') && !price.contains('ريال') && !price.contains('ر.س')) {
        price = '$price د.ع';
      }
      
      return '''
                        <tr>
                            <td style="text-align:right">$index</td>
                            <td style="text-align:right">$serviceName</td>
                            <td style="text-align:left; font-weight:bold; color:#007bff;">$price</td>
                        </tr>''';
    }).join('\n');
    
    final totalRow = '''
                        <tr class="total-row">
                            <td colspan="2" style="text-align:center; font-weight:bold;">الإجمالي</td>
                            <td style="text-align:left; font-weight:bold; color:#007bff; font-size:14px;">${total.toStringAsFixed(0)} د.ع</td>
                        </tr>''';
    
    print('Total calculated: ${total.toStringAsFixed(0)} د.ع');
    return '$tableRows\n$totalRow';
  }

  static Map<String, String> _getStatusInfo(BookingStatus status) {
    switch (status) {
      case BookingStatus.inProgress:
        return {'text': 'قيد العمل', 'class': 'status-in-progress'};
      case BookingStatus.completed:
        return {'text': 'تم التسليم', 'class': 'status-completed'};
      case BookingStatus.cancelled:
        return {'text': 'ملغي', 'class': 'status-cancelled'};
    }
  }

  /// Generate logo HTML based on image path or fallback text/emoji
  static Future<String> _generateLogoHtml(String? imagePath, String fallbackLogo) async {
    if (imagePath != null && imagePath.isNotEmpty) {
      // Check if it's a web URL
      if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
        return '<img src="$imagePath" alt="شعار الشركة" onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'block\';" /><span class="company-logo-text" style="display:none;">$fallbackLogo</span>';
      } else {
        // For local file paths, try to convert to base64
        final base64Image = await _imageToBase64(imagePath);
        if (base64Image != null) {
          return '<img src="$base64Image" alt="شعار الشركة" />';
        } else {
          // Fallback to file:// protocol if base64 conversion fails
          return '<img src="file:///$imagePath" alt="شعار الشركة" onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'block\';" /><span class="company-logo-text" style="display:none;">$fallbackLogo</span>';
        }
      }
    } else {
      // Use fallback emoji/text
      return '<span class="company-logo-text">$fallbackLogo</span>';
    }
  }

  /// Convert image file to base64 for embedding in HTML (optional method)
  static Future<String?> _imageToBase64(String imagePath) async {
    try {
      final file = File(imagePath);
      if (await file.exists()) {
        final bytes = await file.readAsBytes();
        final base64String = base64Encode(bytes);
        final extension = imagePath.split('.').last.toLowerCase();
        String mimeType = 'image/png';
        
        switch (extension) {
          case 'jpg':
          case 'jpeg':
            mimeType = 'image/jpeg';
            break;
          case 'png':
            mimeType = 'image/png';
            break;
          case 'gif':
            mimeType = 'image/gif';
            break;
          case 'svg':
            mimeType = 'image/svg+xml';
            break;
        }
        
        return 'data:$mimeType;base64,$base64String';
      }
    } catch (e) {
      print('Error converting image to base64: $e');
    }
    return null;
  }

  static Future<String> _saveHtmlFile(
    String htmlContent,
    Booking booking,
    String receiptType,
  ) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final receiptsPath = '${directory.path}/$_receiptsDir';
      final receiptsDir = Directory(receiptsPath);
      if (!await receiptsDir.exists()) {
        await receiptsDir.create(recursive: true);
      }
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final customerName = booking.customerName.replaceAll(' ', '_');
      final filename = '${receiptType}_${customerName}_$timestamp.html';
      final filePath = '$receiptsPath/$filename';
      final file = File(filePath);
      await file.writeAsString(htmlContent, encoding: utf8);
      return filePath;
    } catch (e) {
      print('Error saving HTML file: $e');
      rethrow;
    }
  }

  static Future<void> saveCustomTemplate(String templateName, String htmlContent) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final templatesPath = '${directory.path}/$_templatesDir';
      final templatesDir = Directory(templatesPath);
      if (!await templatesDir.exists()) {
        await templatesDir.create(recursive: true);
      }
      final templatePath = '$templatesPath/$templateName.html';
      final file = File(templatePath);
      await file.writeAsString(htmlContent, encoding: utf8);
      print('Custom template saved: $templatePath');
    } catch (e) {
      print('Error saving custom template: $e');
      rethrow;
    }
  }

  static Future<List<String>> getAvailableTemplates() async {
    final templates = <String>['modern', 'classic', 'minimal', 'elegant'];
    try {
      final directory = await getApplicationDocumentsDirectory();
      final templatesPath = '${directory.path}/$_templatesDir';
      final templatesDir = Directory(templatesPath);
      if (await templatesDir.exists()) {
        final files = await templatesDir.list().toList();
        for (final file in files) {
          if (file is File && file.path.endsWith('.html')) {
            final name = file.path.split('/').last.replaceAll('.html', '');
            if (!templates.contains(name)) {
              templates.add(name);
            }
          }
        }
      }
    } catch (e) {
      print('Error loading custom templates: $e');
    }
    return templates;
  }

  static Future<void> cleanupOldReceipts({int daysToKeep = 30}) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final receiptsPath = '${directory.path}/$_receiptsDir';
      final receiptsDir = Directory(receiptsPath);
      if (!await receiptsDir.exists()) return;
      final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));
      final files = await receiptsDir.list().toList();
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          if (stat.modified.isBefore(cutoffDate)) {
            await file.delete();
            print('Deleted old receipt: ${file.path}');
          }
        }
      }
    } catch (e) {
      print('Error cleaning up old receipts: $e');
    }
  }

  static Future<Map<String, int>> getReceiptStats() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final receiptsPath = '${directory.path}/$_receiptsDir';
      final receiptsDir = Directory(receiptsPath);
      if (!await receiptsDir.exists()) {
        return {'total': 0, 'thisMonth': 0, 'thisWeek': 0};
      }
      final files = await receiptsDir.list().toList();
      final now = DateTime.now();
      final thisMonth = DateTime(now.year, now.month);
      final thisWeek = now.subtract(Duration(days: now.weekday - 1));
      int total = 0;
      int thisMonthCount = 0;
      int thisWeekCount = 0;
      for (final file in files) {
        if (file is File && file.path.endsWith('.html')) {
          total++;
          final stat = await file.stat();
          if (stat.modified.isAfter(thisMonth)) {
            thisMonthCount++;
          }
          if (stat.modified.isAfter(thisWeek)) {
            thisWeekCount++;
          }
        }
      }
      return {
        'total': total,
        'thisMonth': thisMonthCount,
        'thisWeek': thisWeekCount,
      };
    } catch (e) {
      print('Error getting receipt stats: $e');
      return {'total': 0, 'thisMonth': 0, 'thisWeek': 0};
    }
  }

  static String _getBasicTemplate() {
    return '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{RECEIPT_TITLE}}</title>
    <style>
        @page {
            size: A4;
            margin: 15mm;
        }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background: white; direction: rtl; font-size: 12px; line-height: 1.4; color: #333; }
        .receipt { width: 100%; max-width: 210mm; min-height: 297mm; margin: 0 auto; background: white; padding: 10mm; box-sizing: border-box; position: relative; }
        .header { text-align: center; margin-bottom: 20px; border-bottom: 2px solid #007bff; padding-bottom: 15px; position: relative; min-height: 80px; }
        .company-logo { position: absolute; left: 0; top: 0; margin-bottom: 10px; }
        .company-logo img { max-width: 80px; max-height: 80px; width: auto; height: auto; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .company-logo-text { font-size: 48px; }
        .company-name { position: absolute; right: 0; top: 0; font-size: 16px; color: #007bff; font-weight: bold; max-width: 200px; text-align: right; }
        .company-address { font-size: 12px; color: #666; margin-bottom: 8px; line-height: 1.4; }
        .receipt-title { font-size: 18px; color: #007bff; margin-bottom: 8px; font-weight: 600; margin-top: 15px; }
        .receipt-number { font-size: 14px; color: #666; background: #f8f9fa; padding: 5px 15px; border-radius: 15px; display: inline-block; margin-top: 5px; }
        .main-content { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px; }
        .section { background: #f9f9f9; padding: 12px; border-radius: 6px; border-left: 4px solid #007bff; }
        .section-title { font-size: 14px; font-weight: bold; color: #007bff; margin-bottom: 8px; border-bottom: 1px solid #ddd; padding-bottom: 4px; }
        .info-row { display: flex; justify-content: space-between; padding: 6px 0; border-bottom: 1px solid #eee; font-size: 11px; }
        .info-row:last-child { border-bottom: none; }
        .label { font-weight: 600; color: #444; min-width: 80px; }
        .value { color: #666; text-align: left; flex: 1; }
        .services-section { grid-column: 1 / -1; margin: 15px 0; }
        .services-table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 11px; }
        .services-table th, .services-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        .services-table th { background: #007bff; color: white; font-weight: bold; font-size: 12px; }
        .services-table th:nth-child(3), .services-table td:nth-child(3) { text-align: left; font-weight: bold; color: #007bff; min-width: 100px; background-color: #f8f9fa; }
        .services-table tr:nth-child(even) { background: #f9f9f9; }
        .services-table tr:hover { background: #f5f5f5; }
        .services-table .total-row { font-weight: bold; background: #e9ecef !important; }
        .services-table .total-row td { font-size: 13px; color: #007bff; }
        .services-table .total-row td:nth-child(3) { background-color: #007bff; color: white !important; }
        .total-section { background: linear-gradient(135deg, #007bff, #0056b3); color: white; padding: 15px; margin: 20px 0; text-align: center; border-radius: 8px; box-shadow: 0 3px 6px rgba(0,123,255,0.3); }
        .total-label { font-size: 13px; margin-bottom: 8px; opacity: 0.9; }
        .total-amount { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
        .financial-details { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; font-size: 11px; margin-top: 10px; }
        .financial-item { text-align: center; padding: 5px; background: rgba(255,255,255,0.1); border-radius: 4px; }
        .status-badge { display: inline-block; padding: 4px 12px; border-radius: 12px; font-size: 10px; font-weight: bold; text-transform: uppercase; }
        .status-in-progress { background: #fff3cd; color: #856404; }
        .status-completed { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        .notes-section { background: #fffbf0; border: 1px solid #ffeaa7; border-radius: 6px; padding: 12px; margin: 15px 0; }
        .notes-title { font-weight: bold; color: #856404; margin-bottom: 6px; font-size: 12px; }
        .notes-content { color: #856404; font-size: 11px; line-height: 1.5; }
        .footer { text-align: center; margin-top: auto; padding-top: 20px; border-top: 1px solid #eee; color: #888; font-size: 10px; }
        .footer-line { margin: 3px 0; }
        .thank-you { font-weight: bold; color: #007bff; margin: 8px 0; font-size: 12px; }
        @media print { body { background: white !important; -webkit-print-color-adjust: exact; print-color-adjust: exact; } .receipt { box-shadow: none !important; min-height: auto; } @page { margin: 10mm; } }
        @media screen and (max-width: 768px) { .main-content { grid-template-columns: 1fr; } .financial-details { grid-template-columns: 1fr; } }
    </style>
</head>
<body>
    <div class="receipt">
        <div class="header">
            <div class="company-logo">{{COMPANY_LOGO}}</div>
            <div class="company-name">{{COMPANY_NAME}}</div>
            <div class="company-address">{{COMPANY_ADDRESS}}</div>
            <h2 class="receipt-title">{{RECEIPT_TITLE}}</h2>
            <span class="receipt-number">رقم الوصل: {{RECEIPT_NUMBER}}</span>
        </div>
        <div class="main-content">
            <div class="section">
                <div class="section-title">معلومات العميل</div>
                <div class="info-row"><span class="label">الاسم:</span><span class="value">{{CUSTOMER_NAME}}</span></div>
                <div class="info-row"><span class="label">الهاتف:</span><span class="value">{{CUSTOMER_PHONE}}</span></div>
                <div class="info-row"><span class="label">محجوز بواسطة:</span><span class="value">{{BOOKED_BY}}</span></div>
            </div>
            <div class="section">
                <div class="section-title">معلومات الخدمة</div>
                <div class="info-row"><span class="label">نوع الخدمة:</span><span class="value">{{SERVICE_TYPE}}</span></div>
                {{#if HALL_NAME}}
                <div class="info-row"><span class="label">اسم القاعة:</span><span class="value">{{HALL_NAME}}</span></div>
                {{/if}}
                <div class="info-row"><span class="label">تاريخ الحجز:</span><span class="value">{{BOOKING_DATE}}</span></div>
                <div class="info-row"><span class="label">تاريخ التسليم:</span><span class="value">{{DELIVERY_DATE}}</span></div>
                <div class="info-row"><span class="label">الحالة:</span><span class="value">{{STATUS_BADGE}}</span></div>
            </div>
            {{#if EVENT_DESCRIPTION}}
            <div class="section" style="grid-column: 1 / -1;">
                <div class="section-title">تفاصيل المناسبة</div>
                <div style="padding: 8px 0; font-size: 11px; line-height: 1.5;">{{EVENT_DESCRIPTION}}</div>
            </div>
            {{/if}}
            {{#if SELECTED_OFFERS}}
            <div class="section services-section">
                <div class="section-title">الخدمات المختارة</div>
                <table class="services-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>اسم الخدمة</th>
                            <th>السعر</th>
                        </tr>
                    </thead>
                    <tbody>
                        {{SELECTED_OFFERS}}
                    </tbody>
                </table>
            </div>
            {{/if}}
        </div>
        <div class="total-section">
            <div class="total-label">{{TOTAL_LABEL}}</div>
            <div class="total-amount">{{TOTAL_AMOUNT}} د.ع</div>
            {{#if DEPOSIT}}
            <div class="financial-details">
                <div class="financial-item"><div>إجمالي المبلغ</div><div><strong>{{TOTAL_AMOUNT}} د.ع</strong></div></div>
                <div class="financial-item"><div>العربون المدفوع</div><div><strong>{{DEPOSIT}} د.ع</strong></div></div>
                <div class="financial-item"><div>المبلغ المتبقي</div><div><strong>{{REMAINING_AMOUNT}} د.ع</strong></div></div>
            </div>
            {{/if}}
        </div>
        {{#if NOTES}}
        <div class="notes-section">
            <div class="notes-title">ملاحظات</div>
            <div class="notes-content">{{NOTES}}</div>
        </div>
        {{/if}}
        <div class="footer"> 
            <div class="footer-line">{{COMPANY_PHONE}}</div>
            <div class="thank-you">شكراً لثقتكم بخدماتنا</div>
            <div class="footer-line">تاريخ الطباعة: {{PRINT_DATE}} في {{PRINT_TIME}} | طُبع بواسطة: {{BOOKED_BY}}</div>
        </div>
    </div>
</body>
</html>''';
  }

  /// Convert HTML to PDF (requires additional dependencies)
  static Future<String?> convertHtmlToPdf(String htmlFilePath) async {
    // This would require a PDF generation library like puppeteer_pdf or similar
    // For now, we'll return null and implement later
    return null;
  }

  /// دالة مساعدة للحصول على اسم المستخدم الحالي
  /// يمكن ربطها مع نظام المصادقة
  static String getCurrentEmployeeName() {
    // يمكن ربط هذه الدالة مع AuthService أو أي نظام مصادقة آخر
    // return AuthService.getCurrentUser()?.name ?? 'غير محدد';
    return 'غير محدد'; // قيمة افتراضية مؤقتة
  }

  /// Helper method to get supported image formats for logo selection
  static List<String> getSupportedImageFormats() {
    return ['png', 'jpg', 'jpeg', 'gif', 'svg'];
  }

  /// Helper method to validate if a file is a supported image format
  static bool isValidImageFile(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();
    return getSupportedImageFormats().contains(extension);
  }

  /// Open HTML file in default browser
  static Future<void> openHtmlFile(String filePath) async {
    final file = File(filePath);
    if (await file.exists()) {
      // On Windows, use the file URL protocol
      if (Platform.isWindows) {
        await Process.run('cmd', ['/c', 'start', 'file:///$filePath']);
      } else if (Platform.isMacOS) {
        await Process.run('open', [filePath]);
      } else if (Platform.isLinux) {
        await Process.run('xdg-open', [filePath]);
      }
    }
  }
}
