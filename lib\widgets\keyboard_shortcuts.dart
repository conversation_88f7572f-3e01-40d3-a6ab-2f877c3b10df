import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Widget للتعامل مع اختصارات لوحة المفاتيح في محرر القوالب
class KeyboardShortcuts extends StatefulWidget {
  final Widget child;
  final VoidCallback? onSave;
  final VoidCallback? onUndo;
  final VoidCallback? onRedo;
  final VoidCallback? onCopy;
  final VoidCallback? onPaste;
  final VoidCallback? onDelete;
  final VoidCallback? onSelectAll;
  final VoidCallback? onZoomIn;
  final VoidCallback? onZoomOut;
  final VoidCallback? onResetZoom;

  const KeyboardShortcuts({
    super.key,
    required this.child,
    this.onSave,
    this.onUndo,
    this.onRedo,
    this.onCopy,
    this.onPaste,
    this.onDelete,
    this.onSelectAll,
    this.onZoomIn,
    this.onZoomOut,
    this.onResetZoom,
  });

  @override
  State<KeyboardShortcuts> createState() => _KeyboardShortcutsState();
}

class _KeyboardShortcutsState extends State<KeyboardShortcuts> {
  final FocusNode _focusNode = FocusNode();

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _focusNode,
      autofocus: true,
      onKeyEvent: _handleKeyEvent,
      child: widget.child,
    );
  }

  KeyEventResult _handleKeyEvent(FocusNode node, KeyEvent event) {
    if (event is! KeyDownEvent) return KeyEventResult.ignored;

    final isControlPressed = HardwareKeyboard.instance.isControlPressed;
    final isShiftPressed = HardwareKeyboard.instance.isShiftPressed;
    final key = event.logicalKey;

    // Ctrl + S - حفظ
    if (isControlPressed && key == LogicalKeyboardKey.keyS) {
      widget.onSave?.call();
      return KeyEventResult.handled;
    }

    // Ctrl + Z - تراجع
    if (isControlPressed && !isShiftPressed && key == LogicalKeyboardKey.keyZ) {
      widget.onUndo?.call();
      return KeyEventResult.handled;
    }

    // Ctrl + Shift + Z أو Ctrl + Y - إعادة
    if ((isControlPressed && isShiftPressed && key == LogicalKeyboardKey.keyZ) ||
        (isControlPressed && key == LogicalKeyboardKey.keyY)) {
      widget.onRedo?.call();
      return KeyEventResult.handled;
    }

    // Ctrl + C - نسخ
    if (isControlPressed && key == LogicalKeyboardKey.keyC) {
      widget.onCopy?.call();
      return KeyEventResult.handled;
    }

    // Ctrl + V - لصق
    if (isControlPressed && key == LogicalKeyboardKey.keyV) {
      widget.onPaste?.call();
      return KeyEventResult.handled;
    }

    // Delete - حذف
    if (key == LogicalKeyboardKey.delete) {
      widget.onDelete?.call();
      return KeyEventResult.handled;
    }

    // Ctrl + A - تحديد الكل
    if (isControlPressed && key == LogicalKeyboardKey.keyA) {
      widget.onSelectAll?.call();
      return KeyEventResult.handled;
    }

    // Ctrl + Plus - تكبير
    if (isControlPressed && (key == LogicalKeyboardKey.equal || key == LogicalKeyboardKey.numpadAdd)) {
      widget.onZoomIn?.call();
      return KeyEventResult.handled;
    }

    // Ctrl + Minus - تصغير
    if (isControlPressed && (key == LogicalKeyboardKey.minus || key == LogicalKeyboardKey.numpadSubtract)) {
      widget.onZoomOut?.call();
      return KeyEventResult.handled;
    }

    // Ctrl + 0 - إعادة تعيين التكبير
    if (isControlPressed && (key == LogicalKeyboardKey.digit0 || key == LogicalKeyboardKey.numpad0)) {
      widget.onResetZoom?.call();
      return KeyEventResult.handled;
    }

    return KeyEventResult.ignored;
  }
}

/// دالة لإظهار مساعدة الاختصارات
void showShortcutsHelp(BuildContext context) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('اختصارات لوحة المفاتيح'),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: const [
            _ShortcutItem(
              shortcut: 'Ctrl + S',
              description: 'حفظ القالب',
              icon: Icons.save,
            ),
            _ShortcutItem(
              shortcut: 'Ctrl + Z',
              description: 'تراجع عن آخر إجراء',
              icon: Icons.undo,
            ),
            _ShortcutItem(
              shortcut: 'Ctrl + Y / Ctrl + Shift + Z',
              description: 'إعادة آخر إجراء',
              icon: Icons.redo,
            ),
            _ShortcutItem(
              shortcut: 'Ctrl + C',
              description: 'نسخ العنصر المحدد',
              icon: Icons.content_copy,
            ),
            _ShortcutItem(
              shortcut: 'Ctrl + V',
              description: 'لصق العنصر',
              icon: Icons.content_paste,
            ),
            _ShortcutItem(
              shortcut: 'Delete',
              description: 'حذف العنصر المحدد',
              icon: Icons.delete,
            ),
            _ShortcutItem(
              shortcut: 'Ctrl + A',
              description: 'تحديد جميع العناصر',
              icon: Icons.select_all,
            ),
            _ShortcutItem(
              shortcut: 'Ctrl + Plus',
              description: 'تكبير العرض',
              icon: Icons.zoom_in,
            ),
            _ShortcutItem(
              shortcut: 'Ctrl + Minus',
              description: 'تصغير العرض',
              icon: Icons.zoom_out,
            ),
            _ShortcutItem(
              shortcut: 'Ctrl + 0',
              description: 'إعادة تعيين التكبير',
              icon: Icons.zoom_out_map,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إغلاق'),
        ),
      ],
    ),
  );
}

class _ShortcutItem extends StatelessWidget {
  final String shortcut;
  final String description;
  final IconData icon;

  const _ShortcutItem({
    required this.shortcut,
    required this.description,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Theme.of(context).primaryColor),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                shortcut,
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 3,
            child: Text(
              description,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }
}
