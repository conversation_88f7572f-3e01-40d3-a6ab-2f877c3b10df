# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\flutter\\src\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\flutter_projects\\studio_managment" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\flutter\\src\\flutter"
  "PROJECT_DIR=D:\\flutter_projects\\studio_managment"
  "FLUTTER_ROOT=C:\\flutter\\src\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\flutter_projects\\studio_managment\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\flutter_projects\\studio_managment"
  "FLUTTER_TARGET=D:\\flutter_projects\\studio_managment\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzUuMC0wLjMucHJl,RkxVVFRFUl9DSEFOTkVMPWJldGE=,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049MjRhNWM3NzU1NA==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049NjU5ZDk1NTNkZg==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My45LjAgKGJ1aWxkIDMuOS4wLTMzMy4yLmJldGEp"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\flutter_projects\\studio_managment\\.dart_tool\\package_config.json"
)
