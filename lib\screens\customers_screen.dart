import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../models/customer.dart';
import '../services/database_helper.dart';
import '../utils/currency_formatter.dart';
import 'customer_details_screen.dart';

class CustomersScreen extends StatefulWidget {
  const CustomersScreen({super.key});

  @override
  State<CustomersScreen> createState() => _CustomersScreenState();
}

class _CustomersScreenState extends State<CustomersScreen> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final TextEditingController _searchController = TextEditingController();
  List<Customer> _customers = [];
  List<Customer> _filteredCustomers = [];
  Map<String, double> _customerDebts = {}; // خريطة لتخزين ديون العملاء
  bool _isLoading = true;
  bool _showOnlyDebtors = false; // فلتر لعرض المدينين فقط

  @override
  void initState() {
    super.initState();
    _loadCustomers();
    _searchController.addListener(_filterCustomers);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadCustomers() async {
    setState(() => _isLoading = true);
    try {
      final customers = await _databaseHelper.getAllCustomers();
      final allBookings = await _databaseHelper.getAllBookings();
      
      // حساب ديون كل عميل
      Map<String, double> debts = {};
      for (final customer in customers) {
        double totalDebt = 0.0;
        final customerBookings = allBookings.where((booking) => 
          booking.customerName == customer.name).toList();
        
        for (final booking in customerBookings) {
          double remainingAmount = booking.totalAmount - booking.paidAmount;
          if (remainingAmount > 0) {
            totalDebt += remainingAmount;
          }
        }
        debts[customer.name] = totalDebt;
      }
      
      setState(() {
        _customers = customers;
        _filteredCustomers = customers;
        _customerDebts = debts;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل العملاء: $e');
    }
  }

  void _filterCustomers() {
    final query = _searchController.text.toLowerCase().trim();
    setState(() {
      List<Customer> baseList = _customers;
      
      // تطبيق فلتر المدينين أولاً
      if (_showOnlyDebtors) {
        baseList = _customers.where((customer) {
          final debt = _customerDebts[customer.name] ?? 0.0;
          return debt > 0;
        }).toList();
      }
      
      // تطبيق فلتر البحث
      if (query.isEmpty) {
        _filteredCustomers = baseList;
      } else {
        _filteredCustomers = baseList.where((customer) {
          return customer.name.toLowerCase().contains(query) ||
                 customer.phone.contains(query);
        }).toList();
      }
    });
  }

  void _toggleDebtorFilter() {
    setState(() {
      _showOnlyDebtors = !_showOnlyDebtors;
    });
    _filterCustomers();
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Future<void> _showAddCustomerDialog() async {
    final result = await showDialog<Customer>(
      context: context,
      builder: (context) => const AddCustomerDialog(),
    );

    if (result != null) {
      try {
        await _databaseHelper.insertCustomer(result);
        _loadCustomers();
        _showSuccessSnackBar('تم إضافة العميل بنجاح');
      } catch (e) {
        _showErrorSnackBar('خطأ في إضافة العميل: $e');
        // If it's a database schema error, offer to reset the database
        if (e.toString().contains('NOT NULL constraint failed') || 
            e.toString().contains('constraint failed')) {
          _showDatabaseResetDialog();
        }
      }
    }
  }

  Future<void> _showDatabaseResetDialog() async {
    final shouldReset = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ في قاعدة البيانات'),
        content: const Text(
          'يبدو أن هناك مشكلة في تركيب قاعدة البيانات. هل تريد إعادة تعيين قاعدة البيانات؟\n\n'
          'تحذير: سيتم حذف جميع البيانات الموجودة.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );

    if (shouldReset == true) {
      try {
        await _databaseHelper.resetDatabase();
        _loadCustomers();
        _showSuccessSnackBar('تم إعادة تعيين قاعدة البيانات بنجاح');
      } catch (e) {
        _showErrorSnackBar('خطأ في إعادة تعيين قاعدة البيانات: $e');
      }
    }
  }

  Future<void> _showEditCustomerDialog(Customer customer) async {
    final result = await showDialog<Customer>(
      context: context,
      builder: (context) => AddCustomerDialog(customer: customer),
    );

    if (result != null) {
      try {
        await _databaseHelper.updateCustomer(result.copyWith(updatedAt: DateTime.now()));
        _loadCustomers();
        _showSuccessSnackBar('تم تحديث بيانات العميل بنجاح');
      } catch (e) {
        _showErrorSnackBar('خطأ في تحديث العميل: $e');
      }
    }
  }

  Future<void> _deleteCustomer(Customer customer) async {
    final shouldDelete = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف العميل'),
        content: Text('هل أنت متأكد من حذف العميل "${customer.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (shouldDelete == true) {
      try {
        await _databaseHelper.deleteCustomer(customer.id);
        _loadCustomers();
        _showSuccessSnackBar('تم حذف العميل بنجاح');
      } catch (e) {
        _showErrorSnackBar('خطأ في حذف العميل: $e');
      }
    }
  }

  void _showCustomerDetails(Customer customer) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CustomerDetailsScreen(customer: customer),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text('إدارة العملاء'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: Color(0xFF2196F3), // لون أزرق واضح
          ),
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'العودة للقائمة الرئيسية',
        ),
        actions: [
          IconButton(
            onPressed: _loadCustomers,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'reset_db') {
                _showDatabaseResetDialog();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'reset_db',
                child: Row(
                  children: [
                    Icon(Icons.refresh, size: 18, color: Colors.red),
                    SizedBox(width: 8),
                    Text('إعادة تعيين قاعدة البيانات', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar with Filter
          Container(
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                TextField(
                  controller: _searchController,
                  textDirection: TextDirection.rtl,
                  decoration: const InputDecoration(
                    hintText: 'البحث عن عميل (الاسم، الهاتف)',
                    hintTextDirection: TextDirection.rtl,
                    prefixIcon: Icon(Icons.search),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.all(16),
                  ),
                ),
                // Debtor filter toggle
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(12),
                      bottomRight: Radius.circular(12),
                    ),
                  ),
                  child: Row(
                    children: [
                      Switch.adaptive(
                        value: _showOnlyDebtors,
                        onChanged: (value) => _toggleDebtorFilter(),
                        activeColor: Colors.red,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'عرض المدينين فقط',
                        style: TextStyle(
                          color: _showOnlyDebtors ? Colors.red[700] : Colors.grey[600],
                          fontWeight: _showOnlyDebtors ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                      const Spacer(),
                      if (_showOnlyDebtors)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.red[100],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.filter_alt, size: 16, color: Colors.red[700]),
                              const SizedBox(width: 4),
                              Text(
                                'فلتر نشط',
                                style: TextStyle(
                                  color: Colors.red[700],
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Stats Card
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF2196F3), Color(0xFF64B5F6)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF2196F3).withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem('إجمالي العملاء', '${_customers.length}', Icons.people),
                _buildStatItem(
                  _showOnlyDebtors ? 'المدينين' : 'نتائج البحث', 
                  '${_filteredCustomers.length}', 
                  _showOnlyDebtors ? Icons.money_off : Icons.search,
                ),
                _buildStatItem(
                  'إجمالي المدينين', 
                  '${_customerDebts.values.where((debt) => debt > 0).length}', 
                  Icons.account_balance_wallet,
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Customers List
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredCustomers.isEmpty
                    ? _buildEmptyState()
                    : AnimationLimiter(
                        child: ListView.builder(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: _filteredCustomers.length,
                          itemBuilder: (context, index) {
                            return AnimationConfiguration.staggeredList(
                              position: index,
                              duration: const Duration(milliseconds: 375),
                              child: SlideAnimation(
                                verticalOffset: 50.0,
                                child: FadeInAnimation(
                                  child: CustomerCard(
                                    customer: _filteredCustomers[index],
                                    debtAmount: _customerDebts[_filteredCustomers[index].name] ?? 0.0,
                                    onTap: () => _showCustomerDetails(_filteredCustomers[index]),
                                    onEdit: () => _showEditCustomerDialog(_filteredCustomers[index]),
                                    onDelete: () => _deleteCustomer(_filteredCustomers[index]),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddCustomerDialog,
        icon: const Icon(Icons.person_add),
        label: const Text('إضافة عميل'),
        backgroundColor: const Color(0xFF2196F3),
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 28),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          title,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    String title, subtitle;
    IconData icon;
    
    if (_showOnlyDebtors && _searchController.text.isEmpty) {
      // عرض المدينين فقط وشريط البحث فارغ
      title = 'لا يوجد عملاء مدينين';
      subtitle = 'جميع العملاء قاموا بتسديد مستحقاتهم';
      icon = Icons.check_circle_outline;
    } else if (_showOnlyDebtors && _searchController.text.isNotEmpty) {
      // عرض المدينين فقط مع البحث
      title = 'لا توجد نتائج في المدينين';
      subtitle = 'جرب البحث بكلمات مختلفة أو أزل فلتر المدينين';
      icon = Icons.search_off;
    } else if (_searchController.text.isNotEmpty) {
      // البحث العادي
      title = 'لا توجد نتائج للبحث';
      subtitle = 'جرب البحث بكلمات مختلفة';
      icon = Icons.search_off;
    } else {
      // لا يوجد عملاء أصلاً
      title = 'لا يوجد عملاء حتى الآن';
      subtitle = 'اضغط على زر "إضافة عميل" لإضافة أول عميل';
      icon = Icons.people_outline;
    }
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class CustomerCard extends StatelessWidget {
  final Customer customer;
  final double debtAmount;
  final VoidCallback? onTap;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const CustomerCard({
    super.key,
    required this.customer,
    required this.debtAmount,
    this.onTap,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          children: [
            ListTile(
            contentPadding: const EdgeInsets.all(16),
            leading: CircleAvatar(
              backgroundColor: const Color(0xFF2196F3),
              child: Text(
                customer.name.isNotEmpty ? customer.name[0].toUpperCase() : '؟',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            title: Text(
              customer.name,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
              textDirection: TextDirection.rtl,
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(Icons.phone, size: 16, color: Colors.grey),
                    const SizedBox(width: 8),
                    Text(customer.phone),
                  ],
                ),
                if (customer.hasEmail) ...[
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(Icons.email, size: 16, color: Colors.grey),
                      const SizedBox(width: 8),
                      Expanded(child: Text(customer.email!)),
                    ],
                  ),
                ],
                if (customer.hasAddress) ...[
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(Icons.location_on, size: 16, color: Colors.grey),
                      const SizedBox(width: 8),
                      Expanded(child: Text(customer.address!)),
                    ],
                  ),
                ],
                // عرض الديون إن وجدت
                if (debtAmount > 0) ...[
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.red.withOpacity(0.3)),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.money_off, size: 16, color: Colors.red[700]),
                        const SizedBox(width: 4),
                        Text(
                          'دين: ${CurrencyFormatter.format(debtAmount)}',
                          style: TextStyle(
                            color: Colors.red[700],
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
            trailing: PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    onEdit();
                    break;
                  case 'delete':
                    onDelete();
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, size: 18),
                      SizedBox(width: 8),
                      Text('تعديل'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, size: 18, color: Colors.red),
                      SizedBox(width: 8),
                      Text('حذف', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
            ),
          ),
          if (customer.hasNotes)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
              ),
              child: Text(
                customer.notes!,
                style: TextStyle(
                  color: Colors.grey[700],
                  fontSize: 14,
                ),
                textDirection: TextDirection.rtl,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AddCustomerDialog extends StatefulWidget {
  final Customer? customer;

  const AddCustomerDialog({super.key, this.customer});

  @override
  State<AddCustomerDialog> createState() => _AddCustomerDialogState();
}

class _AddCustomerDialogState extends State<AddCustomerDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.customer != null) {
      _nameController.text = widget.customer!.name;
      _phoneController.text = widget.customer!.phone;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  void _saveCustomer() {
    if (_formKey.currentState!.validate()) {
      final customer = Customer(
        id: widget.customer?.id,
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim(),
        email: null, // تم تبسيط النموذج - لا نطلب البريد الإلكتروني
        address: null, // تم تبسيط النموذج - لا نطلب العنوان
        notes: null, // تم تبسيط النموذج - لا نطلب الملاحظات
        createdAt: widget.customer?.createdAt,
      );

      Navigator.of(context).pop(customer);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.customer == null ? 'إضافة عميل جديد' : 'تعديل بيانات العميل'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                textDirection: TextDirection.rtl,
                decoration: const InputDecoration(
                  labelText: 'الاسم *',
                  hintText: 'أدخل اسم العميل',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.person),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'الاسم مطلوب';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'رقم الهاتف *',
                  hintText: 'أدخل رقم الهاتف',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.phone),
                ),
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'رقم الهاتف مطلوب';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _saveCustomer,
          child: Text(widget.customer == null ? 'إضافة' : 'حفظ'),
        ),
      ],
    );
  }
}
