import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:intl/intl.dart';
import '../utils/app_colors.dart';
import '../services/database_helper.dart';

class TodoScreen extends StatefulWidget {
  const TodoScreen({super.key});

  @override
  State<TodoScreen> createState() => _TodoScreenState();
}

class _TodoScreenState extends State<TodoScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  
  List<TodoItem> _todoItems = [];
  List<TodoItem> _filteredItems = [];
  String _searchQuery = '';
  TodoFilter _currentFilter = TodoFilter.all;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _loadTodoItems();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadTodoItems() async {
    try {
      final dbHelper = DatabaseHelper();
      final todoMaps = await dbHelper.getAllTodos();
      
      setState(() {
        _todoItems = todoMaps.map((map) => TodoItem(
          id: map['id'],
          title: map['title'],
          description: map['description'] ?? '',
          isCompleted: map['is_completed'] == 1,
          priority: TodoPriority.values.firstWhere(
            (p) => p.name == map['priority'],
            orElse: () => TodoPriority.medium,
          ),
          dueDate: DateTime.parse(map['due_date']),
          category: map['category'],
          createdAt: DateTime.parse(map['created_at']),
        )).toList();
        _applyFilters();
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading todo items: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _applyFilters() {
    List<TodoItem> filtered = List.from(_todoItems);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((item) =>
          item.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          item.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          item.category.toLowerCase().contains(_searchQuery.toLowerCase())).toList();
    }

    // Apply status filter
    switch (_currentFilter) {
      case TodoFilter.pending:
        filtered = filtered.where((item) => !item.isCompleted).toList();
        break;
      case TodoFilter.completed:
        filtered = filtered.where((item) => item.isCompleted).toList();
        break;
      case TodoFilter.overdue:
        filtered = filtered.where((item) => 
            !item.isCompleted && item.dueDate.isBefore(DateTime.now())).toList();
        break;
      case TodoFilter.today:
        final today = DateTime.now();
        filtered = filtered.where((item) => 
            item.dueDate.year == today.year &&
            item.dueDate.month == today.month &&
            item.dueDate.day == today.day).toList();
        break;
      case TodoFilter.all:
        // No additional filtering
        break;
    }

    // Sort by priority and due date
    filtered.sort((a, b) {
      if (a.isCompleted != b.isCompleted) {
        return a.isCompleted ? 1 : -1; // Completed items at the bottom
      }
      
      int priorityComparison = b.priority.index.compareTo(a.priority.index);
      if (priorityComparison != 0) {
        return priorityComparison;
      }
      
      return a.dueDate.compareTo(b.dueDate);
    });

    setState(() {
      _filteredItems = filtered;
    });
  }

  void _showAddTodoDialog() {
    showDialog(
      context: context,
      builder: (context) => AddTodoDialog(
        onTodoAdded: (todoItem) async {
          try {
            final dbHelper = DatabaseHelper();
            await dbHelper.insertTodo({
              'id': todoItem.id,
              'title': todoItem.title,
              'description': todoItem.description,
              'is_completed': todoItem.isCompleted ? 1 : 0,
              'priority': todoItem.priority.name,
              'due_date': todoItem.dueDate.toIso8601String(),
              'category': todoItem.category,
              'created_at': todoItem.createdAt.toIso8601String(),
              'updated_at': todoItem.createdAt.toIso8601String(),
            });
            
            // Reload todos from database
            await _loadTodoItems();
          } catch (e) {
            print('Error adding todo: $e');
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('خطأ في إضافة المهمة'),
                backgroundColor: AppColors.error,
              ),
            );
          }
        },
      ),
    );
  }

  void _toggleTodoCompletion(TodoItem item) async {
    try {
      final dbHelper = DatabaseHelper();
      await dbHelper.toggleTodoCompletion(item.id);
      
      // Reload todos from database
      await _loadTodoItems();
    } catch (e) {
      print('Error toggling todo completion: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('خطأ في تحديث حالة المهمة'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  void _deleteTodoItem(TodoItem item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المهمة'),
        content: Text('هل أنت متأكد من حذف المهمة "${item.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                final dbHelper = DatabaseHelper();
                await dbHelper.deleteTodo(item.id);
                
                // Reload todos from database
                await _loadTodoItems();
                
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم حذف المهمة بنجاح'),
                    backgroundColor: AppColors.success,
                  ),
                );
              } catch (e) {
                print('Error deleting todo: $e');
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('خطأ في حذف المهمة'),
                    backgroundColor: AppColors.error,
                  ),
                );
              }
            },
            child: const Text(
              'حذف',
              style: TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }

  void _showEditTodoDialog(TodoItem item) {
    showDialog(
      context: context,
      builder: (context) => EditTodoDialog(
        todoItem: item,
        onTodoUpdated: (updatedItem) async {
          try {
            final dbHelper = DatabaseHelper();
            await dbHelper.updateTodo(updatedItem.id, {
              'title': updatedItem.title,
              'description': updatedItem.description,
              'priority': updatedItem.priority.name,
              'due_date': updatedItem.dueDate.toIso8601String(),
              'category': updatedItem.category,
              'updated_at': DateTime.now().toIso8601String(),
            });
            
            // Reload todos from database
            await _loadTodoItems();
          } catch (e) {
            print('Error updating todo: $e');
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('خطأ في تحديث المهمة'),
                backgroundColor: AppColors.error,
              ),
            );
          }
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('قائمة المهام'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddTodoDialog,
            tooltip: 'إضافة مهمة جديدة',
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.veryLightBlue,
              AppColors.offWhite,
            ],
          ),
        ),
        child: Column(
          children: [
            // Search and Filter Section
            Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Search Bar
                  TextField(
                    decoration: InputDecoration(
                      hintText: 'البحث في المهام...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: AppColors.white,
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                        _applyFilters();
                      });
                    },
                  ),
                  const SizedBox(height: 12),
                  
                  // Filter Chips
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: TodoFilter.values.map((filter) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: FilterChip(
                            label: Text(_getFilterLabel(filter)),
                            selected: _currentFilter == filter,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() {
                                  _currentFilter = filter;
                                  _applyFilters();
                                });
                              }
                            },
                            selectedColor: AppColors.primaryBlue.withOpacity(0.2),
                            checkmarkColor: AppColors.primaryBlue,
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ),
            ),

            // Stats Row
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: const [
                  BoxShadow(
                    color: AppColors.cardShadow,
                    blurRadius: 5,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem(
                    'الكل',
                    _todoItems.length.toString(),
                    AppColors.primaryBlue,
                    Icons.list,
                  ),
                  _buildStatItem(
                    'معلقة',
                    _todoItems.where((item) => !item.isCompleted).length.toString(),
                    AppColors.warning,
                    Icons.pending,
                  ),
                  _buildStatItem(
                    'مكتملة',
                    _todoItems.where((item) => item.isCompleted).length.toString(),
                    AppColors.success,
                    Icons.check_circle,
                  ),
                  _buildStatItem(
                    'متأخرة',
                    _todoItems.where((item) => 
                        !item.isCompleted && item.dueDate.isBefore(DateTime.now())).length.toString(),
                    AppColors.error,
                    Icons.schedule,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Todo List
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _filteredItems.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                _searchQuery.isNotEmpty ? Icons.search_off : Icons.task_alt,
                                size: 64,
                                color: AppColors.lightGray,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                _searchQuery.isNotEmpty 
                                    ? 'لم يتم العثور على مهام تطابق البحث'
                                    : 'لا توجد مهام حالياً',
                                style: const TextStyle(
                                  color: AppColors.secondaryText,
                                  fontSize: 16,
                                ),
                              ),
                              if (_searchQuery.isEmpty) ...[
                                const SizedBox(height: 16),
                                ElevatedButton.icon(
                                  onPressed: _showAddTodoDialog,
                                  icon: const Icon(Icons.add),
                                  label: const Text('إضافة مهمة جديدة'),
                                ),
                              ],
                            ],
                          ),
                        )
                      : AnimationLimiter(
                          child: ListView.builder(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            itemCount: _filteredItems.length,
                            itemBuilder: (context, index) {
                              return AnimationConfiguration.staggeredList(
                                position: index,
                                duration: const Duration(milliseconds: 375),
                                child: SlideAnimation(
                                  verticalOffset: 50.0,
                                  child: FadeInAnimation(
                                    child: _buildTodoItem(_filteredItems[index], index),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.secondaryText,
          ),
        ),
      ],
    );
  }

  Widget _buildTodoItem(TodoItem item, int index) {
    final isOverdue = !item.isCompleted && item.dueDate.isBefore(DateTime.now());
    final priorityColor = _getPriorityColor(item.priority);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isOverdue ? AppColors.error : priorityColor.withOpacity(0.3),
          width: isOverdue ? 2 : 1,
        ),
        boxShadow: const [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Checkbox(
          value: item.isCompleted,
          onChanged: (_) => _toggleTodoCompletion(item),
          activeColor: AppColors.success,
        ),
        title: Text(
          item.title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            decoration: item.isCompleted ? TextDecoration.lineThrough : null,
            color: item.isCompleted ? AppColors.secondaryText : AppColors.primaryText,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (item.description.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                item.description,
                style: TextStyle(
                  fontSize: 14,
                  color: item.isCompleted ? AppColors.secondaryText : AppColors.primaryText,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
            const SizedBox(height: 8),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: priorityColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    _getPriorityLabel(item.priority),
                    style: TextStyle(
                      fontSize: 10,
                      color: priorityColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: AppColors.info.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    item.category,
                    style: const TextStyle(
                      fontSize: 10,
                      color: AppColors.info,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.schedule,
                  size: 14,
                  color: isOverdue ? AppColors.error : AppColors.secondaryText,
                ),
                const SizedBox(width: 4),
                Text(
                  DateFormat('dd/MM/yyyy').format(item.dueDate),
                  style: TextStyle(
                    fontSize: 12,
                    color: isOverdue ? AppColors.error : AppColors.secondaryText,
                    fontWeight: isOverdue ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton(
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'edit',
              child: const Row(
                children: [
                  Icon(Icons.edit, color: AppColors.info),
                  SizedBox(width: 8),
                  Text('تعديل'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'delete',
              child: const Row(
                children: [
                  Icon(Icons.delete, color: AppColors.error),
                  SizedBox(width: 8),
                  Text('حذف'),
                ],
              ),
            ),
          ],
          onSelected: (value) {
            if (value == 'delete') {
              _deleteTodoItem(item);
            } else if (value == 'edit') {
              _showEditTodoDialog(item);
            }
          },
        ),
      ),
    );
  }

  String _getFilterLabel(TodoFilter filter) {
    switch (filter) {
      case TodoFilter.all:
        return 'الكل';
      case TodoFilter.pending:
        return 'معلقة';
      case TodoFilter.completed:
        return 'مكتملة';
      case TodoFilter.overdue:
        return 'متأخرة';
      case TodoFilter.today:
        return 'اليوم';
    }
  }

  Color _getPriorityColor(TodoPriority priority) {
    switch (priority) {
      case TodoPriority.high:
        return AppColors.error;
      case TodoPriority.medium:
        return AppColors.warning;
      case TodoPriority.low:
        return AppColors.success;
    }
  }

  String _getPriorityLabel(TodoPriority priority) {
    switch (priority) {
      case TodoPriority.high:
        return 'عالية';
      case TodoPriority.medium:
        return 'متوسطة';
      case TodoPriority.low:
        return 'منخفضة';
    }
  }
}

// Add Todo Dialog
class AddTodoDialog extends StatefulWidget {
  final Function(TodoItem) onTodoAdded;

  const AddTodoDialog({
    super.key,
    required this.onTodoAdded,
  });

  @override
  State<AddTodoDialog> createState() => _AddTodoDialogState();
}

class _AddTodoDialogState extends State<AddTodoDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  DateTime _selectedDate = DateTime.now().add(const Duration(days: 1));
  TodoPriority _selectedPriority = TodoPriority.medium;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.add_task, color: AppColors.primaryBlue),
          SizedBox(width: 8),
          Text('إضافة مهمة جديدة'),
        ],
      ),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Title
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'عنوان المهمة',
                  prefixIcon: Icon(Icons.title),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال عنوان المهمة';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Description
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'وصف المهمة (اختياري)',
                  prefixIcon: Icon(Icons.description),
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),

              // Priority
              DropdownButtonFormField<TodoPriority>(
                value: _selectedPriority,
                decoration: const InputDecoration(
                  labelText: 'الأولوية',
                  prefixIcon: Icon(Icons.priority_high),
                  border: OutlineInputBorder(),
                ),
                items: TodoPriority.values.map((priority) {
                  return DropdownMenuItem(
                    value: priority,
                    child: Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: _getPriorityColor(priority),
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(_getPriorityLabel(priority)),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedPriority = value!;
                  });
                },
              ),
              const SizedBox(height: 16),

              // Due Date
              ListTile(
                title: const Text('تاريخ الاستحقاق'),
                subtitle: Text(DateFormat('dd/MM/yyyy').format(_selectedDate)),
                leading: const Icon(Icons.calendar_today),
                trailing: const Icon(Icons.edit),
                onTap: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: _selectedDate,
                    firstDate: DateTime.now(),
                    lastDate: DateTime.now().add(const Duration(days: 365)),
                  );
                  if (date != null) {
                    setState(() {
                      _selectedDate = date;
                    });
                  }
                },
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(color: Colors.grey.shade400),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              final todoItem = TodoItem(
                id: DateTime.now().millisecondsSinceEpoch.toString(),
                title: _titleController.text,
                description: _descriptionController.text,
                isCompleted: false,
                priority: _selectedPriority,
                dueDate: _selectedDate,
                category: 'عام', // Default category
                createdAt: DateTime.now(),
              );
              
              widget.onTodoAdded(todoItem);
              Navigator.pop(context);
              
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إضافة المهمة بنجاح'),
                  backgroundColor: AppColors.success,
                ),
              );
            }
          },
          child: const Text('إضافة'),
        ),
      ],
    );
  }

  Color _getPriorityColor(TodoPriority priority) {
    switch (priority) {
      case TodoPriority.high:
        return AppColors.error;
      case TodoPriority.medium:
        return AppColors.warning;
      case TodoPriority.low:
        return AppColors.success;
    }
  }

  String _getPriorityLabel(TodoPriority priority) {
    switch (priority) {
      case TodoPriority.high:
        return 'عالية';
      case TodoPriority.medium:
        return 'متوسطة';
      case TodoPriority.low:
        return 'منخفضة';
    }
  }
}

// Models
class TodoItem {
  final String id;
  final String title;
  final String description;
  bool isCompleted;
  final TodoPriority priority;
  final DateTime dueDate;
  final String category;
  final DateTime createdAt;

  TodoItem({
    required this.id,
    required this.title,
    required this.description,
    required this.isCompleted,
    required this.priority,
    required this.dueDate,
    required this.category,
    required this.createdAt,
  });
}

enum TodoPriority { high, medium, low }
enum TodoFilter { all, pending, completed, overdue, today }

// Edit Todo Dialog
class EditTodoDialog extends StatefulWidget {
  final TodoItem todoItem;
  final Function(TodoItem) onTodoUpdated;

  const EditTodoDialog({
    super.key,
    required this.todoItem,
    required this.onTodoUpdated,
  });

  @override
  State<EditTodoDialog> createState() => _EditTodoDialogState();
}

class _EditTodoDialogState extends State<EditTodoDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  
  late DateTime _selectedDate;
  late TodoPriority _selectedPriority;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.todoItem.title);
    _descriptionController = TextEditingController(text: widget.todoItem.description);
    _selectedDate = widget.todoItem.dueDate;
    _selectedPriority = widget.todoItem.priority;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.edit, color: AppColors.info),
          SizedBox(width: 8),
          Text('تعديل المهمة'),
        ],
      ),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Title
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'عنوان المهمة',
                  prefixIcon: Icon(Icons.title),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال عنوان المهمة';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Description
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'وصف المهمة (اختياري)',
                  prefixIcon: Icon(Icons.description),
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),

              // Priority
              DropdownButtonFormField<TodoPriority>(
                value: _selectedPriority,
                decoration: const InputDecoration(
                  labelText: 'الأولوية',
                  prefixIcon: Icon(Icons.priority_high),
                  border: OutlineInputBorder(),
                ),
                items: TodoPriority.values.map((priority) {
                  return DropdownMenuItem(
                    value: priority,
                    child: Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: _getPriorityColor(priority),
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(_getPriorityLabel(priority)),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedPriority = value!;
                  });
                },
              ),
              const SizedBox(height: 16),

              // Due Date
              ListTile(
                title: const Text('تاريخ الاستحقاق'),
                subtitle: Text(DateFormat('dd/MM/yyyy').format(_selectedDate)),
                leading: const Icon(Icons.calendar_today),
                trailing: const Icon(Icons.edit),
                onTap: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: _selectedDate,
                    firstDate: DateTime.now(),
                    lastDate: DateTime.now().add(const Duration(days: 365)),
                  );
                  if (date != null) {
                    setState(() {
                      _selectedDate = date;
                    });
                  }
                },
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(color: Colors.grey.shade400),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              final updatedTodo = TodoItem(
                id: widget.todoItem.id,
                title: _titleController.text,
                description: _descriptionController.text,
                isCompleted: widget.todoItem.isCompleted,
                priority: _selectedPriority,
                dueDate: _selectedDate,
                category: widget.todoItem.category,
                createdAt: widget.todoItem.createdAt,
              );
              
              widget.onTodoUpdated(updatedTodo);
              Navigator.pop(context);
              
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم تحديث المهمة بنجاح'),
                  backgroundColor: AppColors.success,
                ),
              );
            }
          },
          child: const Text('حفظ التغييرات'),
        ),
      ],
    );
  }

  Color _getPriorityColor(TodoPriority priority) {
    switch (priority) {
      case TodoPriority.high:
        return AppColors.error;
      case TodoPriority.medium:
        return AppColors.warning;
      case TodoPriority.low:
        return AppColors.success;
    }
  }

  String _getPriorityLabel(TodoPriority priority) {
    switch (priority) {
      case TodoPriority.high:
        return 'عالية';
      case TodoPriority.medium:
        return 'متوسطة';
      case TodoPriority.low:
        return 'منخفضة';
    }
  }
}
