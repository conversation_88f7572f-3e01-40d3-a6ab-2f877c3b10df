# نظام ترقيم الوصولات الجديد

## 📋 **تنسيق رقم الوصل الجديد: `YYMMDDXXXXX`**

### 🔢 **مكونات الرقم:**
- **YY**: آخر رقمين من السنة (25 للعام 2025)
- **MM**: رقم الشهر برقمين (01-12)
- **DD**: رقم اليوم برقمين (01-31)
- **XXXXX**: رقم تسلسلي لكل طلب (00001, 00002, إلخ)

### 📅 **أمثلة على أرقام الوصولات:**

**اليوم (16 يوليو 2025):**
- الطلب الأول: `2507160001`
- الطلب الثاني: `2507160002`
- الطلب العاشر: `2507160010`
- الطلب المائة: `2507160100`

**غداً (17 يوليو 2025):**
- الطلب الأول: `2507170001`
- الطلب الثاني: `2507170002`

### ✨ **المميزات:**

1. **يحتوي على التاريخ**: يمكن معرفة تاريخ إصدار الوصل من الرقم نفسه
2. **ترقيم تسلسلي مستمر**: لا يعاد تعيين الرقم مع كل يوم جديد
3. **فريد وغير متكرر**: كل رقم يُستخدم مرة واحدة فقط
4. **طول ثابت**: 10 أرقام دائماً
5. **مرتب زمنياً**: الوصولات مرتبة حسب التاريخ والوقت

### 🛠️ **دوال الإدارة:**

```dart
// إعادة تعيين العداد (للإدارة)
await PrintService.resetReceiptCounter();

// معرفة آخر رقم تسلسلي
int lastNumber = await PrintService.getLastSequenceNumber();

// معرفة عدد الوصولات اليوم
int todayCount = await PrintService.getTodayReceiptsCount();
```

### 🎯 **الفوائد:**
- ✅ **سهل القراءة**: يمكن تذكره وقراءته بسهولة
- ✅ **يحتوي على تاريخ**: لا حاجة للبحث عن تاريخ الإصدار
- ✅ **مناسب للمحاسبة**: ترقيم منطقي ومتسلسل
- ✅ **عدم التكرار**: مضمون عدم تكرار أي رقم

الآن كل وصل جديد سيحصل على رقم يحتوي على التاريخ + رقم تسلسلي فريد! 📊✨
