import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:studio_managment/services/company_settings_service.dart';

void main() {
  group('Company Settings Service Tests', () {
    setUp(() async {
      // Mock SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
    });

    test('should save and retrieve company settings', () async {
      // Arrange
      const companyName = 'استوديو التصوير الرقمي';
      const companyAddress = 'شارع الملك فهد، الرياض';
      const companyPhone = '+966 50 123 4567';
      const companyEmail = '<EMAIL>';

      // Act
      await CompanySettingsService.saveCompanySettings(
        companyName: companyName,
        companyPhone: companyPhone,
        companyEmail: companyEmail,
        companyAddress: companyAddress,
      );

      final settings = await CompanySettingsService.getCompanySettings();

      // Assert
      expect(settings['companyName'], equals(companyName));
      expect(settings['companyAddress'], equals(companyAddress));
      expect(settings['companyPhone'], equals(companyPhone));
      expect(settings['companyEmail'], equals(companyEmail));
    });

    test('should check if company is configured', () async {
      // Initially not configured
      expect(await CompanySettingsService.isCompanyConfigured(), isFalse);

      // After saving settings
      await CompanySettingsService.saveCompanySettings(
        companyName: 'Test Studio',
        companyPhone: '*********',
        companyEmail: '<EMAIL>',
      );

      expect(await CompanySettingsService.isCompanyConfigured(), isTrue);
    });

    test('should get company logo settings', () async {
      // Test with no logo
      var logoSettings = await CompanySettingsService.getCompanyLogo();
      expect(logoSettings['logoPath'], isNull);
      expect(logoSettings['logoEmoji'], isNull);

      // Save logo settings
      await CompanySettingsService.saveCompanySettings(
        companyName: 'Test',
        companyPhone: '123',
        companyEmail: '<EMAIL>',
        companyLogoPath: '/path/to/logo.png',
        companyLogoEmoji: '📸',
      );

      logoSettings = await CompanySettingsService.getCompanyLogo();
      expect(logoSettings['logoPath'], equals('/path/to/logo.png'));
      expect(logoSettings['logoEmoji'], equals('📸'));
    });

    test('should clear company logo path', () async {
      // Save logo first
      await CompanySettingsService.saveCompanySettings(
        companyName: 'Test',
        companyPhone: '123',
        companyEmail: '<EMAIL>',
        companyLogoPath: '/path/to/logo.png',
      );

      // Clear logo
      await CompanySettingsService.clearCompanyLogoPath();

      // Check if cleared
      final logoSettings = await CompanySettingsService.getCompanyLogo();
      expect(logoSettings['logoPath'], isNull);
    });

    test('should return default values for empty settings', () async {
      final settings = await CompanySettingsService.getCompanySettings();
      
      expect(settings['companyName'], equals('استوديو الذكريات الجميلة'));
      expect(settings['companyAddress'], isEmpty);
      expect(settings['companyPhone'], equals('0555123456'));
      expect(settings['companyEmail'], equals('<EMAIL>'));
    });

    test('should get company name with fallback', () async {
      // Test default name
      expect(await CompanySettingsService.getCompanyName(), equals('استوديو الذكريات الجميلة'));

      // Save custom name
      await CompanySettingsService.saveCompanySettings(
        companyName: 'استوديو النور الرقمي',
        companyPhone: '123',
        companyEmail: '<EMAIL>',
      );

      expect(await CompanySettingsService.getCompanyName(), equals('استوديو النور الرقمي'));
    });
  });
}
