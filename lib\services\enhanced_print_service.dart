import 'dart:io';
import 'package:path_provider/path_provider.dart';
import '../models/booking.dart';
import '../services/html_receipt_enhanced_service.dart';

class EnhancedPrintService {
  /// Generate receipt using HTML service only
  static Future<Map<String, String?>> generateReceipt({
    required Booking booking,
    String receiptType = 'booking',
    double? paidAmount,
    bool generateHtml = true,
    bool generateImage = false, // تم إيقاف الصور - نستخدم HTML فقط
    String htmlTemplate = 'modern',
    Map<String, dynamic>? customData,
  }) async {
    final results = <String, String?>{};
    
    try {
      // Generate HTML receipt only
      final htmlPath = await HtmlReceiptService.generateHtmlReceipt(
        booking: booking,
        templateName: htmlTemplate,
        receiptType: receiptType,
        paidAmount: paidAmount,
        customData: customData ?? _getDefaultCustomData(),
      );
      results['html'] = htmlPath;
      
      // For compatibility, set the same path for 'image' and 'pdf' keys
      results['image'] = htmlPath;
      results['pdf'] = htmlPath;
      
      return results;
    } catch (e) {
      print('Error generating receipt: $e');
      rethrow;
    }
  }

  /// Open receipt file using HTML service
  static Future<void> openReceipt(String filePath) async {
    final file = File(filePath);
    if (!await file.exists()) {
      throw Exception('الملف غير موجود');
    }
    
    // Always use HTML service to open files
    await HtmlReceiptService.openHtmlFile(filePath);
  }

  /// Get default company data
  static Map<String, dynamic> _getDefaultCustomData() {
    return {
      'companyName': 'استوديو الذكريات الجميلة',
      'companyLogo': '📸',
      'companyPhone': '0555123456',
      'companyEmail': '<EMAIL>',
      'companyAddress': 'الرياض، المملكة العربية السعودية',
      'companyWebsite': 'www.studio.com',
    };
  }

  /// Generate comprehensive receipt package (HTML + Image + PDF if possible)
  static Future<Map<String, String?>> generateReceiptPackage({
    required Booking booking,
    String receiptType = 'booking',
    double? paidAmount,
    String htmlTemplate = 'modern',
    Map<String, dynamic>? customData,
  }) async {
    final results = await generateReceipt(
      booking: booking,
      receiptType: receiptType,
      paidAmount: paidAmount,
      generateHtml: true,
      generateImage: true,
      htmlTemplate: htmlTemplate,
      customData: customData,
    );
    
    // Try to convert HTML to PDF if possible
    if (results['html'] != null) {
      try {
        final pdfPath = await HtmlReceiptService.convertHtmlToPdf(results['html']!);
        if (pdfPath != null) {
          results['pdf'] = pdfPath;
        }
      } catch (e) {
        print('PDF conversion not available: $e');
      }
    }
    
    return results;
  }

  /// Get receipt statistics
  static Future<Map<String, int>> getReceiptStatistics() async {
    try {
      final stats = <String, int>{
        'html_receipts': 0,
        'image_receipts': 0,
        'total_receipts': 0,
      };
      
      final directory = await getApplicationDocumentsDirectory();
      
      // Count HTML receipts
      final htmlDir = Directory('${directory.path}/receipts_html');
      if (await htmlDir.exists()) {
        await for (final entity in htmlDir.list()) {
          if (entity is File && entity.path.endsWith('.html')) {
            stats['html_receipts'] = stats['html_receipts']! + 1;
          }
        }
      }
      
      // Count image receipts
      final imageDir = Directory('${directory.path}/receipts');
      if (await imageDir.exists()) {
        await for (final entity in imageDir.list()) {
          if (entity is File && 
              (entity.path.endsWith('.png') || entity.path.endsWith('.jpg'))) {
            stats['image_receipts'] = stats['image_receipts']! + 1;
          }
        }
      }
      
      stats['total_receipts'] = stats['html_receipts']! + stats['image_receipts']!;
      
      return stats;
    } catch (e) {
      return {'html_receipts': 0, 'image_receipts': 0, 'total_receipts': 0};
    }
  }

  /// Clean old receipts (older than specified days)
  static Future<int> cleanOldReceipts({int olderThanDays = 30}) async {
    int deletedCount = 0;
    final cutoffDate = DateTime.now().subtract(Duration(days: olderThanDays));
    
    try {
      final directory = await getApplicationDocumentsDirectory();
      
      // Clean HTML receipts
      final htmlDir = Directory('${directory.path}/receipts_html');
      if (await htmlDir.exists()) {
        await for (final entity in htmlDir.list()) {
          if (entity is File) {
            final stat = await entity.stat();
            if (stat.modified.isBefore(cutoffDate)) {
              await entity.delete();
              deletedCount++;
            }
          }
        }
      }
      
      // Clean image receipts
      final imageDir = Directory('${directory.path}/receipts');
      if (await imageDir.exists()) {
        await for (final entity in imageDir.list()) {
          if (entity is File) {
            final stat = await entity.stat();
            if (stat.modified.isBefore(cutoffDate)) {
              await entity.delete();
              deletedCount++;
            }
          }
        }
      }
      
      return deletedCount;
    } catch (e) {
      print('Error cleaning old receipts: $e');
      return 0;
    }
  }
}

// Extension methods for easier access
extension ReceiptGeneration on Booking {
  /// Generate HTML receipt for this booking
  Future<String> generateHtmlReceipt({
    String template = 'modern',
    String receiptType = 'booking',
    double? paidAmount,
    Map<String, dynamic>? customData,
  }) async {
    return await HtmlReceiptService.generateHtmlReceipt(
      booking: this,
      templateName: template,
      receiptType: receiptType,
      paidAmount: paidAmount,
      customData: customData,
    );
  }

  /// Generate comprehensive receipt package
  Future<Map<String, String?>> generateReceiptPackage({
    String template = 'modern',
    String receiptType = 'booking',
    double? paidAmount,
    Map<String, dynamic>? customData,
  }) async {
    return await EnhancedPrintService.generateReceiptPackage(
      booking: this,
      receiptType: receiptType,
      paidAmount: paidAmount,
      htmlTemplate: template,
      customData: customData,
    );
  }
}
