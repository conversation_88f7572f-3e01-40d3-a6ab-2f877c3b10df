import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import '../utils/app_colors.dart';
import '../utils/currency_formatter.dart';
import '../utils/date_formatter.dart';
import '../services/database_helper.dart';
import '../models/booking.dart';
import '../models/cashbox_session.dart';
import '../services/auth_service.dart';
import 'expense_details_dialogs.dart';
import 'session_history_dialog.dart';
import 'cashbox_management_screen.dart';
import 'cashbox_movements_screen.dart';

class CashboxScreen extends StatefulWidget {
  const CashboxScreen({super.key});

  @override
  State<CashboxScreen> createState() => _CashboxScreenState();
}

class _CashboxScreenState extends State<CashboxScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  
  // Real financial data from database
  double _totalBalance = 0.0;
  double _todayIncome = 0.0;
  double _monthlyIncome = 0.0;
  double _todayExpenses = 0.0;
  double _monthlyExpenses = 0.0;
  
  // Date filtering
  DateTime _selectedStartDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _selectedEndDate = DateTime.now();
  String _selectedPeriod = 'شهر';
  
  // Chart data
  List<FlSpot> _incomeChartData = [];
  List<FlSpot> _expenseChartData = [];
  
  List<Transaction> _recentTransactions = [];
  
  // Session management
  Map<String, dynamic>? _currentSession;
  bool _hasActiveSession = false;
  double _sessionIncome = 0.0;
  double _sessionExpenses = 0.0;
  
  // Cashbox management
  List<Map<String, dynamic>> _cashboxes = [];
  String? _selectedCashboxId;
  Map<String, dynamic>? _selectedCashbox;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _initializeWithTestData();
    _animationController.forward();
  }

  Future<void> _initializeWithTestData() async {
    // Add some test transactions if database is empty
    await _addTestDataIfNeeded();
    await _loadCashboxes();
    await _loadSessionData();
    _loadFinancialData();
  }

  Future<void> _loadCashboxes() async {
    try {
      final cashboxes = await _databaseHelper.getAllCashboxes();
      setState(() {
        _cashboxes = cashboxes;
        if (cashboxes.isNotEmpty && _selectedCashboxId == null) {
          _selectedCashboxId = cashboxes.first['id'];
          _selectedCashbox = cashboxes.first;
        }
      });
    } catch (e) {
      print('Error loading cashboxes: $e');
    }
  }

  Future<void> _addTestDataIfNeeded() async {
    try {
      final existingTransactions = await _databaseHelper.getAllTransactions();
      print('Existing transactions: ${existingTransactions.length}');
      
      if (existingTransactions.isEmpty) {
        print('No test data needed - database will start empty');
      }
    } catch (e) {
      print('Error checking test data: $e');
    }
  }

  Future<void> _loadSessionData() async {
    try {
      final activeSession = await _databaseHelper.getActiveSession(
        cashboxId: _selectedCashboxId,
      );
      setState(() {
        _currentSession = activeSession;
        _hasActiveSession = activeSession != null;
      });
      
      // Load session financial data if we have an active session
      if (activeSession != null) {
        final sessionFinancials = await _databaseHelper.getSessionFinancialData(activeSession['id']);
        setState(() {
          _sessionIncome = sessionFinancials['income'] ?? 0.0;
          _sessionExpenses = sessionFinancials['expenses'] ?? 0.0;
        });
      } else {
        setState(() {
          _sessionIncome = 0.0;
          _sessionExpenses = 0.0;
        });
      }
      
      print('Active session: $activeSession');
      print('Session income: $_sessionIncome, expenses: $_sessionExpenses');
    } catch (e) {
      print('Error loading session data: $e');
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadFinancialData() async {
    try {
      print('Loading financial data...');
      
      // Load financial summary
      final summary = await _databaseHelper.getFinancialSummary();
      print('Financial summary: $summary');
      
      // Load transactions for the selected period
      final transactions = await _databaseHelper.getTransactionsByDateRange(
        _selectedStartDate,
        _selectedEndDate,
      );
      print('Transactions count: ${transactions.length}');
      print('Date range: $_selectedStartDate to $_selectedEndDate');
      
      // Load booking data for additional income
      final bookings = await _databaseHelper.getBookingsByDateRange(
        _selectedStartDate,
        _selectedEndDate,
      );
      print('Bookings count: ${bookings.length}');
      
      // Load cashbox movements for selected cashbox
      List<Map<String, dynamic>> cashboxMovements = [];
      if (_selectedCashboxId != null) {
        cashboxMovements = await _databaseHelper.getCashboxMovements(
          cashboxId: _selectedCashboxId!,
          startDate: _selectedStartDate,
          endDate: _selectedEndDate,
        );
      }
      print('Cashbox movements count: ${cashboxMovements.length}');
      
      // Calculate total from bookings with paid amounts
      double totalFromBookings = 0.0;
      for (var booking in bookings) {
        print('Booking: ${booking.customerName}, Paid: ${booking.paidAmount}, Total: ${booking.totalAmount}');
        totalFromBookings += booking.paidAmount;
      }
      print('Total from bookings: $totalFromBookings');
      
      // Calculate cashbox movements totals
      double cashboxIncome = 0.0;
      double cashboxExpenses = 0.0;
      double todayCashboxIncome = 0.0;
      double todayCashboxExpenses = 0.0;
      final today = DateTime.now();
      
      for (var movement in cashboxMovements) {
        final amount = (movement['amount'] as num).toDouble();
        final type = movement['type'] as String;
        final createdAt = DateTime.parse(movement['created_at']);
        final isToday = createdAt.day == today.day && 
                       createdAt.month == today.month && 
                       createdAt.year == today.year;
        
        if (type == 'deposit') {
          cashboxIncome += amount;
          if (isToday) todayCashboxIncome += amount;
        } else if (type == 'withdrawal') {
          cashboxExpenses += amount;
          if (isToday) todayCashboxExpenses += amount;
        }
        // Note: transfers are handled separately as they don't affect overall income/expenses
      }
      print('Cashbox income: $cashboxIncome, expenses: $cashboxExpenses');
      print('Today cashbox income: $todayCashboxIncome, expenses: $todayCashboxExpenses');
      
      setState(() {
        _totalBalance = (summary['totalRevenue'] ?? 0.0) - (summary['totalExpenses'] ?? 0.0) + totalFromBookings + cashboxIncome - cashboxExpenses;
        _todayIncome = (summary['dailyRevenue'] ?? 0.0) + _calculateTodayIncomeFromBookings(bookings) + todayCashboxIncome;
        _monthlyIncome = (summary['monthlyRevenue'] ?? 0.0) + totalFromBookings + cashboxIncome;
        _todayExpenses = (summary['dailyExpenses'] ?? 0.0) + todayCashboxExpenses;
        _monthlyExpenses = (summary['monthlyExpenses'] ?? 0.0) + cashboxExpenses;
        
        // Convert database transactions to Transaction objects
        _recentTransactions = _convertDbTransactions(transactions);
        
        // Add booking payments as transactions
        _addBookingTransactions(bookings);
        
        // Add cashbox movements as transactions
        _addCashboxMovementsAsTransactions(cashboxMovements);
        
        // Generate chart data
        _generateChartData();
      });
      
      print('Final balances - Total: $_totalBalance, Today Income: $_todayIncome, Monthly Income: $_monthlyIncome');
    } catch (e) {
      print('Error loading financial data: $e');
    }
  }

  double _calculateTodayIncomeFromBookings(List<dynamic> bookings) {
    final today = DateTime.now();
    double todayIncome = 0.0;
    
    for (var booking in bookings) {
      // Check if deposit was paid today
      if (booking.deposit > 0 && 
          booking.createdAt.day == today.day &&
          booking.createdAt.month == today.month &&
          booking.createdAt.year == today.year) {
        todayIncome += booking.deposit;
      }
      
      // Check if remaining amount was paid today (delivery date)
      if (booking.paidAmount > booking.deposit &&
          booking.deliveryDate.day == today.day &&
          booking.deliveryDate.month == today.month &&
          booking.deliveryDate.year == today.year) {
        final remainingAmount = booking.paidAmount - booking.deposit;
        todayIncome += remainingAmount;
      }
    }
    
    return todayIncome;
  }

  List<Transaction> _convertDbTransactions(List<Map<String, dynamic>> dbTransactions) {
    return dbTransactions.map((t) => Transaction(
      id: t['id'] ?? '',
      type: t['type'] == 'income' ? TransactionType.income : TransactionType.expense,
      amount: (t['amount'] ?? 0.0).toDouble(),
      description: t['description'] ?? '',
      date: DateTime.parse(t['date']),
      category: t['category'] ?? '',
    )).toList();
  }

  void _addBookingTransactions(List<dynamic> bookings) {
    for (var booking in bookings) {
      // Add deposit payment if exists
      if (booking.deposit > 0) {
        _recentTransactions.add(Transaction(
          id: 'B${booking.id}_deposit',
          type: TransactionType.income,
          amount: booking.deposit,
          description: 'عربون من ${booking.customerName} - ${booking.serviceType}',
          date: booking.createdAt, // Use creation date for deposit
          category: 'عربون',
        ));
      }
      
      // Add remaining payment if customer paid more than deposit
      if (booking.paidAmount > booking.deposit) {
        final remainingAmount = booking.paidAmount - booking.deposit;
        _recentTransactions.add(Transaction(
          id: 'B${booking.id}_remaining',
          type: TransactionType.income,
          amount: remainingAmount,
          description: 'استكمال دفع من ${booking.customerName} - ${booking.serviceType}',
          date: booking.deliveryDate, // Use delivery date for remaining payment
          category: 'استكمال دفع',
        ));
      }
    }
    
    // Sort by date
    _recentTransactions.sort((a, b) => b.date.compareTo(a.date));
  }

  void _addCashboxMovementsAsTransactions(List<Map<String, dynamic>> movements) {
    for (var movement in movements) {
      final amount = (movement['amount'] as num).toDouble();
      final type = movement['type'] as String;
      final description = movement['description'] as String;
      final createdAt = DateTime.parse(movement['created_at']);
      
      if (type == 'deposit') {
        _recentTransactions.add(Transaction(
          id: 'CM${movement['id']}_deposit',
          type: TransactionType.income,
          amount: amount,
          description: 'إيداع في الصندوق: $description',
          date: createdAt,
          category: 'إيداع صندوق',
        ));
      } else if (type == 'withdrawal') {
        _recentTransactions.add(Transaction(
          id: 'CM${movement['id']}_withdrawal',
          type: TransactionType.expense,
          amount: amount,
          description: 'سحب من الصندوق: $description',
          date: createdAt,
          category: 'سحب صندوق',
        ));
      }
      // Note: transfers are not added as they don't represent income/expense, just movement between cashboxes
    }
  }

  void _generateChartData() {
    final Map<int, double> dailyIncome = {};
    final Map<int, double> dailyExpenses = {};
    
    // Get last 7 days
    final now = DateTime.now();
    for (int i = 6; i >= 0; i--) {
      dailyIncome[i] = 0;
      dailyExpenses[i] = 0;
    }
    
    for (var transaction in _recentTransactions) {
      final daysDiff = now.difference(transaction.date).inDays;
      if (daysDiff >= 0 && daysDiff <= 6) {
        final index = 6 - daysDiff;
        
        if (transaction.type == TransactionType.income) {
          dailyIncome[index] = (dailyIncome[index] ?? 0) + transaction.amount;
        } else {
          dailyExpenses[index] = (dailyExpenses[index] ?? 0) + transaction.amount;
        }
      }
    }
    
    _incomeChartData = dailyIncome.entries
        .map((e) => FlSpot(
              e.key.toDouble(),
              e.value / 1000, // Convert to thousands for better chart display
            ))
        .toList();
        
    _expenseChartData = dailyExpenses.entries
        .map((e) => FlSpot(
              e.key.toDouble(),
              e.value / 1000,
            ))
        .toList();
  }

  void _showDateFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة البيانات'),
        content: StatefulBuilder(
          builder: (context, setStateDialog) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<String>(
                value: _selectedPeriod,
                decoration: const InputDecoration(
                  labelText: 'الفترة الزمنية',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 'يوم', child: Text('اليوم')),
                  DropdownMenuItem(value: 'أسبوع', child: Text('الأسبوع')),
                  DropdownMenuItem(value: 'شهر', child: Text('الشهر')),
                  DropdownMenuItem(value: 'سنة', child: Text('السنة')),
                  DropdownMenuItem(value: 'مخصص', child: Text('فترة مخصصة')),
                ],
                onChanged: (value) {
                  setStateDialog(() {
                    _selectedPeriod = value!;
                    _updateDateRange();
                  });
                },
              ),
              
              if (_selectedPeriod == 'مخصص') ...[
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ListTile(
                        title: const Text('من'),
                        subtitle: Text(DateFormat('dd/MM/yyyy').format(_selectedStartDate)),
                        trailing: const Icon(Icons.calendar_month),
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _selectedStartDate,
                            firstDate: DateTime(2020),
                            lastDate: DateTime.now(),
                          );
                          if (date != null) {
                            setStateDialog(() {
                              _selectedStartDate = date;
                            });
                          }
                        },
                      ),
                    ),
                    Expanded(
                      child: ListTile(
                        title: const Text('إلى'),
                        subtitle: Text(DateFormat('dd/MM/yyyy').format(_selectedEndDate)),
                        trailing: const Icon(Icons.calendar_month),
                        onTap: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _selectedEndDate,
                            firstDate: _selectedStartDate,
                            lastDate: DateTime.now(),
                          );
                          if (date != null) {
                            setStateDialog(() {
                              _selectedEndDate = date;
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _loadFinancialData();
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  void _updateDateRange() {
    final now = DateTime.now();
    switch (_selectedPeriod) {
      case 'يوم':
        _selectedStartDate = DateTime(now.year, now.month, now.day);
        _selectedEndDate = now;
        break;
      case 'أسبوع':
        _selectedStartDate = now.subtract(Duration(days: now.weekday - 1));
        _selectedEndDate = now;
        break;
      case 'شهر':
        _selectedStartDate = DateTime(now.year, now.month, 1);
        _selectedEndDate = now;
        break;
      case 'سنة':
        _selectedStartDate = DateTime(now.year, 1, 1);
        _selectedEndDate = now;
        break;
    }
  }

  void _showAddTransactionDialog() {
    // Check if there's an active session
    if (!_hasActiveSession) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يجب فتح جلسة أولاً قبل إضافة المعاملات المالية'),
          backgroundColor: AppColors.warning,
          action: SnackBarAction(
            label: 'فتح جلسة',
            textColor: Colors.white,
            onPressed: _showOpenSessionDialog,
          ),
          duration: const Duration(seconds: 4),
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => const AddTransactionDialog(),
    ).then((result) {
      if (result != null && result is Transaction) {
        // Save to database
        _saveTransactionToDatabase(result);
      }
    });
  }

  void _showTodayIncomeDetails() {
    showDialog(
      context: context,
      builder: (context) => TodayIncomeDetailsDialog(
        databaseHelper: _databaseHelper,
      ),
    );
  }

  void _showMonthlyIncomeDetails() {
    showDialog(
      context: context,
      builder: (context) => MonthlyIncomeDetailsDialog(
        databaseHelper: _databaseHelper,
      ),
    );
  }

  void _navigateToCashboxMovements() {
    if (_selectedCashboxId != null && _selectedCashbox != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CashboxMovementsScreen(
            cashboxId: _selectedCashboxId!,
            cashboxName: _selectedCashbox!['name'],
          ),
        ),
      ).then((_) {
        // Reload financial data when returning from movements screen
        _loadFinancialData();
        _loadSessionData();
      });
    }
  }

  void _showTodayExpenseDetails() {
    showDialog(
      context: context,
      builder: (context) => TodayExpenseDetailsDialog(
        databaseHelper: _databaseHelper,
      ),
    );
  }

  void _showMonthlyExpenseDetails() {
    showDialog(
      context: context,
      builder: (context) => MonthlyExpenseDetailsDialog(
        databaseHelper: _databaseHelper,
      ),
    );
  }

  Future<void> _saveTransactionToDatabase(Transaction transaction) async {
    try {
      // Check if there's an active session
      if (!_hasActiveSession) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('يجب فتح جلسة أولاً قبل إضافة المعاملات المالية'),
              backgroundColor: AppColors.warning,
              duration: const Duration(seconds: 3),
            ),
          );
        }
        return;
      }

      await _databaseHelper.insertTransaction({
        'id': transaction.id,
        'type': transaction.type == TransactionType.income ? 'income' : 'expense',
        'amount': transaction.amount,
        'description': transaction.description,
        'date': transaction.date.toIso8601String(),
        'category': transaction.category,
        'created_at': DateTime.now().toIso8601String(),
      });
      
      // Reload session data to update financial calculations
      await _loadSessionData();
      // Reload financial data for overall statistics
      _loadFinancialData();
      
      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إضافة المعاملة المالية بنجاح'),
            backgroundColor: AppColors.success,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      print('Error saving transaction: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة المعاملة المالية: $e'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // =============== Session Management ===============

  void _showOpenSessionDialog() {
    final TextEditingController startingBalanceController = TextEditingController();
    final TextEditingController notesController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.play_circle_outline, color: AppColors.success),
            SizedBox(width: 12),
            Text('فتح جلسة جديدة'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'أدخل المبلغ الموجود في الصندوق حالياً لبدء جلسة جديدة:',
                style: TextStyle(fontSize: 14, color: AppColors.secondaryText),
              ),
              SizedBox(height: 20),
              TextFormField(
                controller: startingBalanceController,
                decoration: InputDecoration(
                  labelText: 'رصيد بداية الجلسة *',
                  suffixText: 'د.ع',
                  prefixIcon: Icon(Icons.account_balance_wallet, color: AppColors.success),
                  hintText: 'مثال: 100000',
                  border: OutlineInputBorder(),
                  helperText: 'المبلغ الفعلي الموجود في الصندوق',
                ),
                keyboardType: TextInputType.number,
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: notesController,
                decoration: InputDecoration(
                  labelText: 'ملاحظات (اختيارية)',
                  prefixIcon: Icon(Icons.note_add),
                  border: OutlineInputBorder(),
                  hintText: 'مثال: جلسة الصباح، نوبة المساء...',
                ),
                maxLines: 2,
              ),
              SizedBox(height: 16),
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.veryLightBlue,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: AppColors.info, size: 20),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'سيتم إغلاق أي جلسة نشطة حالياً تلقائياً',
                        style: TextStyle(
                          color: AppColors.info,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (startingBalanceController.text.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('يرجى إدخال رصيد بداية الجلسة'),
                    backgroundColor: AppColors.error,
                  ),
                );
                return;
              }

              final startingBalance = double.tryParse(startingBalanceController.text);
              if (startingBalance == null || startingBalance < 0) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('يرجى إدخال رقم صحيح ومبلغ صالح'),
                    backgroundColor: AppColors.error,
                  ),
                );
                return;
              }

              try {
                // TODO: Get current user from AuthService
                final openedBy = 'المدير'; // Replace with actual user
                
                await _databaseHelper.createCashboxSession(
                  cashboxId: _selectedCashboxId,
                  startingBalance: startingBalance,
                  openedBy: openedBy,
                  notes: notesController.text.trim().isEmpty ? null : notesController.text.trim(),
                );

                await _loadSessionData();
                Navigator.pop(context);

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('تم فتح جلسة جديدة بنجاح'),
                    backgroundColor: AppColors.success,
                  ),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('خطأ في فتح الجلسة: $e'),
                    backgroundColor: AppColors.error,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.success,
              foregroundColor: Colors.white,
            ),
            child: Text('فتح الجلسة'),
          ),
        ],
      ),
    );
  }

  void _showCloseSessionDialog() {
    if (_currentSession == null) return;

    final TextEditingController actualBalanceController = TextEditingController();
    final TextEditingController notesController = TextEditingController();

    // Calculate expected balance
    final startingBalance = (_currentSession!['starting_balance'] as num? ?? 0.0).toDouble();
    final expectedBalance = startingBalance + _sessionIncome - _sessionExpenses;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.stop_circle_outlined, color: AppColors.error),
            SizedBox(width: 12),
            Text('إغلاق الجلسة الحالية'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Session info
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.veryLightBlue,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.primaryBlue.withOpacity(0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تفاصيل الجلسة الحالية:',
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                    SizedBox(height: 12),
                    _buildSessionDetailRow('رصيد البداية', startingBalance),
                    _buildSessionDetailRow('الإيرادات', _sessionIncome),
                    _buildSessionDetailRow('المصروفات', _sessionExpenses),
                    Divider(),
                    _buildSessionDetailRow(
                      'الرصيد المتوقع', 
                      expectedBalance, 
                      isBold: true,
                      color: AppColors.primaryBlue,
                    ),
                  ],
                ),
              ),
              SizedBox(height: 20),
              Text(
                'أدخل المبلغ الفعلي الموجود في الصندوق حالياً:',
                style: TextStyle(fontSize: 14, color: AppColors.secondaryText),
              ),
              SizedBox(height: 12),
              TextFormField(
                controller: actualBalanceController,
                decoration: InputDecoration(
                  labelText: 'الرصيد الفعلي *',
                  suffixText: 'د.ع',
                  prefixIcon: Icon(Icons.account_balance, color: AppColors.error),
                  hintText: CurrencyFormatter.format(expectedBalance),
                  border: OutlineInputBorder(),
                  helperText: 'قم بعد النقود الموجودة فعلياً في الصندوق',
                ),
                keyboardType: TextInputType.number,
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: notesController,
                decoration: InputDecoration(
                  labelText: 'ملاحظات (اختيارية)',
                  prefixIcon: Icon(Icons.note_add),
                  border: OutlineInputBorder(),
                  hintText: 'أي ملاحظات حول الجلسة...',
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (actualBalanceController.text.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('يرجى إدخال الرصيد الفعلي'),
                    backgroundColor: AppColors.error,
                  ),
                );
                return;
              }

              final actualBalance = double.tryParse(actualBalanceController.text);
              if (actualBalance == null || actualBalance < 0) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('يرجى إدخال رقم صحيح ومبلغ صالح'),
                    backgroundColor: AppColors.error,
                  ),
                );
                return;
              }

              try {
                // TODO: Get current user from AuthService
                final closedBy = 'المدير'; // Replace with actual user
                
                await _databaseHelper.closeCashboxSession(
                  actualBalance: actualBalance,
                  closedBy: closedBy,
                  notes: notesController.text.trim().isEmpty ? null : notesController.text.trim(),
                );

                await _loadSessionData();
                Navigator.pop(context);

                final difference = actualBalance - expectedBalance;
                String message = 'تم إغلاق الجلسة بنجاح';
                Color backgroundColor = AppColors.success;

                if (difference != 0) {
                  if (difference > 0) {
                    message += '\nزيادة في الصندوق: ${CurrencyFormatter.format(difference)}';
                    backgroundColor = AppColors.warning;
                  } else {
                    message += '\nنقص في الصندوق: ${CurrencyFormatter.format(difference.abs())}';
                    backgroundColor = AppColors.error;
                  }
                }

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(message),
                    backgroundColor: backgroundColor,
                    duration: Duration(seconds: 4),
                  ),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('خطأ في إغلاق الجلسة: $e'),
                    backgroundColor: AppColors.error,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: Text('إغلاق الجلسة'),
          ),
        ],
      ),
    );
  }

  Widget _buildSessionDetailRow(String label, double amount, {bool isBold = false, Color? color}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '$label:',
            style: TextStyle(
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              color: color ?? AppColors.primaryText,
            ),
          ),
          Text(
            CurrencyFormatter.format(amount),
            style: TextStyle(
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              color: color ?? AppColors.primaryText,
            ),
          ),
        ],
      ),
    );
  }

  void _showSessionHistoryDialog() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          child: SessionHistoryDialog(databaseHelper: _databaseHelper),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Text(_hasActiveSession ? 'الصندوق - جلسة نشطة' : 'الصندوق - لا توجد جلسة'),
            if (_selectedCashbox != null) ...[
              SizedBox(width: 8),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _selectedCashbox!['name'],
                  style: TextStyle(fontSize: 12),
                ),
              ),
            ],
          ],
        ),
        actions: [
          // Cashbox selector
          if (_cashboxes.length > 1)
            PopupMenuButton<String>(
              icon: Icon(Icons.account_balance_wallet),
              tooltip: 'اختيار الصندوق',
              onSelected: (cashboxId) async {
                setState(() {
                  _selectedCashboxId = cashboxId;
                  _selectedCashbox = _cashboxes.firstWhere(
                    (box) => box['id'] == cashboxId,
                  );
                });
                await _loadSessionData();
                _loadFinancialData();
              },
              itemBuilder: (context) => _cashboxes.map((cashbox) {
                return PopupMenuItem<String>(
                  value: cashbox['id'],
                  child: Row(
                    children: [
                      Icon(
                        Icons.account_balance_wallet,
                        size: 16,
                        color: cashbox['id'] == _selectedCashboxId 
                            ? Colors.blue 
                            : Colors.grey,
                      ),
                      SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              cashbox['name'],
                              style: TextStyle(
                                fontWeight: cashbox['id'] == _selectedCashboxId 
                                    ? FontWeight.bold 
                                    : FontWeight.normal,
                              ),
                            ),
                            if (cashbox['location'] != null)
                              Text(
                                cashbox['location'],
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                          ],
                        ),
                      ),
                      if (cashbox['id'] == _selectedCashboxId)
                        Icon(Icons.check, size: 16, color: Colors.blue),
                    ],
                  ),
                );
              }).toList(),
            ),
          
          // Cashbox movements
          IconButton(
            icon: Icon(Icons.receipt_long),
            onPressed: _selectedCashboxId != null 
                ? () => _navigateToCashboxMovements()
                : null,
            tooltip: 'حركات الصندوق',
          ),
          
          // Cashbox management
          IconButton(
            icon: Icon(Icons.settings_applications),
            onPressed: () async {
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => CashboxManagementScreen(),
                ),
              );
              if (result == true) {
                await _loadCashboxes();
                await _loadSessionData();
                _loadFinancialData();
              }
            },
            tooltip: 'إدارة الصناديق',
          ),
          
          // Session management buttons
          if (!_hasActiveSession)
            IconButton(
              icon: const Icon(Icons.play_circle_outline, color: Colors.green),
              onPressed: _showOpenSessionDialog,
              tooltip: 'فتح جلسة جديدة',
            )
          else
            IconButton(
              icon: const Icon(Icons.stop_circle_outlined, color: Colors.red),
              onPressed: _showCloseSessionDialog,
              tooltip: 'إغلاق الجلسة الحالية',
            ),
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: _showSessionHistoryDialog,
            tooltip: 'تاريخ الجلسات',
          ),
          IconButton(
            icon: const Icon(Icons.filter_alt),
            onPressed: _showDateFilterDialog,
            tooltip: 'فلترة البيانات',
          ),
          IconButton(
            icon: Icon(
              Icons.add,
              color: _hasActiveSession ? null : Colors.grey,
            ),
            onPressed: _showAddTransactionDialog,
            tooltip: _hasActiveSession ? 'إضافة معاملة مالية' : 'فتح جلسة أولاً',
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.veryLightBlue,
              AppColors.offWhite,
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: AnimationLimiter(
              child: Column(
                children: [
          // Session Status Card
          if (_hasActiveSession) _buildSessionStatusCard(),
          if (_hasActiveSession) const SizedBox(height: 12),
          
          // Cashbox info card if multiple cashboxes exist
          if (_cashboxes.length > 1 && _selectedCashbox != null) 
            _buildCashboxInfoCard(),
          if (_cashboxes.length > 1 && _selectedCashbox != null) 
            const SizedBox(height: 12),                  // Financial Summary Cards
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: AnimationConfiguration.staggeredList(
                      position: 0,
                      duration: const Duration(milliseconds: 375),
                      child: SlideAnimation(
                        verticalOffset: -50.0,
                        child: FadeInAnimation(
                          child: Column(
                            children: [
                              // Main Balance Card
                              _buildBalanceCard(),
                              const SizedBox(height: 12),
                              Row(
                                children: [
                                  Expanded(
                                    child: GestureDetector(
                                      onTap: () => _showTodayIncomeDetails(),
                                      child: _buildSummaryCard(
                                        'دخل اليوم',
                                        _todayIncome,
                                        Icons.trending_up,
                                        AppColors.success,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: GestureDetector(
                                      onTap: () => _showTodayExpenseDetails(),
                                      child: _buildSummaryCard(
                                        'مصاريف اليوم',
                                        _todayExpenses,
                                        Icons.trending_down,
                                        AppColors.error,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              Row(
                                children: [
                                  Expanded(
                                    child: GestureDetector(
                                      onTap: () => _showMonthlyIncomeDetails(),
                                      child: _buildSummaryCard(
                                        'دخل الشهر',
                                        _monthlyIncome,
                                        Icons.calendar_month,
                                        AppColors.primaryBlue,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: GestureDetector(
                                      onTap: () => _showMonthlyExpenseDetails(),
                                      child: _buildSummaryCard(
                                        'مصاريف الشهر',
                                        _monthlyExpenses,
                                        Icons.money_off,
                                        AppColors.warning,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              // Chart Card
                              _buildChartCard(),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  
                  // Transactions List
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    height: 400, // Fixed height to make it scrollable
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: const [
                        BoxShadow(
                          color: AppColors.cardShadow,
                          blurRadius: 10,
                          offset: Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Transactions Header
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: const BoxDecoration(
                            color: AppColors.primaryBlue,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(16),
                              topRight: Radius.circular(16),
                            ),
                          ),
                          child: Row(
                            children: [
                              const Icon(
                                Icons.history,
                                color: AppColors.white,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              const Text(
                                'المعاملات الأخيرة',
                                style: TextStyle(
                                  color: AppColors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const Spacer(),
                              Text(
                                '${_recentTransactions.length} معاملة',
                                style: const TextStyle(
                                  color: AppColors.white,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                        
                        // Transactions List
                        Expanded(
                          child: _recentTransactions.isEmpty
                              ? const Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.receipt_long,
                                        size: 64,
                                        color: AppColors.lightGray,
                                      ),
                                      SizedBox(height: 16),
                                      Text(
                                        'لا توجد معاملات مالية',
                                        style: TextStyle(
                                          color: AppColors.secondaryText,
                                          fontSize: 16,
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : ListView.builder(
                                  padding: const EdgeInsets.only(bottom: 16),
                                  itemCount: _recentTransactions.length,
                                  itemBuilder: (context, index) {
                                    return AnimationConfiguration.staggeredList(
                                      position: index,
                                      duration: const Duration(milliseconds: 375),
                                      child: SlideAnimation(
                                        horizontalOffset: 50.0,
                                        child: FadeInAnimation(
                                          child: _buildTransactionTile(_recentTransactions[index]),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBalanceCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primaryBlue,
            AppColors.info,
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: const [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 20,
            offset: Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.account_balance_wallet,
            color: AppColors.white,
            size: 40,
          ),
          const SizedBox(height: 12),
          const Text(
            'الرصيد الإجمالي',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            CurrencyFormatter.format(_totalBalance),
            style: const TextStyle(
              color: AppColors.white,
              fontSize: 32,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSessionStatusCard() {
    if (_currentSession == null) return const SizedBox();

    final startDate = DateTime.parse(_currentSession!['start_date']);
    final startingBalance = (_currentSession!['starting_balance'] as num? ?? 0.0).toDouble();
    final openedBy = _currentSession!['opened_by'] as String? ?? 'غير محدد';
    final duration = DateTime.now().difference(startDate);
    
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF10B981),
            Color(0xFF34D399),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: const [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 15,
            offset: Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.play_circle_filled,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'جلسة نشطة',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${hours}س ${minutes}د',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'رصيد البداية',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      CurrencyFormatter.format(startingBalance),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                width: 1,
                height: 40,
                color: Colors.white.withOpacity(0.3),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'الرصيد المتوقع',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      CurrencyFormatter.format(startingBalance + _sessionIncome - _sessionExpenses),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'فُتحت بواسطة: $openedBy • ${DateFormatter.formatDateTimeShort(startDate)}',
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 11,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildChartCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: const [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 10,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.show_chart,
                color: AppColors.primaryBlue,
                size: 24,
              ),
              const SizedBox(width: 12),
              const Text(
                'الرسم البياني للإيرادات والمصروفات',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryText,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          SizedBox(
            height: 200,
            child: _incomeChartData.isEmpty && _expenseChartData.isEmpty
                ? const Center(
                    child: Text(
                      'لا توجد بيانات للعرض',
                      style: TextStyle(
                        color: AppColors.secondaryText,
                        fontSize: 14,
                      ),
                    ),
                  )
                : LineChart(
                    LineChartData(
                      gridData: const FlGridData(show: true),
                      titlesData: FlTitlesData(
                        leftTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            reservedSize: 60,
                            getTitlesWidget: (value, meta) {
                              return Text(
                                '${value.toInt()}K',
                                style: const TextStyle(
                                  color: AppColors.secondaryText,
                                  fontSize: 10,
                                ),
                              );
                            },
                          ),
                        ),
                        bottomTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            reservedSize: 30,
                            getTitlesWidget: (value, meta) {
                              final now = DateTime.now();
                              final dayIndex = value.toInt();
                              if (dayIndex >= 0 && dayIndex <= 6) {
                                final date = now.subtract(Duration(days: 6 - dayIndex));
                                return Text(
                                  DateFormat('dd/MM').format(date),
                                  style: const TextStyle(
                                    color: AppColors.secondaryText,
                                    fontSize: 10,
                                  ),
                                );
                              }
                              return const Text('');
                            },
                          ),
                        ),
                        rightTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                        topTitles: const AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                      ),
                      borderData: FlBorderData(
                        show: true,
                        border: Border.all(
                          color: AppColors.lightGray,
                          width: 1,
                        ),
                      ),
                      lineBarsData: [
                        if (_incomeChartData.isNotEmpty)
                          LineChartBarData(
                            spots: _incomeChartData,
                            isCurved: true,
                            color: AppColors.success,
                            barWidth: 3,
                            isStrokeCapRound: true,
                            dotData: const FlDotData(show: true),
                            belowBarData: BarAreaData(
                              show: true,
                              color: AppColors.success.withOpacity(0.1),
                            ),
                          ),
                        if (_expenseChartData.isNotEmpty)
                          LineChartBarData(
                            spots: _expenseChartData,
                            isCurved: true,
                            color: AppColors.error,
                            barWidth: 3,
                            isStrokeCapRound: true,
                            dotData: const FlDotData(show: true),
                            belowBarData: BarAreaData(
                              show: true,
                              color: AppColors.error.withOpacity(0.1),
                            ),
                          ),
                      ],
                      minX: 0,
                      maxX: 6,
                      minY: 0,
                    ),
                  ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildLegendItem('الإيرادات', AppColors.success),
              _buildLegendItem('المصروفات', AppColors.error),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(String title, Color color) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.secondaryText,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
    String title,
    double amount,
    IconData icon,
    Color color, {
    bool isWide = false,
  }) {
    return Container(
      width: isWide ? double.infinity : null,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: const [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 10,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: color,
                size: 24,
              ),
              if (!isWide) const SizedBox(width: 8),
              if (isWide) const SizedBox(width: 12),
              Flexible(
                child: Text(
                  title,
                  style: const TextStyle(
                    color: AppColors.secondaryText,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            CurrencyFormatter.format(amount),
            style: TextStyle(
              color: color,
              fontSize: isWide ? 20 : 16,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCashboxInfoCard() {
    if (_selectedCashbox == null) return const SizedBox();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF3B82F6),
            Color(0xFF60A5FA),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: const [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 15,
            offset: Offset(0, 8),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.account_balance_wallet,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _selectedCashbox!['name'],
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      color: Colors.white.withOpacity(0.8),
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _selectedCashbox!['location'] ?? 'غير محدد',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Icon(
                      Icons.attach_money,
                      color: Colors.white.withOpacity(0.8),
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _selectedCashbox!['currency'] ?? 'IQD',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          if (_cashboxes.length > 1)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${_cashboxes.length} صناديق',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTransactionTile(Transaction transaction) {
    final isIncome = transaction.type == TransactionType.income;
    final color = isIncome ? AppColors.success : AppColors.error;
    final icon = isIncome ? Icons.add_circle : Icons.remove_circle;
    final sign = isIncome ? '+' : '-';

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.offWhite,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.description,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  '${transaction.category} • ${DateFormatter.formatDateTimeShort(transaction.date)}',
                  style: const TextStyle(
                    color: AppColors.secondaryText,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '$sign${CurrencyFormatter.format(transaction.amount)}',
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              Text(
                transaction.id,
                style: const TextStyle(
                  color: AppColors.secondaryText,
                  fontSize: 10,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// Transaction Model
class Transaction {
  final String id;
  final TransactionType type;
  final double amount;
  final String description;
  final DateTime date;
  final String category;

  Transaction({
    required this.id,
    required this.type,
    required this.amount,
    required this.description,
    required this.date,
    required this.category,
  });
}

enum TransactionType { income, expense }

// Add Transaction Dialog
class AddTransactionDialog extends StatefulWidget {
  const AddTransactionDialog({super.key});

  @override
  State<AddTransactionDialog> createState() => _AddTransactionDialogState();
}

class _AddTransactionDialogState extends State<AddTransactionDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  TransactionType _selectedType = TransactionType.income;

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            _selectedType == TransactionType.income 
                ? Icons.add_circle 
                : Icons.remove_circle,
            color: _selectedType == TransactionType.income 
                ? AppColors.success 
                : AppColors.error,
          ),
          const SizedBox(width: 8),
          const Text('إضافة معاملة مالية'),
        ],
      ),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Transaction Type
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.lightGray),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: RadioListTile<TransactionType>(
                        title: const Text('دخل'),
                        value: TransactionType.income,
                        groupValue: _selectedType,
                        activeColor: AppColors.success,
                        onChanged: (value) {
                          setState(() {
                            _selectedType = value!;
                          });
                        },
                      ),
                    ),
                    Expanded(
                      child: RadioListTile<TransactionType>(
                        title: const Text('مصروف'),
                        value: TransactionType.expense,
                        groupValue: _selectedType,
                        activeColor: AppColors.error,
                        onChanged: (value) {
                          setState(() {
                            _selectedType = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              
              // Amount
              TextFormField(
                controller: _amountController,
                decoration: InputDecoration(
                  labelText: 'المبلغ',
                  suffixText: 'د.ع',
                  prefixIcon: Icon(
                    Icons.attach_money,
                    color: _selectedType == TransactionType.income 
                        ? AppColors.success 
                        : AppColors.error,
                  ),
                  hintText: 'مثال: 50000',
                  border: const OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال المبلغ';
                  }
                  if (double.tryParse(value) == null) {
                    return 'يرجى إدخال رقم صحيح';
                  }
                  if (double.parse(value) <= 0) {
                    return 'يجب أن يكون المبلغ أكبر من صفر';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              
              // Description
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'الوصف',
                  prefixIcon: Icon(Icons.description),
                  hintText: 'وصف المعاملة',
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال وصف المعاملة';
                  }
                  if (value.length < 3) {
                    return 'الوصف يجب أن يكون أكثر من 3 أحرف';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              final transaction = Transaction(
                id: 'T${DateTime.now().millisecondsSinceEpoch}',
                type: _selectedType,
                amount: double.parse(_amountController.text),
                description: _descriptionController.text,
                date: DateTime.now(),
                category: _selectedType == TransactionType.income ? 'إيرادات' : 'مصروفات',
              );
              Navigator.pop(context, transaction);
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: _selectedType == TransactionType.income 
                ? AppColors.success 
                : AppColors.error,
          ),
          child: const Text(
            'إضافة',
            style: TextStyle(color: Colors.white),
          ),
        ),
      ],
    );
  }
}

// Today Income Details Dialog
class TodayIncomeDetailsDialog extends StatefulWidget {
  final DatabaseHelper databaseHelper;

  const TodayIncomeDetailsDialog({
    super.key,
    required this.databaseHelper,
  });

  @override
  State<TodayIncomeDetailsDialog> createState() => _TodayIncomeDetailsDialogState();
}

class _TodayIncomeDetailsDialogState extends State<TodayIncomeDetailsDialog> {
  List<IncomeDetail> _incomeDetails = [];
  bool _isLoading = true;
  double _totalIncome = 0.0;

  @override
  void initState() {
    super.initState();
    _loadTodayIncomeDetails();
  }

  Future<void> _loadTodayIncomeDetails() async {
    try {
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);

      List<IncomeDetail> details = [];

      // Load transactions for today
      final transactions = await widget.databaseHelper.getTransactionsByDateRange(
        startOfDay,
        endOfDay,
      );

      for (var transaction in transactions) {
        if (transaction['type'] == 'income') {
          details.add(IncomeDetail(
            id: transaction['id'],
            amount: transaction['amount'].toDouble(),
            description: transaction['description'],
            category: transaction['category'],
            date: DateTime.parse(transaction['date']),
            type: IncomeType.transaction,
            customerName: '',
            serviceType: '',
          ));
        }
      }

      // Load bookings for today
      final bookings = await widget.databaseHelper.getBookingsByDateRange(
        startOfDay,
        endOfDay,
      );

      for (var booking in bookings) {
        // Check if customer paid deposit today (creation date)
        if (booking.deposit > 0 && 
            booking.createdAt.day == today.day &&
            booking.createdAt.month == today.month &&
            booking.createdAt.year == today.year) {
          details.add(IncomeDetail(
            id: 'B${booking.id}_deposit',
            amount: booking.deposit,
            description: 'عربون من ${booking.customerName}',
            category: 'عربون',
            date: booking.createdAt,
            type: IncomeType.advance,
            customerName: booking.customerName,
            serviceType: booking.serviceType,
          ));
        }

        // Check if customer completed payment today (delivery date)
        if (booking.paidAmount > booking.deposit) {
          final remainingAmount = booking.paidAmount - booking.deposit;
          if (remainingAmount > 0) {
            // Check if delivery was today
            final deliveryDate = booking.deliveryDate; 
            if (deliveryDate.day == today.day &&
                deliveryDate.month == today.month &&
                deliveryDate.year == today.year) {
              details.add(IncomeDetail(
                id: 'B${booking.id}_remaining',
                amount: remainingAmount,
                description: 'استكمال دفع من ${booking.customerName}',
                category: 'استكمال دفع',
                date: deliveryDate,
                type: IncomeType.completion,
                customerName: booking.customerName,
                serviceType: booking.serviceType,
              ));
            }
          }
        }
      }

      // Sort by date (newest first)
      details.sort((a, b) => b.date.compareTo(a.date));

      double total = details.fold(0.0, (sum, detail) => sum + detail.amount);

      setState(() {
        _incomeDetails = details;
        _totalIncome = total;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading today income details: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                const Icon(
                  Icons.trending_up,
                  color: AppColors.success,
                  size: 28,
                ),
                const SizedBox(width: 12),
                const Text(
                  'تفاصيل دخل اليوم',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryText,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Total Summary
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.success,
                    Color(0xFF4CAF50),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  const Text(
                    'إجمالي الدخل لهذا اليوم',
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    CurrencyFormatter.format(_totalIncome),
                    style: const TextStyle(
                      color: AppColors.white,
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${_incomeDetails.length} عملية مالية',
                    style: const TextStyle(
                      color: AppColors.white,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Details List
            if (_isLoading)
              const Expanded(
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              )
            else if (_incomeDetails.isEmpty)
              const Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.inbox,
                        size: 64,
                        color: AppColors.lightGray,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'لا توجد عمليات دخل لهذا اليوم',
                        style: TextStyle(
                          color: AppColors.secondaryText,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              Expanded(
                child: ListView.builder(
                  itemCount: _incomeDetails.length,
                  itemBuilder: (context, index) {
                    final detail = _incomeDetails[index];
                    return _buildIncomeDetailTile(detail);
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildIncomeDetailTile(IncomeDetail detail) {
    IconData icon;
    Color iconColor;
    String typeText;

    switch (detail.type) {
      case IncomeType.advance:
        icon = Icons.payments;
        iconColor = AppColors.info;
        typeText = 'عربون';
        break;
      case IncomeType.completion:
        icon = Icons.check_circle;
        iconColor = AppColors.success;
        typeText = 'استكمال دفع';
        break;
      case IncomeType.transaction:
        icon = Icons.receipt;
        iconColor = AppColors.primaryBlue;
        typeText = 'معاملة مالية';
        break;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: iconColor.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: const [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Icon
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),

          // Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  detail.description,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 15,
                    color: AppColors.primaryText,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                if (detail.customerName.isNotEmpty) ...[
                  Text(
                    'العميل: ${detail.customerName}',
                    style: const TextStyle(
                      color: AppColors.secondaryText,
                      fontSize: 13,
                    ),
                  ),
                  const SizedBox(height: 2),
                ],
                if (detail.serviceType.isNotEmpty) ...[
                  Text(
                    'الخدمة: ${detail.serviceType}',
                    style: const TextStyle(
                      color: AppColors.secondaryText,
                      fontSize: 13,
                    ),
                  ),
                  const SizedBox(height: 2),
                ],
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: iconColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        typeText,
                        style: TextStyle(
                          color: iconColor,
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      DateFormatter.formatTimeShort12Hour(detail.date),
                      style: const TextStyle(
                        color: AppColors.secondaryText,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Amount
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '+${CurrencyFormatter.format(detail.amount)}',
                style: TextStyle(
                  color: iconColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              Text(
                detail.id,
                style: const TextStyle(
                  color: AppColors.secondaryText,
                  fontSize: 10,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// Income Detail Model
class IncomeDetail {
  final String id;
  final double amount;
  final String description;
  final String category;
  final DateTime date;
  final IncomeType type;
  final String customerName;
  final String serviceType;

  IncomeDetail({
    required this.id,
    required this.amount,
    required this.description,
    required this.category,
    required this.date,
    required this.type,
    required this.customerName,
    required this.serviceType,
  });
}

enum IncomeType {
  advance,     // عربون
  completion,  // استكمال دفع
  transaction, // معاملة مالية عادية
}

// Monthly Income Details Dialog
class MonthlyIncomeDetailsDialog extends StatefulWidget {
  final DatabaseHelper databaseHelper;

  const MonthlyIncomeDetailsDialog({
    super.key,
    required this.databaseHelper,
  });

  @override
  State<MonthlyIncomeDetailsDialog> createState() => _MonthlyIncomeDetailsDialogState();
}

class _MonthlyIncomeDetailsDialogState extends State<MonthlyIncomeDetailsDialog> {
  List<IncomeDetail> _incomeDetails = [];
  bool _isLoading = true;
  double _totalIncome = 0.0;

  @override
  void initState() {
    super.initState();
    _loadMonthlyIncomeDetails();
  }

  Future<void> _loadMonthlyIncomeDetails() async {
    try {
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final endOfMonth = DateTime(now.year, now.month + 1, 1).subtract(const Duration(days: 1));

      List<IncomeDetail> details = [];

      // Load transactions for this month
      final transactions = await widget.databaseHelper.getTransactionsByDateRange(
        startOfMonth,
        endOfMonth,
      );

      for (var transaction in transactions) {
        if (transaction['type'] == 'income') {
          details.add(IncomeDetail(
            id: transaction['id'],
            amount: transaction['amount'].toDouble(),
            description: transaction['description'],
            category: transaction['category'],
            date: DateTime.parse(transaction['date']),
            type: IncomeType.transaction,
            customerName: '',
            serviceType: '',
          ));
        }
      }

      // Load bookings for this month
      final bookings = await widget.databaseHelper.getBookingsByDateRange(
        startOfMonth,
        endOfMonth,
      );

      for (var booking in bookings) {
        // Add deposit payments (when booking was created)
        if (booking.deposit > 0) {
          details.add(IncomeDetail(
            id: 'B${booking.id}_deposit',
            amount: booking.deposit,
            description: 'عربون من ${booking.customerName}',
            category: 'عربون',
            date: booking.createdAt,
            type: IncomeType.advance,
            customerName: booking.customerName,
            serviceType: booking.serviceType,
          ));
        }

        // Add remaining payments (when order was delivered)
        if (booking.paidAmount > booking.deposit) {
          final remainingAmount = booking.paidAmount - booking.deposit;
          if (remainingAmount > 0) {
            details.add(IncomeDetail(
              id: 'B${booking.id}_remaining',
              amount: remainingAmount,
              description: 'استكمال دفع من ${booking.customerName}',
              category: 'استكمال دفع',
              date: booking.deliveryDate,
              type: IncomeType.completion,
              customerName: booking.customerName,
              serviceType: booking.serviceType,
            ));
          }
        }
      }

      // Sort by date (newest first)
      details.sort((a, b) => b.date.compareTo(a.date));

      double total = details.fold(0.0, (sum, detail) => sum + detail.amount);

      setState(() {
        _incomeDetails = details;
        _totalIncome = total;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading monthly income details: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                const Icon(
                  Icons.calendar_month,
                  color: AppColors.primaryBlue,
                  size: 28,
                ),
                const SizedBox(width: 12),
                const Text(
                  'تفاصيل دخل الشهر',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryText,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Total Summary
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.primaryBlue,
                    AppColors.info,
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  const Text(
                    'إجمالي الدخل لهذا الشهر',
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    CurrencyFormatter.format(_totalIncome),
                    style: const TextStyle(
                      color: AppColors.white,
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${_incomeDetails.length} عملية مالية',
                    style: const TextStyle(
                      color: AppColors.white,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Details List
            if (_isLoading)
              const Expanded(
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              )
            else if (_incomeDetails.isEmpty)
              const Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.inbox,
                        size: 64,
                        color: AppColors.lightGray,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'لا توجد عمليات دخل لهذا الشهر',
                        style: TextStyle(
                          color: AppColors.secondaryText,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              Expanded(
                child: ListView.builder(
                  itemCount: _incomeDetails.length,
                  itemBuilder: (context, index) {
                    final detail = _incomeDetails[index];
                    return _buildIncomeDetailTile(detail);
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildIncomeDetailTile(IncomeDetail detail) {
    IconData icon;
    Color iconColor;
    String typeText;

    switch (detail.type) {
      case IncomeType.advance:
        icon = Icons.payments;
        iconColor = AppColors.info;
        typeText = 'عربون';
        break;
      case IncomeType.completion:
        icon = Icons.check_circle;
        iconColor = AppColors.success;
        typeText = 'استكمال دفع';
        break;
      case IncomeType.transaction:
        icon = Icons.receipt;
        iconColor = AppColors.primaryBlue;
        typeText = 'معاملة مالية';
        break;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: iconColor.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: const [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Icon
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),

          // Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  detail.description,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 15,
                    color: AppColors.primaryText,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                if (detail.customerName.isNotEmpty) ...[
                  Text(
                    'العميل: ${detail.customerName}',
                    style: const TextStyle(
                      color: AppColors.secondaryText,
                      fontSize: 13,
                    ),
                  ),
                  const SizedBox(height: 2),
                ],
                if (detail.serviceType.isNotEmpty) ...[
                  Text(
                    'الخدمة: ${detail.serviceType}',
                    style: const TextStyle(
                      color: AppColors.secondaryText,
                      fontSize: 13,
                    ),
                  ),
                  const SizedBox(height: 2),
                ],
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: iconColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        typeText,
                        style: TextStyle(
                          color: iconColor,
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      DateFormat('dd/MM/yyyy').format(detail.date),
                      style: const TextStyle(
                        color: AppColors.secondaryText,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Amount
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '+${CurrencyFormatter.format(detail.amount)}',
                style: TextStyle(
                  color: iconColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              Text(
                detail.id,
                style: const TextStyle(
                  color: AppColors.secondaryText,
                  fontSize: 10,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
