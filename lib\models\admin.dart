import 'package:uuid/uuid.dart';

enum AdminRole {
  owner('مالك'),
  manager('مدير'),
  supervisor('مشرف'),
  employee('موظف');

  const AdminRole(this.arabicName);
  final String arabicName;
}

class Admin {
  final String id;
  final String username;
  final String password;
  final String fullName;
  final String phone;
  final String? email;
  final AdminRole role;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Admin({
    String? id,
    required this.username,
    required this.password,
    required this.fullName,
    required this.phone,
    this.email,
    required this.role,
    this.isActive = true,
    DateTime? createdAt,
    this.updatedAt,
  }) : 
    id = id ?? const Uuid().v4(),
    createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'username': username,
      'password': password,
      'full_name': fullName,
      'phone': phone,
      'email': email,
      'role': role.name,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  factory Admin.fromMap(Map<String, dynamic> map) {
    return Admin(
      id: map['id'],
      username: map['username'],
      password: map['password'],
      fullName: map['full_name'],
      phone: map['phone'],
      email: map['email'],
      role: AdminRole.values.firstWhere(
        (role) => role.name == map['role'],
        orElse: () => AdminRole.employee,
      ),
      isActive: map['is_active'] == 1,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null 
          ? DateTime.parse(map['updated_at']) 
          : null,
    );
  }

  Admin copyWith({
    String? username,
    String? password,
    String? fullName,
    String? phone,
    String? email,
    AdminRole? role,
    bool? isActive,
    DateTime? updatedAt,
  }) {
    return Admin(
      id: id,
      username: username ?? this.username,
      password: password ?? this.password,
      fullName: fullName ?? this.fullName,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      role: role ?? this.role,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Admin(id: $id, username: $username, fullName: $fullName, role: ${role.arabicName})';
  }
}
