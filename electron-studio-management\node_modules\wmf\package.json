{"name": "wmf", "version": "1.0.2", "author": "sheetjs", "description": "Windows MetaFile (WMF) parser", "keywords": ["wmf", "image", "office", "word"], "bin": {}, "main": "./dist/wmf.node.js", "unpkg": "./dist/wmf.js", "jsdelivr": "./dist/wmf.js", "types": "types", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "dependencies": {}, "devDependencies": {"source-map-loader": "^0.2.4", "uglifyjs-webpack-plugin": "^2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-wmf.git"}, "scripts": {}, "config": {"blanket": {"pattern": "wmf.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "files": ["LICENSE", "README.md", "dist/wmf.js", "dist/wmf.node.js", "dist/wmf.js.map", "dist/wmf.node.js.map"], "bugs": {"url": "https://github.com/SheetJS/js-wmf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}}