import '../utils/currency_formatter.dart';

class ServicePricing {
  static const Map<String, int> servicePrices = {};

  /// Get the price for a specific service
  static int getServicePrice(String serviceName) {
    return servicePrices[serviceName] ?? 0;
  }

  /// Get formatted price for a specific service
  static String getFormattedServicePrice(String serviceName) {
    int price = getServicePrice(serviceName);
    return CurrencyFormatter.formatInt(price);
  }

  /// Get all services with their formatted prices
  static Map<String, String> getFormattedServicePrices() {
    Map<String, String> formattedPrices = {};
    servicePrices.forEach((service, price) {
      formattedPrices[service] = CurrencyFormatter.formatInt(price);
    });
    return formattedPrices;
  }

  /// Get service list with prices for display
  static List<String> getServiceListWithPrices() {
    return servicePrices.entries
        .map((entry) => '${entry.key} - ${CurrencyFormatter.formatInt(entry.value)}')
        .toList();
  }

  /// Common photography packages
  static const Map<String, PackageInfo> packages = {};

  /// Get package price
  static int getPackagePrice(String packageName) {
    return packages[packageName]?.price ?? 0;
  }

  /// Get formatted package price
  static String getFormattedPackagePrice(String packageName) {
    int price = getPackagePrice(packageName);
    return CurrencyFormatter.formatInt(price);
  }
}

class PackageInfo {
  final int price;
  final List<String> services;
  final String description;

  const PackageInfo({
    required this.price,
    required this.services,
    required this.description,
  });
}

/// Common pricing utilities
class PricingUtils {
  /// Calculate discount amount
  static double calculateDiscount(double totalAmount, double discountPercentage) {
    return totalAmount * (discountPercentage / 100);
  }

  /// Calculate total after discount
  static double calculateTotalAfterDiscount(double totalAmount, double discountPercentage) {
    return totalAmount - calculateDiscount(totalAmount, discountPercentage);
  }

  /// Calculate tax (if applicable)
  static double calculateTax(double amount, double taxPercentage) {
    return amount * (taxPercentage / 100);
  }

  /// Calculate installment amount
  static double calculateInstallment(double totalAmount, int numberOfInstallments) {
    return totalAmount / numberOfInstallments;
  }

  /// Format pricing breakdown
  static Map<String, String> formatPricingBreakdown({
    required double subtotal,
    double discount = 0,
    double tax = 0,
    double additionalFees = 0,
  }) {
    double total = subtotal - discount + tax + additionalFees;
    
    return {
      'المجموع الفرعي': CurrencyFormatter.format(subtotal),
      if (discount > 0) 'الخصم': '- ${CurrencyFormatter.format(discount)}',
      if (tax > 0) 'الضريبة': CurrencyFormatter.format(tax),
      if (additionalFees > 0) 'رسوم إضافية': CurrencyFormatter.format(additionalFees),
      'المجموع الكلي': CurrencyFormatter.format(total),
    };
  }
}
