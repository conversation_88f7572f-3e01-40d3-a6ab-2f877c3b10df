# نظام قوالب HTML لطباعة الوصولات

## نظرة عامة

تم إضافة نظام قوالب HTML متطور لطباعة الوصولات يوفر مرونة كبيرة في التصميم وسهولة في التخصيص. النظام يدعم إنشاء وصولات بتنسيق HTML قابل للتخصيص بالكامل.

## المميزات الجديدة

### 1. خدمة قوالب HTML (`HtmlReceiptService`)
- **إنشاء وصولات HTML**: تحويل بيانات الحجز إلى ملفات HTML جميلة
- **قوالب متعددة**: 4 قوالب مُصممة مسبقاً (حديث، كلاسيكي، بسيط، أنيق)
- **قوالب مخصصة**: إمكانية إنشاء وحفظ قوالب خاصة
- **دعم المتغيرات**: نظام متغيرات شامل لإدراج البيانات

### 2. إدارة قوالب HTML (`HtmlTemplateManagerScreen`)
- **واجهة سهلة**: إدارة القوالب من خلال واجهة بسيطة
- **معاينة مباشرة**: إنشاء وصولات تجريبية لاختبار القوالب
- **محرر مدمج**: إنشاء وتعديل قوالب HTML مباشرة
- **إدارة الملفات**: فتح وتصفح الوصولات المُنشأة

### 3. خيارات طباعة محسنة (`BookingReceiptOptionsDialog`)
- **تنسيقات متعددة**: HTML أو صورة أو كلاهما
- **اختيار القالب**: تحديد القالب المطلوب لـ HTML
- **معلومات الوصل**: معاينة بيانات الوصل قبل الإنشاء
- **فتح مباشر**: فتح الوصولات فور إنشائها

### 4. خدمة طباعة محسنة (`EnhancedPrintService`)
- **دعم التنسيقات المختلطة**: إنشاء HTML وصورة معاً
- **إحصائيات الوصولات**: تتبع عدد الوصولات المُنشأة
- **تنظيف تلقائي**: حذف الوصولات القديمة
- **إعدادات الشركة**: بيانات قابلة للتخصيص

## القوالب المتاحة

### 1. القالب الحديث (Modern)
- **التصميم**: تدرجات لونية جميلة وتأثيرات بصرية
- **المميزات**: 
  - شعار دائري متحرك
  - بطاقات معلومات منظمة
  - ألوان جذابة (أزرق وبنفسجي)
  - تصميم متجاوب

### 2. القالب الكلاسيكي (Classic)
- **التصميم**: تصميم تقليدي بحدود مزدوجة
- **المميزات**:
  - جدول معلومات منظم
  - حدود واضحة
  - تنسيق بسيط ومقروء
  - مناسب للطباعة

### 3. القالب البسيط (Minimal)
- **التصميم**: تصميم نظيف ومبسط
- **المميزات**:
  - أبيض ورمادي فقط
  - خطوط واضحة
  - مساحات بيضاء مناسبة
  - سرعة في التحميل

### 4. القالب الأنيق (Elegant)
- **التصميم**: تصميم راقي بخلفية داكنة
- **المميزات**:
  - خلفية متدرجة داكنة
  - بطاقات بيضاء أنيقة
  - تأثيرات ظلال جميلة
  - مناسب للمناسبات الخاصة

## المتغيرات المتاحة

### معلومات الشركة
- `{{COMPANY_NAME}}`: اسم الشركة
- `{{COMPANY_LOGO}}`: شعار الشركة
- `{{COMPANY_PHONE}}`: هاتف الشركة
- `{{COMPANY_EMAIL}}`: بريد الشركة الإلكتروني

### معلومات الوصل
- `{{RECEIPT_TITLE}}`: عنوان الوصل
- `{{RECEIPT_NUMBER}}`: رقم الوصل
- `{{PRINT_DATE}}`: تاريخ الطباعة
- `{{PRINT_TIME}}`: وقت الطباعة

### معلومات العميل
- `{{CUSTOMER_NAME}}`: اسم العميل
- `{{CUSTOMER_PHONE}}`: هاتف العميل

### معلومات الحجز
- `{{SERVICE_TYPE}}`: نوع الخدمة
- `{{BOOKING_DATE}}`: تاريخ الحجز
- `{{DELIVERY_DATE}}`: تاريخ التسليم
- `{{EVENT_DESCRIPTION}}`: وصف المناسبة
- `{{STATUS_TEXT}}`: حالة الطلب

### معلومات الخدمات
- `{{SELECTED_OFFERS}}`: العروض المختارة (HTML)
- `{{SELECTED_OFFERS_TEXT}}`: العروض المختارة (نص)
- `{{ADDITIONS}}`: الإضافات (HTML)
- `{{ADDITIONS_TEXT}}`: الإضافات (نص)

### معلومات مالية
- `{{TOTAL_AMOUNT}}`: إجمالي المبلغ
- `{{TOTAL_LABEL}}`: تسمية المجموع
- `{{DEPOSIT}}`: العربون
- `{{REMAINING_AMOUNT}}`: المبلغ المتبقي

### معلومات إضافية
- `{{NOTES}}`: الملاحظات
- `{{STATUS_BADGE}}`: شارة الحالة
- `{{STATUS_CLASS}}`: فئة CSS للحالة

## البلوكات الشرطية

يمكن استخدام البلوكات الشرطية لإظهار المحتوى فقط عند توفر البيانات:

```html
{{#if DEPOSIT}}
<div class="deposit-info">
    <p>العربون: {{DEPOSIT}} ر.س</p>
    <p>المتبقي: {{REMAINING_AMOUNT}} ر.س</p>
</div>
{{/if}}
```

البلوكات المتاحة:
- `{{#if DEPOSIT}}...{{/if}}`
- `{{#if NOTES}}...{{/if}}`
- `{{#if EVENT_DESCRIPTION}}...{{/if}}`
- `{{#if SELECTED_OFFERS}}...{{/if}}`
- `{{#if ADDITIONS}}...{{/if}}`

## كيفية الاستخدام

### 1. إنشاء وصل HTML
```dart
final htmlPath = await HtmlReceiptService.generateHtmlReceipt(
  booking: booking,
  templateName: 'modern',
  receiptType: 'booking',
  customData: {
    'companyName': 'استوديو الذكريات الجميلة',
    'companyPhone': '0555123456',
  },
);
```

### 2. إنشاء وصل بتنسيقات متعددة
```dart
final results = await EnhancedPrintService.generateReceipt(
  booking: booking,
  generateHtml: true,
  generateImage: true,
  htmlTemplate: 'elegant',
);
```

### 3. استخدام Dialog الخيارات
```dart
showDialog(
  context: context,
  builder: (context) => BookingReceiptOptionsDialog(
    booking: booking,
    receiptType: 'booking',
  ),
);
```

## إنشاء قالب مخصص

### 1. البنية الأساسية
```html
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>{{RECEIPT_TITLE}}</title>
    <style>
        /* CSS styles here */
    </style>
</head>
<body>
    <!-- HTML content here -->
</body>
</html>
```

### 2. استخدام المتغيرات
```html
<h1>{{COMPANY_NAME}}</h1>
<p>{{RECEIPT_TITLE}} - {{RECEIPT_NUMBER}}</p>
<p>العميل: {{CUSTOMER_NAME}}</p>
<p>المبلغ: {{TOTAL_AMOUNT}} ر.س</p>
```

### 3. حفظ القالب
```dart
await HtmlReceiptService.saveCustomTemplate(
  'my_template',
  htmlContent,
);
```

## المزايا

### 1. مرونة كاملة في التصميم
- استخدام CSS كامل
- تحكم دقيق في التخطيط
- دعم الخطوط المخصصة
- تأثيرات بصرية متقدمة

### 2. سهولة التخصيص
- تغيير الألوان والخطوط
- إضافة شعارات وصور
- تعديل التخطيط
- إضافة معلومات جديدة

### 3. التوافق والوصولية
- يعمل في جميع المتصفحات
- قابل للطباعة مباشرة
- يدعم الهواتف والأجهزة اللوحية
- إمكانية تحويل إلى PDF

### 4. سهولة المشاركة
- ملفات HTML خفيفة
- يمكن إرسالها بالبريد الإلكتروني
- عرض فوري في المتصفح
- حفظ دائم ومنظم

## ملفات النظام

### الخدمات
- `lib/services/html_receipt_service.dart`: خدمة قوالب HTML الرئيسية
- `lib/services/enhanced_print_service.dart`: خدمة الطباعة المحسنة

### الشاشات
- `lib/screens/html_template_manager_screen.dart`: إدارة قوالب HTML

### الواجهات
- `lib/widgets/booking_receipt_options_dialog.dart`: حوار خيارات الطباعة

## مجلدات الحفظ

### قوالب HTML
- المسار: `Documents/html_templates/`
- أنواع الملفات: `.html`

### الوصولات المُنشأة
- المسار: `Documents/receipts_html/`
- أنواع الملفات: `.html`

## النتيجة

نظام قوالب HTML يوفر:
✅ **مرونة كاملة**: تصميم وتخصيص بلا حدود  
✅ **سهولة الاستخدام**: واجهات بديهية ومبسطة  
✅ **جودة عالية**: قوالب احترافية وجميلة  
✅ **توافق شامل**: يعمل على جميع الأجهزة والمتصفحات  
✅ **إدارة ذكية**: تنظيم وإحصائيات متقدمة  

الآن يمكنك إنشاء وصولات HTML احترافية وجميلة بسهولة تامة!
