import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:excel/excel.dart' as excel;
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import 'dart:io';
import '../models/customer.dart';
import '../models/booking.dart';
import '../services/database_helper.dart';
import '../services/html_receipt_enhanced_service.dart';
import '../utils/app_colors.dart';
import '../utils/currency_formatter.dart';

class CustomerDetailsScreen extends StatefulWidget {
  final Customer customer;

  const CustomerDetailsScreen({super.key, required this.customer});

  @override
  State<CustomerDetailsScreen> createState() => _CustomerDetailsScreenState();
}

class _CustomerDetailsScreenState extends State<CustomerDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  
  List<Booking> _bookings = [];
  bool _isLoading = true;
  double _totalSpent = 0.0;
  double _totalDebt = 0.0;
  int _completedBookings = 0;
  int _pendingBookings = 0;
  List<Booking> _bookingsWithDebt = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadCustomerData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadCustomerData() async {
    setState(() => _isLoading = true);
    
    try {
      // تحميل حجوزات العميل
      final allBookings = await _databaseHelper.getAllBookings();
      final bookings = allBookings.where((booking) => booking.customerName == widget.customer.name).toList();
      
      // حساب الإحصائيات
      double totalSpent = 0.0;
      double totalDebt = 0.0;
      int completed = 0;
      int pending = 0;
      List<Booking> bookingsWithDebt = [];
      
      for (final booking in bookings) {
        totalSpent += booking.paidAmount;
        if (booking.status == BookingStatus.completed) {
          completed++;
        } else if (booking.status == BookingStatus.inProgress) {
          pending++;
        }
        
        // حساب الديون المتبقية
        double remainingAmount = booking.totalAmount - booking.paidAmount;
        if (remainingAmount > 0) {
          totalDebt += remainingAmount;
          bookingsWithDebt.add(booking);
        }
      }
      
      setState(() {
        _bookings = bookings;
        _totalSpent = totalSpent;
        _totalDebt = totalDebt;
        _completedBookings = completed;
        _pendingBookings = pending;
        _bookingsWithDebt = bookingsWithDebt;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل بيانات العميل: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Future<void> _payRemainingDebt(Booking booking) async {
    double remainingAmount = booking.totalAmount - booking.paidAmount;
    
    // عرض مربع حوار للتأكيد
    bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد تسديد الدين'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('هل تريد تسديد الدين المتبقي لهذا الحجز؟'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.veryLightBlue,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('رقم الحجز:'),
                      Text(
                        _formatBookingId(booking),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('المبلغ المتبقي:'),
                      Text(
                        CurrencyFormatter.format(remainingAmount),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppColors.error,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.success,
              foregroundColor: Colors.white,
            ),
            child: const Text('تأكيد التسديد'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _processPayment(booking, remainingAmount);
    }
  }

  Future<void> _payPartialDebt(Booking booking) async {
    double remainingAmount = booking.totalAmount - booking.paidAmount;
    TextEditingController amountController = TextEditingController();
    
    // عرض مربع حوار لإدخال المبلغ
    bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('دفع جزئي'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('أدخل المبلغ المراد دفعه:'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.veryLightBlue,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('رقم الحجز:'),
                      Text(
                        _formatBookingId(booking),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('المبلغ المتبقي:'),
                      Text(
                        CurrencyFormatter.format(remainingAmount),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppColors.error,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: amountController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'المبلغ المراد دفعه',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixText: 'ج.م ',
              ),
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              double? amount = double.tryParse(amountController.text);
              if (amount != null && amount > 0 && amount <= remainingAmount) {
                Navigator.of(context).pop(true);
              } else {
                _showErrorSnackBar('يرجى إدخال مبلغ صحيح لا يتجاوز المبلغ المتبقي');
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: Colors.white,
            ),
            child: const Text('تأكيد الدفع'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      double amount = double.parse(amountController.text);
      await _processPayment(booking, amount);
    }
  }

  Future<void> _processPayment(Booking booking, double amount) async {
    try {
      // حفظ المبلغ المدفوع سابقاً قبل التحديث
      double previousPaidAmount = booking.paidAmount;
      
      // إنشاء نسخة محدثة من الحجز
      double newPaidAmount = booking.paidAmount + amount;
      BookingStatus newStatus = booking.status;
      
      // إذا تم تسديد المبلغ بالكامل، يمكن تغيير حالة الحجز إلى مكتمل
      if (newPaidAmount >= booking.totalAmount) {
        newStatus = BookingStatus.completed;
      }
      
      Booking updatedBooking = booking.copyWith(
        paidAmount: newPaidAmount,
        status: newStatus,
        updatedAt: DateTime.now(),
      );
      
      // تحديث الحجز في قاعدة البيانات
      await _databaseHelper.updateBooking(updatedBooking);
      
      // إعادة تحميل البيانات
      await _loadCustomerData();
      
      // عرض حوار خيارات ما بعد التسديد
      _showPostPaymentOptions(booking, amount, previousPaidAmount, booking.totalAmount - newPaidAmount);
      
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء تسديد المبلغ: $e');
    }
  }

  void _showPostPaymentOptions(Booking booking, double paidAmount, double previousPaidAmount, double remainingAmount) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: AppColors.success),
            const SizedBox(width: 8),
            const Text('تم التسديد بنجاح'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.success.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('المبلغ المدفوع:'),
                      Text(
                        CurrencyFormatter.format(paidAmount),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppColors.success,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('المبلغ المتبقي:'),
                      Text(
                        CurrencyFormatter.format(remainingAmount),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: remainingAmount > 0 ? AppColors.warning : AppColors.success,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'هل تريد طباعة وصل التسديد؟',
              style: TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton.icon(
            onPressed: () async {
              Navigator.of(context).pop();
              await _printPaymentReceipt(booking, paidAmount, previousPaidAmount, remainingAmount);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: Colors.white,
            ),
            icon: const Icon(Icons.print),
            label: const Text('طباعة الوصل'),
          ),
        ],
      ),
    );
  }

  Future<void> _printPaymentReceipt(Booking booking, double paidAmount, double previousPaidAmount, double remainingAmount) async {
    try {
      // Save payment receipt as HTML file
      final htmlPath = await HtmlReceiptService.generateHtmlReceipt(
        booking: booking,
        templateName: 'modern',
        receiptType: 'payment',
        paidAmount: paidAmount,
        customData: {
          'companyName': 'استوديو الذكريات الجميلة',
          'companyLogo': '📸',
          'companyPhone': '0555123456',
          'companyEmail': '<EMAIL>',
        },
      );
      
      _showSuccessSnackBar('تم حفظ وصل التسديد كملف HTML بنجاح');
      // Open the saved HTML file
      await HtmlReceiptService.openHtmlFile(htmlPath);
    } catch (e) {
      _showErrorSnackBar('خطأ في حفظ وصل التسديد: $e');
    }
  }

  Future<void> _payAllDebts() async {
    if (_bookingsWithDebt.isEmpty || _totalDebt <= 0) {
      _showErrorSnackBar('لا توجد ديون متبقية لتسديدها');
      return;
    }
    
    // عرض مربع حوار للتأكيد
    bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد تسديد جميع الديون'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('هل تريد تسديد جميع الديون المتبقية لهذا العميل؟'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.veryLightBlue,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('عدد الحجوزات:'),
                      Text(
                        '${_bookingsWithDebt.length} حجز',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('إجمالي المبلغ:'),
                      Text(
                        CurrencyFormatter.format(_totalDebt),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppColors.error,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'سيتم تسديد جميع الديون المتبقية وتحديث حالة الحجوزات إلى مكتملة.',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.secondaryText,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.success,
              foregroundColor: Colors.white,
            ),
            child: const Text('تأكيد تسديد جميع الديون'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _processAllPayments();
    }
  }

  Future<void> _processAllPayments() async {
    try {
      double totalPaidAmount = 0;
      List<Booking> paidBookings = [];
      
      // تسديد جميع الديون
      for (Booking booking in _bookingsWithDebt) {
        double remainingAmount = booking.totalAmount - booking.paidAmount;
        
        if (remainingAmount > 0) {
          totalPaidAmount += remainingAmount;
          paidBookings.add(booking);
          
          Booking updatedBooking = booking.copyWith(
            paidAmount: booking.totalAmount, // تسديد المبلغ بالكامل
            status: BookingStatus.completed,
            updatedAt: DateTime.now(),
          );
          
          await _databaseHelper.updateBooking(updatedBooking);
        }
      }
      
      // إعادة تحميل البيانات
      await _loadCustomerData();
      
      // عرض حوار خيارات ما بعد التسديد الشامل
      _showPostAllPaymentsOptions(totalPaidAmount, paidBookings.length);
      
    } catch (e) {
      _showErrorDialog('خطأ في التسديد', 'حدث خطأ أثناء تسديد الديون: $e');
    }
  }

  void _showPostAllPaymentsOptions(double totalPaidAmount, int bookingsCount) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: AppColors.success),
            const SizedBox(width: 8),
            const Text('تم تسديد جميع الديون'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.success.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('عدد الحجوزات:'),
                      Text(
                        '$bookingsCount حجز',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('إجمالي المبلغ المسدد:'),
                      Text(
                        CurrencyFormatter.format(totalPaidAmount),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppColors.success,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'تم تسديد جميع الديون بنجاح!\nجميع الحجوزات الآن مكتملة.',
              style: TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Future<void> _exportCustomerDebtsToExcel() async {
    if (_bookingsWithDebt.isEmpty) {
      _showErrorSnackBar('لا توجد ديون لتصديرها');
      return;
    }

    try {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // إنشاء ملف Excel
      final excelFile = excel.Excel.createExcel();
      final sheet = excelFile['ديون_${widget.customer.name}'];

      // إضافة العناوين
      sheet.cell(excel.CellIndex.indexByString('A1')).value = excel.TextCellValue('رقم الحجز');
      sheet.cell(excel.CellIndex.indexByString('B1')).value = excel.TextCellValue('تاريخ الحجز');
      sheet.cell(excel.CellIndex.indexByString('C1')).value = excel.TextCellValue('نوع الخدمة');
      sheet.cell(excel.CellIndex.indexByString('D1')).value = excel.TextCellValue('المبلغ الإجمالي');
      sheet.cell(excel.CellIndex.indexByString('E1')).value = excel.TextCellValue('المبلغ المدفوع');
      sheet.cell(excel.CellIndex.indexByString('F1')).value = excel.TextCellValue('المبلغ المتبقي');

      // تنسيق العناوين
      for (int col = 0; col < 6; col++) {
        final cell = sheet.cell(excel.CellIndex.indexByColumnRow(columnIndex: col, rowIndex: 0));
        cell.cellStyle = excel.CellStyle(
          bold: true,
          fontSize: 12,
          horizontalAlign: excel.HorizontalAlign.Center,
          verticalAlign: excel.VerticalAlign.Center,
        );
      }

      // إضافة البيانات
      int rowIndex = 1;
      double totalDebt = 0;
      
      for (final booking in _bookingsWithDebt) {
        double remainingAmount = booking.totalAmount - booking.paidAmount;
        totalDebt += remainingAmount;
        
        sheet.cell(excel.CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: rowIndex)).value = 
            excel.TextCellValue(_formatBookingId(booking));
        sheet.cell(excel.CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: rowIndex)).value = 
            excel.TextCellValue(_formatDate(booking.bookingDate));
        sheet.cell(excel.CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: rowIndex)).value = 
            excel.TextCellValue(booking.serviceType);
        sheet.cell(excel.CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: rowIndex)).value = 
            excel.DoubleCellValue(booking.totalAmount);
        sheet.cell(excel.CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: rowIndex)).value = 
            excel.DoubleCellValue(booking.paidAmount);
        sheet.cell(excel.CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: rowIndex)).value = 
            excel.DoubleCellValue(remainingAmount);
        
        rowIndex++;
      }

      // إضافة صف الإجمالي
      sheet.cell(excel.CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: rowIndex)).value = 
          excel.TextCellValue('الإجمالي');
      sheet.cell(excel.CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: rowIndex)).value = 
          excel.DoubleCellValue(totalDebt);

      // تنسيق صف الإجمالي
      for (int col = 0; col < 6; col++) {
        final cell = sheet.cell(excel.CellIndex.indexByColumnRow(columnIndex: col, rowIndex: rowIndex));
        cell.cellStyle = excel.CellStyle(
          bold: true,
          fontSize: 12,
          horizontalAlign: excel.HorizontalAlign.Center,
          verticalAlign: excel.VerticalAlign.Center,
        );
      }

      // حفظ الملف
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateFormat('yyyy-MM-dd_HH-mm-ss').format(DateTime.now());
      final filePath = '${directory.path}/ديون_${widget.customer.name}_$timestamp.xlsx';
      
      final file = File(filePath);
      await file.writeAsBytes(excelFile.encode()!);

      // إغلاق مؤشر التحميل
      Navigator.of(context).pop();

      // عرض رسالة نجاح
      _showSuccessDialog('تم تصدير الملف بنجاح', 'تم حفظ الملف في:\n$filePath');
      
    } catch (e) {
      // إغلاق مؤشر التحميل
      Navigator.of(context).pop();
      
      // عرض رسالة خطأ
      _showErrorDialog('خطأ في التصدير', 'حدث خطأ أثناء تصدير الملف:\n$e');
    }
  }

  void _showSuccessDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: AppColors.success),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.error, color: AppColors.error),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.offWhite,
      appBar: AppBar(
        title: Text('تفاصيل العميل - ${widget.customer.name}'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          isScrollable: true,
          tabs: [
            const Tab(icon: Icon(Icons.person), text: 'المعلومات'),
            const Tab(icon: Icon(Icons.calendar_today), text: 'الحجوزات'),
            Tab(
              icon: const Icon(Icons.money_off),
              text: 'الديون (${CurrencyFormatter.format(_totalDebt)})',
            ),
            const Tab(icon: Icon(Icons.bar_chart), text: 'الإحصائيات'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildInfoTab(),
                _buildBookingsTab(),
                _buildDebtsTab(),
                _buildStatsTab(),
              ],
            ),
    );
  }

  Widget _buildInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // بطاقة معلومات العميل
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // صورة العميل/الحرف الأول
                  CircleAvatar(
                    radius: 50,
                    backgroundColor: AppColors.primaryBlue,
                    child: Text(
                      widget.customer.name.isNotEmpty ? widget.customer.name[0].toUpperCase() : '؟',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // اسم العميل
                  Text(
                    widget.customer.name,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 20),
                  
                  // معلومات الاتصال
                  _buildInfoRow(Icons.phone, 'رقم الهاتف', widget.customer.phone),
                  
                  if (widget.customer.hasEmail)
                    _buildInfoRow(Icons.email, 'البريد الإلكتروني', widget.customer.email!),
                  
                  if (widget.customer.hasAddress)
                    _buildInfoRow(Icons.location_on, 'العنوان', widget.customer.address!),
                  
                  if (widget.customer.hasNotes)
                    _buildInfoRow(Icons.note, 'ملاحظات', widget.customer.notes!),
                  
                  // تاريخ التسجيل
                  _buildInfoRow(
                    Icons.calendar_today,
                    'تاريخ التسجيل',
                    _formatDate(widget.customer.createdAt),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // بطاقة ملخص سريع
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'ملخص سريع',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildQuickStat('إجمالي الحجوزات', '${_bookings.length}', Icons.calendar_today),
                      _buildQuickStat('المبلغ المدفوع', CurrencyFormatter.format(_totalSpent), Icons.attach_money),
                      _buildQuickStat('إجمالي الديون', CurrencyFormatter.format(_totalDebt), Icons.money_off),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookingsTab() {
    if (_bookings.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.calendar_today_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد حجوزات',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'لم يقم هذا العميل بأي حجوزات بعد',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return AnimationLimiter(
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _bookings.length,
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 375),
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: _buildBookingCard(_bookings[index]),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDebtsTab() {
    if (_bookingsWithDebt.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle_outline,
              size: 64,
              color: AppColors.success,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد ديون متبقية',
              style: TextStyle(
                fontSize: 18,
                color: AppColors.success,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'هذا العميل لا يوجد عليه أي ديون',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // إجمالي الديون
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.error,
                AppColors.error.withOpacity(0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: AppColors.error.withOpacity(0.3),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.money_off,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'إجمالي الديون المتبقية',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          CurrencyFormatter.format(_totalDebt),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${_bookingsWithDebt.length} حجز',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // زر تسديد جميع الديون
              ElevatedButton.icon(
                onPressed: () => _payAllDebts(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: AppColors.error,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                icon: const Icon(Icons.payment, size: 20),
                label: const Text(
                  'تسديد جميع الديون',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              // زر تصدير Excel
              ElevatedButton.icon(
                onPressed: () => _exportCustomerDebtsToExcel(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: AppColors.primaryBlue,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                icon: const Icon(Icons.download, size: 20),
                label: const Text(
                  'تصدير إلى Excel',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
        
        // قائمة الديون
        Expanded(
          child: AnimationLimiter(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _bookingsWithDebt.length,
              itemBuilder: (context, index) {
                return AnimationConfiguration.staggeredList(
                  position: index,
                  duration: const Duration(milliseconds: 375),
                  child: SlideAnimation(
                    verticalOffset: 50.0,
                    child: FadeInAnimation(
                      child: _buildDebtCard(_bookingsWithDebt[index]),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // إحصائيات الحجوزات
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'إحصائيات الحجوزات',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'إجمالي الحجوزات',
                          '${_bookings.length}',
                          Icons.calendar_today,
                          AppColors.primaryBlue,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          'حجوزات مكتملة',
                          '$_completedBookings',
                          Icons.check_circle,
                          AppColors.success,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'حجوزات جارية',
                          '$_pendingBookings',
                          Icons.schedule,
                          AppColors.warning,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          'حجوزات ملغية',
                          '${_bookings.length - _completedBookings - _pendingBookings}',
                          Icons.cancel,
                          AppColors.error,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // إحصائيات مالية
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الإحصائيات المالية',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 20),
                  _buildFinancialStat('إجمالي المبلغ المدفوع', _totalSpent, AppColors.success),
                  const SizedBox(height: 12),
                  _buildFinancialStat('إجمالي الديون المتبقية', _totalDebt, AppColors.error),
                  const SizedBox(height: 12),
                  _buildFinancialStat(
                    'متوسط قيمة الحجز',
                    _bookings.isNotEmpty ? (_totalSpent + _totalDebt) / _bookings.length : 0,
                    AppColors.primaryBlue,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, color: AppColors.primaryBlue, size: 20),
          const SizedBox(width: 12),
          Text(
            '$label:',
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              color: AppColors.secondaryText,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 16,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStat(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: AppColors.primaryBlue, size: 28),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.primaryBlue,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.secondaryText,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildDebtCard(Booking booking) {
    double remainingAmount = booking.totalAmount - booking.paidAmount;
    double paidPercentage = (booking.paidAmount / booking.totalAmount) * 100;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: AppColors.error.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.error.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.money_off,
                    color: AppColors.error,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      FutureBuilder<String>(
                        future: booking.formattedReceiptNumber,
                        builder: (context, snapshot) {
                          return Text(
                            'حجز رقم: ${snapshot.data ?? "جاري التحميل..."}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          );
                        },
                      ),
                      Text(
                        _formatDate(booking.bookingDate),
                        style: const TextStyle(
                          color: AppColors.secondaryText,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.error.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    CurrencyFormatter.format(remainingAmount),
                    style: TextStyle(
                      color: AppColors.error,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // نوع الخدمة
            Row(
              children: [
                Icon(Icons.camera_alt, color: AppColors.primaryBlue, size: 18),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    booking.serviceType,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // تفاصيل الدفع
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.veryLightBlue,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'المبلغ الإجمالي:',
                        style: TextStyle(fontSize: 14),
                      ),
                      Text(
                        CurrencyFormatter.format(booking.totalAmount),
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 6),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'المبلغ المدفوع:',
                        style: TextStyle(fontSize: 14),
                      ),
                      Text(
                        CurrencyFormatter.format(booking.paidAmount),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: AppColors.success,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 6),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'المبلغ المتبقي:',
                        style: TextStyle(fontSize: 14),
                      ),
                      Text(
                        CurrencyFormatter.format(remainingAmount),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: AppColors.error,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 12),
            
            // شريط التقدم
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'نسبة الدفع:',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.secondaryText,
                      ),
                    ),
                    Text(
                      '${paidPercentage.toStringAsFixed(1)}%',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: AppColors.secondaryText,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: paidPercentage / 100,
                  backgroundColor: AppColors.lightGray,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    paidPercentage > 50 ? AppColors.success : AppColors.warning,
                  ),
                ),
              ],
            ),
            
            // تاريخ التسليم المتوقع
            if (booking.status == BookingStatus.inProgress) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.warning.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.schedule,
                      color: AppColors.warning,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'تاريخ التسليم المتوقع: ${_formatDate(booking.deliveryDate)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.warning,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            // أزرار الإجراءات
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _payRemainingDebt(booking),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.success,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    icon: const Icon(Icons.payment, size: 20),
                    label: const Text(
                      'تسديد الدين المتبقي',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _payPartialDebt(booking),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryBlue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    icon: const Icon(Icons.payment, size: 20),
                    label: const Text(
                      'دفع جزئي',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBookingCard(Booking booking) {
    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (booking.status) {
      case BookingStatus.completed:
        statusColor = AppColors.success;
        statusText = 'مكتمل';
        statusIcon = Icons.check_circle;
        break;
      case BookingStatus.inProgress:
        statusColor = AppColors.warning;
        statusText = 'جاري';
        statusIcon = Icons.schedule;
        break;
      case BookingStatus.cancelled:
        statusColor = AppColors.error;
        statusText = 'ملغي';
        statusIcon = Icons.cancel;
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.calendar_today, color: AppColors.primaryBlue, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'حجز رقم: ${_formatBookingId(booking)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        _formatDate(booking.bookingDate),
                        style: const TextStyle(
                          color: AppColors.secondaryText,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(statusIcon, color: statusColor, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        statusText,
                        style: TextStyle(
                          color: statusColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            Text(
              booking.serviceType,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.secondaryText,
              ),
            ),
            
            const SizedBox(height: 8),
            
            Row(
              children: [
                Icon(Icons.attach_money, color: AppColors.success, size: 18),
                const SizedBox(width: 4),
                Text(
                  'المبلغ الإجمالي: ${CurrencyFormatter.format(booking.totalAmount)}',
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
            
            const SizedBox(height: 4),
            
            Row(
              children: [
                Icon(Icons.payment, color: AppColors.primaryBlue, size: 18),
                const SizedBox(width: 4),
                Text(
                  'المبلغ المدفوع: ${CurrencyFormatter.format(booking.paidAmount)}',
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
            
            if (booking.totalAmount - booking.paidAmount > 0) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(Icons.pending, color: AppColors.warning, size: 18),
                  const SizedBox(width: 4),
                  Text(
                    'المبلغ المتبقي: ${CurrencyFormatter.format(booking.totalAmount - booking.paidAmount)}',
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ],
            
            if (booking.notes?.isNotEmpty == true) ...[
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.veryLightBlue,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  booking.notes!,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.secondaryText,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 28),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.secondaryText,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialStat(String title, double amount, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.attach_money, color: color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.secondaryText,
                  ),
                ),
                Text(
                  CurrencyFormatter.format(amount),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatBookingId(Booking booking) {
    // تنسيق رقم الحجز بالشكل YYYYMMDDNN (مثل: 2507200001)
    final date = booking.createdAt;
    final yyyy = date.year.toString(); // السنة كاملة (4 أرقام)
    final mm = date.month.toString().padLeft(2, '0'); // الشهر برقمين
    final dd = date.day.toString().padLeft(2, '0'); // اليوم برقمين
    
    // استخراج رقم تسلسلي من آخر رقمين من ID
    String sequential = booking.id.replaceAll(RegExp(r'[^0-9]'), '');
    if (sequential.isEmpty) {
      sequential = '01';
    } else {
      // أخذ آخر رقمين أو إضافة صفر في البداية
      int sequentialNum = int.tryParse(sequential.length >= 2 
        ? sequential.substring(sequential.length - 2) 
        : sequential) ?? 1;
      sequential = sequentialNum.toString().padLeft(2, '0');
    }
    
    return '$yyyy$mm$dd$sequential';
  }
}
