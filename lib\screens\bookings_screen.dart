import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../utils/app_colors.dart';
import '../utils/currency_formatter.dart';
import '../models/booking.dart';
import '../services/database_helper.dart';
import '../services/html_receipt_enhanced_service.dart';
import '../services/company_settings_service.dart';
import '../widgets/add_booking_dialog.dart';
import 'booking_details_screen.dart';

class BookingsScreen extends StatefulWidget {
  const BookingsScreen({super.key});

  @override
  State<BookingsScreen> createState() => _BookingsScreenState();
}

class _BookingsScreenState extends State<BookingsScreen> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final TextEditingController _searchController = TextEditingController();
  
  List<Booking> _allBookings = [];
  List<Booking> _filteredBookings = [];
  bool _isLoading = false;
  BookingStatus? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _loadBookings();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadBookings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _allBookings = await _databaseHelper.getAllBookings();
    } catch (e) {
      _allBookings = _getDemoBookings();
    }

    _filterBookings(_searchController.text);

    setState(() {
      _isLoading = false;
    });
  }

  List<Booking> _getDemoBookings() {
    return [

    ];
  }

  void _filterBookings(String query) {
    setState(() {
      var bookings = _allBookings;

      // تصفية حسب الحالة
      if (_selectedStatus != null) {
        bookings = bookings.where((b) => b.status == _selectedStatus).toList();
      }

      // تصفية حسب البحث
      if (query.isNotEmpty) {
        bookings = bookings.where((booking) =>
            booking.customerName.toLowerCase().contains(query.toLowerCase()) ||
            booking.customerPhone.contains(query) ||
            booking.serviceType.toLowerCase().contains(query.toLowerCase())).toList();
      }

      _filteredBookings = bookings;
    });
  }

  Future<void> _updateBookingStatus(Booking booking, BookingStatus newStatus) async {
    try {
      final updatedBooking = booking.copyWith(
        status: newStatus,
        updatedAt: DateTime.now(),
      );
      
      await _databaseHelper.updateBooking(updatedBooking);

      setState(() {
        final index = _allBookings.indexWhere((b) => b.id == booking.id);
        if (index != -1) {
          _allBookings[index] = updatedBooking;
        }
        _filterBookings(_searchController.text);
      });

      _showSnackBar('تم تحديث حالة الطلب بنجاح', AppColors.success);
    } catch (e) {
      _showSnackBar('حدث خطأ أثناء تحديث الطلب', AppColors.error);
    }
  }

  Future<void> _deleteBooking(Booking booking) async {
    // إظهار رسالة تأكيد
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('هل أنت متأكد من حذف هذا الحجز؟'),
            const SizedBox(height: 8),
            Text(
              'العميل: ${booking.customerName}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(
              'الخدمة: ${booking.serviceType}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'تحذير: لا يمكن التراجع عن هذا الإجراء!',
              style: TextStyle(
                color: AppColors.error,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _databaseHelper.deleteBooking(booking.id);

        setState(() {
          _allBookings.removeWhere((b) => b.id == booking.id);
          _filterBookings(_searchController.text);
        });

        _showSnackBar('تم حذف الحجز بنجاح', AppColors.success);
      } catch (e) {
        _showSnackBar('حدث خطأ أثناء حذف الحجز', AppColors.error);
      }
    }
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showAddBookingDialog() {
    showDialog(
      context: context,
      builder: (context) => AddBookingDialog(
        onBookingAdded: () => _loadBookings(),
      ),
    );
  }

  void _showEditBookingDialog(Booking booking) {
    showDialog(
      context: context,
      builder: (context) => AddBookingDialog(
        onBookingAdded: () => _loadBookings(),
        bookingToEdit: booking,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('الحجوزات'),
          elevation: 0,
          backgroundColor: AppColors.primaryBlue,
          foregroundColor: Colors.white,
          actions: [
          ],
          bottom: TabBar(
            onTap: (index) {
              setState(() {
                switch (index) {
                  case 0:
                    _selectedStatus = null;
                    break;
                  case 1:
                    _selectedStatus = BookingStatus.inProgress;
                    break;
                  case 2:
                    _selectedStatus = BookingStatus.completed;
                    break;
                }
                _filterBookings(_searchController.text);
              });
            },
            indicatorColor: Colors.white,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            tabs: const [
              Tab(icon: Icon(Icons.list), text: 'جميع الحجوزات'),
              Tab(icon: Icon(Icons.work), text: 'قيد العمل'),
              Tab(icon: Icon(Icons.check_circle), text: 'تم التسليم'),
            ],
          ),
        ),
        floatingActionButton: FloatingActionButton.extended(
          onPressed: _showAddBookingDialog,
          icon: const Icon(Icons.add),
          label: const Text('حجز جديد'),
          backgroundColor: AppColors.primaryBlue,
          foregroundColor: Colors.white,
        ),
        body: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [AppColors.veryLightBlue, AppColors.offWhite],
            ),
          ),
          child: Column(
            children: [
                // شريط البحث
              _buildSearchBar(),
              
              // إحصائيات
              _buildStatistics(),
              
              const SizedBox(height: 16),
              
              // قائمة الحجوزات
              Expanded(child: _buildBookingsList()),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: TextField(
            controller: _searchController,
            decoration: const InputDecoration(
              hintText: 'ابحث بالاسم، رقم الهاتف، أو نوع الخدمة',
              prefixIcon: Icon(Icons.search),
              border: InputBorder.none,
            ),
            onChanged: _filterBookings,
          ),
        ),
      ),
    );
  }

  Widget _buildStatistics() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'إجمالي الحجوزات',
              _allBookings.length.toString(),
              Icons.receipt_long,
              AppColors.primaryBlue,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'قيد العمل',
              _allBookings.where((b) => b.status == BookingStatus.inProgress).length.toString(),
              Icons.work,
              AppColors.warning,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              'تم التسليم',
              _allBookings.where((b) => b.status == BookingStatus.completed).length.toString(),
              Icons.check_circle,
              AppColors.success,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.secondaryText,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBookingsList() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_filteredBookings.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد حجوزات',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _filteredBookings.length,
      itemBuilder: (context, index) {
        final booking = _filteredBookings[index];
        return BookingCard(
          booking: booking,
          onStatusUpdate: (newStatus) => _updateBookingStatus(booking, newStatus),
          onDelete: () => _deleteBooking(booking),
          onEdit: _showEditBookingDialog,
          onTap: () => Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => BookingDetailsScreen(booking: booking),
            ),
          ),
        );
      },
    );
  }
}

class BookingCard extends StatefulWidget {
  final Booking booking;
  final Function(BookingStatus) onStatusUpdate;
  final VoidCallback onDelete;
  final Function(Booking) onEdit;
  final VoidCallback? onTap;

  const BookingCard({
    super.key,
    required this.booking,
    required this.onStatusUpdate,
    required this.onDelete,
    required this.onEdit,
    this.onTap,
  });

  @override
  State<BookingCard> createState() => _BookingCardState();
}

class _BookingCardState extends State<BookingCard> {
  bool _isExpanded = false;

  Color get _statusColor {
    switch (widget.booking.status) {
      case BookingStatus.inProgress:
        return AppColors.warning;
      case BookingStatus.completed:
        return AppColors.success;
      case BookingStatus.cancelled:
        return AppColors.error;
    }
  }

  IconData get _statusIcon {
    switch (widget.booking.status) {
      case BookingStatus.inProgress:
        return Icons.work;
      case BookingStatus.completed:
        return Icons.check_circle;
      case BookingStatus.cancelled:
        return Icons.cancel;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isOverdue = widget.booking.status == BookingStatus.inProgress &&
        widget.booking.deliveryDate.isBefore(DateTime.now());

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: widget.onTap != null ? 3 : 1,
      child: Column(
        children: [
          ListTile(
              onTap: widget.onTap ?? () => setState(() => _isExpanded = !_isExpanded),
              leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _statusColor,
                shape: BoxShape.circle,
              ),
              child: Icon(_statusIcon, color: Colors.white, size: 20),
            ),
            title: Row(
              children: [
                Expanded(
                  child: Text(
                    widget.booking.customerName,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _statusColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    widget.booking.status.arabicName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(widget.booking.serviceType),
                FutureBuilder<String>(
                  future: widget.booking.formattedReceiptNumber,
                  builder: (context, snapshot) {
                    return Text('رقم الطلب: ${snapshot.data ?? "جاري التحميل..."}');
                  },
                ),
                Row(
                  children: [
                    Icon(
                      isOverdue ? Icons.warning : Icons.schedule,
                      size: 16,
                      color: isOverdue ? AppColors.error : AppColors.secondaryText,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'التسليم: ${DateFormat('dd/MM/yyyy').format(widget.booking.deliveryDate)}',
                      style: TextStyle(
                        color: isOverdue ? AppColors.error : AppColors.secondaryText,
                        fontWeight: isOverdue ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // إضافة أيقونة لعرض التفاصيل
                if (widget.onTap != null)
                  IconButton(
                    onPressed: widget.onTap,
                    icon: const Icon(Icons.visibility),
                    tooltip: 'عرض التفاصيل',
                    iconSize: 20,
                  ),
                // زر التوسع
                IconButton(
                  onPressed: () => setState(() => _isExpanded = !_isExpanded),
                  icon: Icon(_isExpanded ? Icons.expand_less : Icons.expand_more),
                  tooltip: _isExpanded ? 'إخفاء التفاصيل' : 'عرض التفاصيل السريعة',
                ),
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert),
                  onSelected: (value) async {
                    switch (value) {
                      case 'edit':
                        // منع تعديل الحجوزات المكتملة
                        if (widget.booking.status == BookingStatus.completed) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('لا يمكن تعديل حجز تم تسليمه'),
                              backgroundColor: AppColors.warning,
                            ),
                          );
                          return;
                        }
                        widget.onEdit(widget.booking);
                        break;
                      case 'print':
                        await _printBookingReceipt(widget.booking);
                        break;
                      case 'complete':
                        widget.onStatusUpdate(BookingStatus.completed);
                        break;
                      case 'delete':
                        widget.onDelete();
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    // إخفاء خيار التعديل للحجوزات المكتملة
                    if (widget.booking.status != BookingStatus.completed)
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 18, color: AppColors.info),
                            SizedBox(width: 8),
                            Text('تعديل الحجز'),
                          ],
                        ),
                      ),
                    if (widget.booking.status != BookingStatus.completed)
                      const PopupMenuDivider(),
                    const PopupMenuItem(
                      value: 'print',
                      child: Row(
                        children: [
                          Icon(Icons.print, size: 18),
                          SizedBox(width: 8),
                          Text('طباعة الوصل'),
                        ],
                      ),
                    ),
                    const PopupMenuDivider(),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 18, color: AppColors.error),
                          SizedBox(width: 8),
                          Text('حذف الحجز', style: TextStyle(color: AppColors.error)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          if (_isExpanded) _buildExpandedContent(),
        ],
      ),
    );
  }

  Widget _buildExpandedContent() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Divider(),
          _buildDetailRow('رقم الهاتف', widget.booking.customerPhone),
          if (widget.booking.bookedBy != null && widget.booking.bookedBy!.isNotEmpty)
            _buildDetailRow('حجز بواسطة', widget.booking.bookedBy!),
          _buildDetailRow('تاريخ الحجز', DateFormat('dd/MM/yyyy').format(widget.booking.bookingDate)),
          _buildDetailRow('المبلغ الكلي', CurrencyFormatter.format(widget.booking.totalAmount)),
          if (widget.booking.discount > 0)
            _buildDetailRow('الخصم', CurrencyFormatter.format(widget.booking.discount), valueColor: AppColors.success),
          _buildDetailRow('العربون', CurrencyFormatter.format(widget.booking.paidAmount)),
          if (widget.booking.calculatedRemainingAmount > 0)
            _buildDetailRow(
              'المبلغ المتبقي',
              CurrencyFormatter.format(widget.booking.calculatedRemainingAmount),
              valueColor: AppColors.warning,
            ),
          if (widget.booking.notes != null && widget.booking.notes!.isNotEmpty)
            _buildDetailRow('ملاحظات', widget.booking.notes!),
          const SizedBox(height: 16),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                color: AppColors.secondaryText,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: valueColor ?? AppColors.primaryText,
                fontWeight: valueColor != null ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _printBookingReceipt(widget.booking),
                icon: const Icon(Icons.print),
                label: const Text('طباعة الوصل'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: widget.onDelete,
            icon: const Icon(Icons.delete),
            label: const Text('حذف الحجز'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.error,
              side: const BorderSide(color: AppColors.error),
            ),
          ),
        ),
      ],
    );
  }

  /// طباعة وصل الحجز باستخدام HtmlReceiptService المحسن
  Future<void> _printBookingReceipt(Booking booking) async {
    try {
      // الحصول على إعدادات الشركة
      final companySettings = await CompanySettingsService.getCompanySettings();
      
      // إنشاء الوصل كملف HTML باستخدام القالب الافتراضي
      final htmlPath = await HtmlReceiptService.generateHtmlReceipt(
        booking: booking,
        templateName: 'modern', // استخدام القالب الحديث كافتراضي
        receiptType: 'booking',
        customData: companySettings,
      );
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء الوصل بنجاح وحفظه كملف HTML'),
            backgroundColor: AppColors.success,
            duration: Duration(seconds: 2),
          ),
        );
        
        // فتح الملف في المتصفح
        await HtmlReceiptService.openHtmlFile(htmlPath);
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء الوصل: $e'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}