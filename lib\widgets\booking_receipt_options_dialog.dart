import 'package:flutter/material.dart';
import '../models/booking.dart';
import '../services/enhanced_print_service.dart';
import '../services/html_receipt_enhanced_service.dart';

class BookingReceiptOptionsDialog extends StatefulWidget {
  final Booking booking;
  final String receiptType;
  final double? paidAmount;

  const BookingReceiptOptionsDialog({
    super.key,
    required this.booking,
    this.receiptType = 'booking',
    this.paidAmount,
  });

  @override
  State<BookingReceiptOptionsDialog> createState() => _BookingReceiptOptionsDialogState();
}

class _BookingReceiptOptionsDialogState extends State<BookingReceiptOptionsDialog> {
  String _selectedFormat = 'html';
  String _selectedTemplate = 'modern';
  List<String> _availableTemplates = [];
  bool _isLoading = false;
  bool _generateBoth = false;

  @override
  void initState() {
    super.initState();
    _loadTemplates();
  }

  Future<void> _loadTemplates() async {
    try {
      final templates = await HtmlReceiptService.getAvailableTemplates();
      setState(() {
        _availableTemplates = templates;
        if (templates.isNotEmpty) {
          _selectedTemplate = templates.first;
        }
      });
    } catch (e) {
      // Use default if loading fails
      setState(() {
        _availableTemplates = ['modern', 'classic', 'minimal', 'elegant'];
        _selectedTemplate = 'modern';
      });
    }
  }

  Future<void> _generateReceipt() async {
    setState(() => _isLoading = true);

    try {
      Map<String, String?> results;
      
      if (_generateBoth) {
        // Generate both formats
        results = await EnhancedPrintService.generateReceipt(
          booking: widget.booking,
          receiptType: widget.receiptType,
          paidAmount: widget.paidAmount,
          generateHtml: true,
          generateImage: true,
          htmlTemplate: _selectedTemplate,
        );
      } else if (_selectedFormat == 'html') {
        // Generate HTML only
        results = await EnhancedPrintService.generateReceipt(
          booking: widget.booking,
          receiptType: widget.receiptType,
          paidAmount: widget.paidAmount,
          generateHtml: true,
          generateImage: false,
          htmlTemplate: _selectedTemplate,
        );
      } else {
        // Generate image only
        results = await EnhancedPrintService.generateReceipt(
          booking: widget.booking,
          receiptType: widget.receiptType,
          paidAmount: widget.paidAmount,
          generateHtml: false,
          generateImage: true,
        );
      }

      setState(() => _isLoading = false);

      if (mounted) {
        Navigator.pop(context);
        _showResultDialog(results);
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        _showError('خطأ في إنشاء الوصل: $e');
      }
    }
  }

  void _showResultDialog(Map<String, String?> results) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تم إنشاء الوصل بنجاح'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (results['html'] != null)
              ListTile(
                leading: const Icon(Icons.web, color: Colors.blue),
                title: const Text('وصل HTML'),
                subtitle: Text(results['html']!.split('/').last),
                trailing: IconButton(
                  icon: const Icon(Icons.open_in_browser),
                  onPressed: () async {
                    try {
                      await EnhancedPrintService.openReceipt(results['html']!);
                    } catch (e) {
                      _showError('خطأ في فتح الملف: $e');
                    }
                  },
                ),
              ),
            if (results['image'] != null)
              ListTile(
                leading: const Icon(Icons.image, color: Colors.green),
                title: const Text('وصل صورة'),
                subtitle: Text(results['image']!.split('/').last),
                trailing: IconButton(
                  icon: const Icon(Icons.visibility),
                  onPressed: () async {
                    try {
                      await EnhancedPrintService.openReceipt(results['image']!);
                    } catch (e) {
                      _showError('خطأ في فتح الملف: $e');
                    }
                  },
                ),
              ),
            if (results['pdf'] != null)
              ListTile(
                leading: const Icon(Icons.picture_as_pdf, color: Colors.red),
                title: const Text('وصل PDF'),
                subtitle: Text(results['pdf']!.split('/').last),
                trailing: IconButton(
                  icon: const Icon(Icons.open_in_new),
                  onPressed: () async {
                    try {
                      await EnhancedPrintService.openReceipt(results['pdf']!);
                    } catch (e) {
                      _showError('خطأ في فتح الملف: $e');
                    }
                  },
                ),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  String _getTemplateDisplayName(String templateName) {
    switch (templateName) {
      case 'modern':
        return 'حديث';
      case 'classic':
        return 'كلاسيكي';
      case 'minimal':
        return 'بسيط';
      case 'elegant':
        return 'أنيق';
      default:
        return templateName;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('خيارات ${widget.receiptType == 'payment' ? 'وصل الدفع' : 'وصل الحجز'}'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Format selection
            const Text(
              'تنسيق الوصل:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Column(
              children: [
                RadioListTile<String>(
                  title: const Text('HTML (ويب)'),
                  subtitle: const Text('قابل للتخصيص ويمكن فتحه في المتصفح'),
                  value: 'html',
                  groupValue: _selectedFormat,
                  onChanged: (value) {
                    setState(() {
                      _selectedFormat = value!;
                      _generateBoth = false;
                    });
                  },
                ),
                RadioListTile<String>(
                  title: const Text('صورة (PNG)'),
                  subtitle: const Text('جودة عالية وسهولة في المشاركة'),
                  value: 'image',
                  groupValue: _selectedFormat,
                  onChanged: (value) {
                    setState(() {
                      _selectedFormat = value!;
                      _generateBoth = false;
                    });
                  },
                ),
              ],
            ),
            
            // Both formats option
            CheckboxListTile(
              title: const Text('إنشاء كلا التنسيقين'),
              subtitle: const Text('HTML + صورة'),
              value: _generateBoth,
              onChanged: (value) {
                setState(() => _generateBoth = value ?? false);
              },
            ),
            
            const SizedBox(height: 16),
            
            // Template selection (only for HTML)
            if (_selectedFormat == 'html' || _generateBoth)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'قالب HTML:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String>(
                    value: _selectedTemplate,
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => _selectedTemplate = value);
                      }
                    },
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    items: _availableTemplates.map((template) {
                      return DropdownMenuItem(
                        value: template,
                        child: Text(_getTemplateDisplayName(template)),
                      );
                    }).toList(),
                  ),
                ],
              ),
            
            const SizedBox(height: 16),
            
            // Customer info preview
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معلومات الوصل:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text('العميل: ${widget.booking.customerName}'),
                    Text('الخدمة: ${widget.booking.serviceType}'),
                    if (widget.receiptType == 'payment' && widget.paidAmount != null)
                      Text('المبلغ المدفوع: ${widget.paidAmount!.toStringAsFixed(2)} ر.س')
                    else
                      Text('إجمالي المبلغ: ${widget.booking.totalAmount.toStringAsFixed(2)} ر.س'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton.icon(
          onPressed: _isLoading ? null : _generateReceipt,
          icon: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.receipt),
          label: Text(_isLoading ? 'جاري الإنشاء...' : 'إنشاء الوصل'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }
}
