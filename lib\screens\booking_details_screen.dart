import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../utils/app_colors.dart';
import '../utils/currency_formatter.dart';
import '../models/booking.dart';
import '../services/html_receipt_enhanced_service.dart';
import '../services/company_settings_service.dart';
import '../services/database_helper.dart';
import '../widgets/booking_receipt_options_dialog.dart';

class BookingDetailsScreen extends StatefulWidget {
  final Booking booking;

  const BookingDetailsScreen({
    super.key,
    required this.booking,
  });

  @override
  State<BookingDetailsScreen> createState() => _BookingDetailsScreenState();
}

class _BookingDetailsScreenState extends State<BookingDetailsScreen> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final Map<String, double> _offerPrices = {};
  final Map<String, double> _additionPrices = {};

  @override
  void initState() {
    super.initState();
    _loadServicePrices();
  }

  Future<void> _loadServicePrices() async {
    try {
      final services = await _databaseHelper.getAllServices();
      
      for (final service in services) {
        final name = service['name'] ?? '';
        final price = (service['price'] ?? 0).toDouble();
        final category = service['category'] ?? '';
        
        // تصنيف الخدمات حسب النوع
        if (category.startsWith('إضافة')) {
          _additionPrices[name] = price;
        } else {
          _offerPrices[name] = price;
        }
      }
    } catch (e) {
      // في حالة الخطأ، استخدم قيم افتراضية
      debugPrint('خطأ في تحميل أسعار الخدمات: $e');
    }
    
    if (mounted) {
      setState(() {});
    }
  }

  Booking get booking => widget.booking;

  double _calculateOffersAndAdditionsTotal() {
    double total = 0.0;
    
    // حساب مجموع العروض
    if (booking.selectedOffers != null) {
      for (final offer in booking.selectedOffers!) {
        total += _offerPrices[offer] ?? 0.0;
      }
    }
    
    // حساب مجموع الإضافات
    if (booking.additions != null) {
      for (final addition in booking.additions!) {
        total += _additionPrices[addition] ?? 0.0;
      }
    }
    
    return total;
  }

  /// استخراج اسم الخدمة من النص
  String _extractServiceName(String serviceText) {
    // تنسيق جديد: "اسم الخدمة - 250000 د.ع"
    final newFormatPattern = RegExp(r'^(.+?)\s*-\s*(\d+(?:\.\d+)?)\s*(د\.ع)\s*$');
    final newFormatMatch = newFormatPattern.firstMatch(serviceText);
    
    if (newFormatMatch != null) {
      return newFormatMatch.group(1)?.trim() ?? serviceText;
    }
    
    // تنسيق قديم: "اسم الخدمة - وصف - سعر د.ع"
    final parts = serviceText.split(' - ');
    if (parts.isNotEmpty) {
      return parts[0].trim();
    }
    
    return serviceText.trim();
  }

  /// استخراج وصف الخدمة من النص
  String _extractServiceDescription(String serviceText) {
    // تنسيق جديد: "اسم الخدمة - 250000 د.ع" (لا يحتوي على وصف)
    final newFormatPattern = RegExp(r'^(.+?)\s*-\s*(\d+(?:\.\d+)?)\s*(د\.ع)\s*$');
    final newFormatMatch = newFormatPattern.firstMatch(serviceText);
    
    if (newFormatMatch != null) {
      return ''; // لا يوجد وصف في التنسيق الجديد
    }
    
    // تنسيق قديم: "اسم الخدمة - وصف - سعر د.ع"
    final parts = serviceText.split(' - ');
    
    if (parts.length >= 2) {
      // فحص الجزء الأخير للسعر
      final lastPart = parts.last.trim();
      final pricePattern = RegExp(r'^(\d+(?:\.\d+)?)\s*(د\.ع|ريال|ر\.س|دينار)?\s*$');
      final priceMatch = pricePattern.firstMatch(lastPart);
      
      if (priceMatch != null && parts.length > 2) {
        // يوجد سعر في الجزء الأخير والوصف في المنتصف
        return parts.sublist(1, parts.length - 1).join(' - ').trim();
      } else if (priceMatch == null) {
        // لا يوجد سعر في الجزء الأخير - كل شيء بعد الاسم هو وصف
        return parts.sublist(1).join(' - ').trim();
      }
    }
    
    return '';
  }

  /// استخراج السعر من النص
  String? _extractPrice(String serviceText) {
    // تنسيق جديد: "اسم الخدمة - 250000 د.ع"
    final newFormatPattern = RegExp(r'^(.+?)\s*-\s*(\d+(?:\.\d+)?)\s*(د\.ع)\s*$');
    final newFormatMatch = newFormatPattern.firstMatch(serviceText);
    
    if (newFormatMatch != null) {
      final priceValue = newFormatMatch.group(2)?.trim() ?? '0';
      final currency = newFormatMatch.group(3)?.trim() ?? 'د.ع';
      return '$priceValue $currency';
    }
    
    // تنسيق قديم: "اسم الخدمة - وصف - سعر د.ع"
    final parts = serviceText.split(' - ');
    
    if (parts.length >= 2) {
      final lastPart = parts.last.trim();
      final pricePattern = RegExp(r'^(\d+(?:\.\d+)?)\s*(د\.ع|ريال|ر\.س|دينار)?\s*$');
      final priceMatch = pricePattern.firstMatch(lastPart);
      
      if (priceMatch != null) {
        final priceValue = priceMatch.group(1)?.trim() ?? '0';
        final currency = priceMatch.group(2)?.trim() ?? 'د.ع';
        return '$priceValue $currency';
      }
    }
    
    return null;
  }

  Color get _statusColor {
    switch (booking.status) {
      case BookingStatus.inProgress:
        return AppColors.warning;
      case BookingStatus.completed:
        return AppColors.success;
      case BookingStatus.cancelled:
        return AppColors.error;
    }
  }

  IconData get _statusIcon {
    switch (booking.status) {
      case BookingStatus.inProgress:
        return Icons.work;
      case BookingStatus.completed:
        return Icons.check_circle;
      case BookingStatus.cancelled:
        return Icons.cancel;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isOverdue = booking.status == BookingStatus.inProgress &&
        booking.deliveryDate.isBefore(DateTime.now());

    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل الحجز'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () async {
              // Show new receipt options dialog
              showDialog(
                context: context,
                builder: (context) => BookingReceiptOptionsDialog(
                  booking: booking,
                  receiptType: 'booking',
                ),
              );
            },
            icon: const Icon(Icons.print),
            tooltip: 'طباعة الوصل',
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [AppColors.veryLightBlue, AppColors.offWhite],
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Section
              _buildHeaderSection(isOverdue),
              
              const SizedBox(height: 16),
              
              // Customer Information
              _buildCustomerSection(),
              
              const SizedBox(height: 16),
              
              // Service Details
              _buildServiceSection(),
              
              const SizedBox(height: 16),
              
              // Financial Details
              _buildFinancialSection(),
              
              const SizedBox(height: 16),
              
              // Dates Information
              _buildDatesSection(isOverdue),
              
              const SizedBox(height: 16),
              
              // Additional Information
              if (booking.bookedBy != null || booking.notes != null)
                _buildAdditionalInfoSection(),
              
              const SizedBox(height: 24),
              
              // Action Buttons
              _buildActionButtons(context),
              
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderSection(bool isOverdue) {
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [_statusColor.withOpacity(0.1), _statusColor.withOpacity(0.05)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _statusColor,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(_statusIcon, color: Colors.white, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      FutureBuilder<String>(
                        future: booking.formattedReceiptNumber,
                        builder: (context, snapshot) {
                          return Text(
                            'حجز رقم: ${snapshot.data ?? "..."}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppColors.primaryText,
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: _statusColor,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          booking.status.arabicName,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (isOverdue) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.error.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.warning, color: AppColors.error, size: 20),
                    const SizedBox(width: 8),
                    const Text(
                      'تحذير: تجاوز موعد التسليم',
                      style: TextStyle(
                        color: AppColors.error,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.person, color: AppColors.primaryBlue, size: 24),
                const SizedBox(width: 12),
                const Text(
                  'بيانات العميل',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryText,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildDetailRow('اسم العميل', booking.customerName, Icons.person_outline),
            _buildDetailRow('رقم الهاتف', booking.customerPhone, Icons.phone),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.camera_alt, color: AppColors.primaryBlue, size: 24),
                const SizedBox(width: 12),
                const Text(
                  'تفاصيل الخدمة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryText,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildDetailRow('نوع الخدمة', booking.serviceType, Icons.work_outline),
            if (booking.hallName != null && booking.hallName!.isNotEmpty)
              _buildDetailRow('اسم القاعة', booking.hallName!, Icons.location_city),
            if (booking.eventDescription != null && booking.eventDescription!.isNotEmpty)
              _buildDetailRow('وصف المناسبة', booking.eventDescription!, Icons.description),
            if (booking.eventDate != null)
              _buildDetailRow('تاريخ المناسبة', DateFormat('dd/MM/yyyy').format(booking.eventDate!), Icons.event),
            
            // Selected Offers
            if (booking.selectedOffers != null && booking.selectedOffers!.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text(
                'العروض المختارة:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryText,
                ),
              ),
              const SizedBox(height: 8),
              ...booking.selectedOffers!.map((offer) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.success.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AppColors.success.withOpacity(0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.local_offer, size: 18, color: AppColors.success),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              _extractServiceName(offer),
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: AppColors.success,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              _extractPrice(offer) ?? (_offerPrices[_extractServiceName(offer)] != null 
                                ? CurrencyFormatter.format(_offerPrices[_extractServiceName(offer)]!)
                                : '...'),
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                      if (_extractServiceDescription(offer).isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.7),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.info_outline, size: 16, color: AppColors.secondaryText),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _extractServiceDescription(offer),
                                  style: const TextStyle(
                                    color: AppColors.secondaryText,
                                    fontSize: 12,
                                    fontStyle: FontStyle.italic,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              )),
            ],
            
            // Selected Additions
            if (booking.additions != null && booking.additions!.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text(
                'الإضافات:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryText,
                ),
              ),
              const SizedBox(height: 8),
              ...booking.additions!.map((addition) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.info.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AppColors.info.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.add_circle_outline, size: 18, color: AppColors.info),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          addition,
                          style: const TextStyle(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppColors.info,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _additionPrices[addition] != null 
                            ? CurrencyFormatter.format(_additionPrices[addition]!)
                            : '...',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              )),
            ],
            
            // إضافة مجموع العروض والإضافات إذا كان متاحاً
            if ((booking.selectedOffers != null && booking.selectedOffers!.isNotEmpty) ||
                (booking.additions != null && booking.additions!.isNotEmpty)) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.primaryBlue.withOpacity(0.3)),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'مجموع العروض والإضافات:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryText,
                      ),
                    ),
                    Text(
                      CurrencyFormatter.format(_calculateOffersAndAdditionsTotal()),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryBlue,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.payments, color: AppColors.primaryBlue, size: 24),
                const SizedBox(width: 12),
                const Text(
                  'التفاصيل المالية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryText,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildFinancialRow('المبلغ الكلي', CurrencyFormatter.format(booking.totalAmount), AppColors.primaryText),
            if (booking.discount > 0)
              _buildFinancialRow('الخصم', CurrencyFormatter.format(booking.discount), AppColors.success),
            _buildFinancialRow('المبلغ بعد الخصم', CurrencyFormatter.format(booking.totalAmount - booking.discount), AppColors.primaryText),
            _buildFinancialRow('العربون المدفوع', CurrencyFormatter.format(booking.paidAmount), AppColors.info),
            const Divider(),
            _buildFinancialRow(
              'المبلغ المتبقي',
              CurrencyFormatter.format(booking.calculatedRemainingAmount),
              booking.calculatedRemainingAmount > 0 ? AppColors.warning : AppColors.success,
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDatesSection(bool isOverdue) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.schedule, color: AppColors.primaryBlue, size: 24),
                const SizedBox(width: 12),
                const Text(
                  'التواريخ المهمة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryText,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildDetailRow('تاريخ الحجز', DateFormat('dd/MM/yyyy - hh:mm a').format(booking.bookingDate), Icons.today),
            if (booking.eventDate != null)
              _buildDetailRow('تاريخ المناسبة', DateFormat('dd/MM/yyyy').format(booking.eventDate!), Icons.event),
            _buildDetailRow(
              'موعد التسليم',
              DateFormat('dd/MM/yyyy').format(booking.deliveryDate),
              isOverdue ? Icons.warning : Icons.schedule,
              valueColor: isOverdue ? AppColors.error : null,
            ),
            if (booking.updatedAt != null)
              _buildDetailRow('آخر تحديث', DateFormat('dd/MM/yyyy - hh:mm a').format(booking.updatedAt!), Icons.update),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info, color: AppColors.primaryBlue, size: 24),
                const SizedBox(width: 12),
                const Text(
                  'معلومات إضافية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryText,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (booking.bookedBy != null && booking.bookedBy!.isNotEmpty)
              _buildDetailRow('محجوز بواسطة', booking.bookedBy!, Icons.person_pin),
            if (booking.notes != null && booking.notes!.isNotEmpty)
              _buildDetailRow('ملاحظات', booking.notes!, Icons.note),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () async {
              try {
                // الحصول على إعدادات الشركة
                final companySettings = await CompanySettingsService.getCompanySettings();
                
                // إنشاء الوصل كملف HTML باستخدام الخدمة المحسنة
                final htmlPath = await HtmlReceiptService.generateHtmlReceipt(
                  booking: booking,
                  templateName: 'modern', // القالب الافتراضي
                  receiptType: 'booking',
                  customData: companySettings,
                );
                
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تم حفظ وصل الحجز في: $htmlPath'),
                      backgroundColor: AppColors.success,
                      duration: const Duration(seconds: 3),
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  // Check if it's a template-related error
                  final errorMessage = e.toString();
                  if (errorMessage.contains('يجب إنشاء قالب طباعة أولاً')) {
                    _showTemplateRequiredDialog();
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('خطأ في حفظ الوصل: $e'),
                        backgroundColor: AppColors.error,
                      ),
                    );
                  }
                }
              }
            },
            icon: const Icon(Icons.save_alt),
            label: const Text('حفظ وصل الحجز كصورة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.all(16),
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back),
            label: const Text('العودة للحجوزات'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.primaryBlue,
              side: const BorderSide(color: AppColors.primaryBlue),
              padding: const EdgeInsets.all(16),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: AppColors.secondaryText),
          const SizedBox(width: 12),
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                color: AppColors.secondaryText,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: valueColor ?? AppColors.primaryText,
                fontWeight: valueColor != null ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialRow(String label, String value, Color valueColor, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: AppColors.secondaryText,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              fontSize: isTotal ? 16 : 14,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: valueColor,
              fontWeight: FontWeight.bold,
              fontSize: isTotal ? 18 : 16,
            ),
          ),
        ],
      ),
    );
  }

  void _showTemplateRequiredDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: const Icon(
          Icons.warning_amber_rounded,
          color: Colors.orange,
          size: 64,
        ),
        title: const Text('مطلوب قالب طباعة'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'لا يمكن طباعة الوصل بدون قالب طباعة.',
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 12),
            Text(
              'يجب إنشاء قالب طباعة أولاً من إعدادات القوالب.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // الطباعة مباشرة باستخدام HTML
              HtmlReceiptService.generateHtmlReceipt(
                booking: widget.booking,
                templateName: 'default',
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: Colors.white,
            ),
            child: const Text('إعدادات القوالب'),
          ),
        ],
      ),
    );
  }
}
