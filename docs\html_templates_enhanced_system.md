# 🎨 نظام قوالب HTML المحسن لطباعة الوصولات

## 📋 نظرة عامة

تم تحديث نظام قوالب HTML ليستخدم ملفات منفصلة `index.html` و `styles.css` لكل قالب، مما يوفر:
- **تنظيم أفضل**: ملفات HTML و CSS منفصلة
- **سهولة التخصيص**: تعديل التصميم دون تعقيد
- **مرونة كاملة**: استخدام CSS متقدم
- **قوالب احترافية**: 4 تصاميم جاهزة

## 🏗️ بنية النظام الجديدة

```
assets/templates/
├── modern/
│   ├── index.html      # قالب HTML الحديث
│   └── styles.css      # أنماط CSS الحديثة
├── classic/
│   ├── index.html      # قالب HTML الكلاسيكي
│   └── styles.css      # أنماط CSS الكلاسيكية
├── minimal/
│   ├── index.html      # قالب HTML البسيط
│   └── styles.css      # أنماط CSS البسيطة
└── elegant/
    ├── index.html      # قالب HTML الأنيق
    └── styles.css      # أنماط CSS الأنيقة
```

## 🎯 المميزات الجديدة

### 1. خدمة محسنة (`HtmlReceateService`)
- تحميل القوالب من الأصول (assets)
- دمج CSS مع HTML تلقائياً
- نظام احتياط للقوالب المدمجة
- دعم القوالب المخصصة

### 2. قوالب محسنة
- **HTML5 حديث**: استخدام عناصر HTML5 الدلالية
- **CSS3 متقدم**: تأثيرات وتدرجات جميلة
- **تصميم متجاوب**: يعمل على جميع الأحجام
- **طباعة محسنة**: أنماط خاصة للطباعة

### 3. نظام متغيرات شامل
- معلومات الشركة والعميل
- تفاصيل الخدمة والدفع
- بلوكات شرطية ذكية
- تنسيق تلقائي للقوائم

## 🎨 القوالب المتاحة

### 1. القالب الحديث (Modern)
**الملف:** `assets/templates/modern/`
```html
<!-- index.html -->
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="styles.css">
</head>
<!-- ... بقية الكود -->
```

**المميزات:**
- ✨ تدرجات لونية جميلة (أزرق - بنفسجي)
- 🎯 بطاقات معلومات منظمة
- 🎭 تأثيرات بصرية متحركة
- 📱 تصميم متجاوب كامل

### 2. القالب الكلاسيكي (Classic)
**الملف:** `assets/templates/classic/`
```html
<!-- index.html مع جداول منظمة -->
<table class="info-table">
    <thead>
        <tr><th colspan="2">معلومات العميل</th></tr>
    </thead>
    <!-- ... -->
</table>
```

**المميزات:**
- 📊 جداول منظمة وواضحة
- 🖋️ خط Amiri الكلاسيكي
- 🔲 حدود مزدوجة تقليدية
- 🖨️ مثالي للطباعة

### 3. القالب البسيط (Minimal)
**الملف:** `assets/templates/minimal/`
```html
<!-- index.html بتصميم نظيف -->
<section class="section">
    <h3 class="section-title">العميل</h3>
    <div class="info-row">
        <!-- ... -->
    </div>
</section>
```

**المميزات:**
- 🤍 تصميم نظيف وبسيط
- ⚡ سرعة تحميل عالية
- 📝 مساحات بيضاء متوازنة
- 🎯 تركيز على المحتوى

### 4. القالب الأنيق (Elegant)
**الملف:** `assets/templates/elegant/`
```html
<!-- index.html بتصميم راقي -->
<div class="background">
    <div class="receipt">
        <div class="header">
            <!-- خلفية متدرجة داكنة -->
        </div>
        <div class="content">
            <!-- بطاقات شفافة أنيقة -->
        </div>
    </div>
</div>
```

**المميزات:**
- 🌌 خلفية متدرجة داكنة
- 💎 بطاقات شفافة أنيقة
- ✨ تأثيرات ظلال جميلة
- 🎭 مناسب للمناسبات الخاصة

## 🔧 كيفية الاستخدام

### 1. إنشاء وصل HTML
```dart
final htmlPath = await HtmlReceateService.generateHtmlReceipt(
  booking: booking,
  templateName: 'modern', // أو classic, minimal, elegant
  receiptType: 'booking',
  customData: {
    'companyName': 'استوديو الذكريات الجميلة',
    'companyLogo': '📸',
    'companyPhone': '0555123456',
    'companyEmail': '<EMAIL>',
  },
);
```

### 2. الحصول على قائمة القوالب
```dart
final templates = await HtmlReceateService.getAvailableTemplates();
// النتيجة: ['modern', 'classic', 'minimal', 'elegant', ...القوالب المخصصة]
```

### 3. إنشاء قالب مخصص
```dart
await HtmlReceateService.saveCustomTemplate(
  'my_template',
  htmlContent, // محتوى HTML كامل مع CSS مدمج
);
```

### 4. فتح الوصل في المتصفح
```dart
await HtmlReceateService.openHtmlFile(htmlPath);
```

## 🎛️ المتغيرات المتاحة

### معلومات أساسية
```html
{{COMPANY_NAME}}        <!-- اسم الشركة -->
{{COMPANY_LOGO}}        <!-- شعار الشركة -->
{{COMPANY_PHONE}}       <!-- هاتف الشركة -->
{{COMPANY_EMAIL}}       <!-- بريد الشركة -->

{{RECEIPT_TITLE}}       <!-- عنوان الوصل -->
{{RECEIPT_NUMBER}}      <!-- رقم الوصل -->
{{PRINT_DATE}}          <!-- تاريخ الطباعة -->
{{PRINT_TIME}}          <!-- وقت الطباعة -->
```

### معلومات العميل والخدمة
```html
{{CUSTOMER_NAME}}       <!-- اسم العميل -->
{{CUSTOMER_PHONE}}      <!-- هاتف العميل -->
{{SERVICE_TYPE}}        <!-- نوع الخدمة -->
{{BOOKING_DATE}}        <!-- تاريخ الحجز -->
{{DELIVERY_DATE}}       <!-- تاريخ التسليم -->
{{EVENT_DESCRIPTION}}   <!-- وصف المناسبة -->
```

### معلومات مالية
```html
{{TOTAL_AMOUNT}}        <!-- إجمالي المبلغ -->
{{TOTAL_LABEL}}         <!-- تسمية المجموع -->
{{DEPOSIT}}             <!-- العربون -->
{{REMAINING_AMOUNT}}    <!-- المبلغ المتبقي -->
```

### حالة ومعلومات إضافية
```html
{{STATUS_TEXT}}         <!-- نص الحالة -->
{{STATUS_CLASS}}        <!-- فئة CSS للحالة -->
{{STATUS_BADGE}}        <!-- شارة الحالة كاملة -->
{{NOTES}}               <!-- الملاحظات -->
```

### قوائم الخدمات
```html
{{SELECTED_OFFERS}}     <!-- العروض المختارة (HTML) -->
{{SELECTED_OFFERS_TEXT}} <!-- العروض المختارة (نص) -->
{{ADDITIONS}}           <!-- الإضافات (HTML) -->
{{ADDITIONS_TEXT}}      <!-- الإضافات (نص) -->
```

## 🔀 البلوكات الشرطية

### استخدام الشروط
```html
{{#if DEPOSIT}}
<div class="deposit-section">
    <p>العربون: {{DEPOSIT}} ر.س</p>
    <p>المتبقي: {{REMAINING_AMOUNT}} ر.س</p>
</div>
{{/if}}

{{#if NOTES}}
<div class="notes">
    <h4>ملاحظات</h4>
    <p>{{NOTES}}</p>
</div>
{{/if}}

{{#if SELECTED_OFFERS}}
<div class="services">
    <h4>الخدمات المختارة</h4>
    {{SELECTED_OFFERS}}
</div>
{{/if}}
```

### الشروط المتاحة
- `{{#if DEPOSIT}}...{{/if}}`
- `{{#if NOTES}}...{{/if}}`
- `{{#if EVENT_DESCRIPTION}}...{{/if}}`
- `{{#if SELECTED_OFFERS}}...{{/if}}`
- `{{#if ADDITIONS}}...{{/if}}`

## 🎨 إنشاء قالب مخصص

### 1. بنية HTML الأساسية
```html
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{RECEIPT_TITLE}}</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="receipt">
        <!-- محتوى القالب هنا -->
        <header class="header">
            <h1>{{COMPANY_NAME}}</h1>
            <h2>{{RECEIPT_TITLE}}</h2>
        </header>
        
        <main class="content">
            <!-- أقسام المحتوى -->
        </main>
        
        <footer class="footer">
            <!-- معلومات التذييل -->
        </footer>
    </div>
</body>
</html>
```

### 2. ملف CSS منفصل
```css
/* styles.css */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

body {
    font-family: 'Cairo', Arial, sans-serif;
    direction: rtl;
    /* باقي الأنماط */
}

.receipt {
    max-width: 800px;
    margin: 0 auto;
    /* أنماط الوصل */
}

/* أنماط للطباعة */
@media print {
    body {
        background: white !important;
    }
    
    .receipt {
        box-shadow: none !important;
    }
}
```

### 3. تصميم متجاوب
```css
/* أنماط الهواتف */
@media (max-width: 768px) {
    .receipt {
        padding: 15px;
    }
    
    .header {
        padding: 20px 15px;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
}
```

## 🚀 المزايا الجديدة

### 1. الأداء
- ⚡ تحميل أسرع للقوالب
- 💾 ذاكرة تخزين محسنة
- 🔄 نظام احتياط ذكي

### 2. المرونة
- 🎨 تحكم كامل في التصميم
- 🔧 سهولة التخصيص
- 📱 دعم شامل للأجهزة

### 3. الجودة
- 🎯 قوالب احترافية
- ✅ كود نظيف ومنظم
- 🖨️ جودة طباعة عالية

## 📈 إحصائيات النظام

```dart
// الحصول على إحصائيات الوصولات
final stats = await HtmlReceateService.getReceiptStats();
print('إجمالي الوصولات: ${stats['total']}');
print('هذا الشهر: ${stats['thisMonth']}');
print('هذا الأسبوع: ${stats['thisWeek']}');
```

## 🧹 إدارة الملفات

```dart
// تنظيف الوصولات القديمة (أكثر من 30 يوماً)
await HtmlReceateService.cleanupOldReceipts(daysToKeep: 30);
```

## 🎯 الخطوات التالية

### التحسينات المقترحة:
1. **تصدير PDF**: إضافة إمكانية تحويل HTML إلى PDF
2. **قوالب ديناميكية**: قوالب تتغير حسب نوع الخدمة
3. **معاينة مباشرة**: معاينة القوالب أثناء التعديل
4. **مكتبة قوالب**: مجموعة أكبر من القوالب الجاهزة

### ملاحظات مهمة:
- ✅ النظام متوافق مع الإصدارات السابقة
- ✅ يدعم جميع أنواع الوصولات الحالية
- ✅ سهل التطوير والتخصيص
- ✅ موثق بالكامل ومختبر

---

**النتيجة:** نظام قوالب HTML احترافي ومرن يوفر تصاميم جميلة وقابلة للتخصيص بالكامل! 🎉
