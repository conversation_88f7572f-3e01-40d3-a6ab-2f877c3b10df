class CurrencyFormatter {
  static const String currencySymbol = 'د.ع';
  static const String currencyName = 'دينار عراقي';
  static const String currencyCode = 'IQD';

  /// Format a number as Iraqi Dinar currency
  /// Example: 25000 -> "25,000 د.ع"
  static String format(double? amount, {bool showSymbol = true}) {
    if (amount == null || amount == 0) {
      return showSymbol ? '0 $currencySymbol' : '0';
    }

    // Convert to integer if it's a whole number
    String formattedAmount;
    if (amount == amount.toInt()) {
      formattedAmount = _addCommas(amount.toInt().toString());
    } else {
      formattedAmount = _addCommas(amount.toStringAsFixed(2));
    }

    return showSymbol ? '$formattedAmount $currencySymbol' : formattedAmount;
  }

  /// Format an integer as Iraqi Dinar currency
  /// Example: 25000 -> "25,000 د.ع"
  static String formatInt(int amount, {bool showSymbol = true}) {
    if (amount == 0) {
      return showSymbol ? '0 $currencySymbol' : '0';
    }

    final formattedAmount = _addCommas(amount.toString());
    return showSymbol ? '$formattedAmount $currencySymbol' : formattedAmount;
  }

  /// Add commas to a number string for better readability
  /// Example: "25000" -> "25,000"
  static String _addCommas(String number) {
    // Handle decimal numbers
    List<String> parts = number.split('.');
    String integerPart = parts[0];
    String decimalPart = parts.length > 1 ? '.${parts[1]}' : '';

    // Add commas to integer part
    String result = '';
    for (int i = 0; i < integerPart.length; i++) {
      if (i > 0 && (integerPart.length - i) % 3 == 0) {
        result += ',';
      }
      result += integerPart[i];
    }

    return result + decimalPart;
  }

  /// Parse a currency string back to double
  /// Example: "25,000 د.ع" -> 25000.0
  static double parse(String currencyString) {
    if (currencyString.isEmpty) return 0.0;
    
    // Remove currency symbol and commas
    String cleanString = currencyString
        .replaceAll(currencySymbol, '')
        .replaceAll(',', '')
        .trim();
    
    try {
      return double.parse(cleanString);
    } catch (e) {
      return 0.0;
    }
  }

  /// Get currency input formatter for text fields
  static String getCurrencyInputHint() {
    return 'أدخل المبلغ بالدينار العراقي';
  }

  /// Validate if a string represents a valid currency amount
  static bool isValidAmount(String amount) {
    if (amount.isEmpty) return false;
    
    try {
      double value = parse(amount);
      return value >= 0;
    } catch (e) {
      return false;
    }
  }

  /// Format currency for display in receipts and reports
  static String formatForReceipt(double amount) {
    return '${format(amount)} ($currencyName)';
  }

  /// Common denominations for Iraqi Dinar
  static List<int> getCommonDenominations() {
    return [
      1000,    // 1,000 IQD
      5000,    // 5,000 IQD
      10000,   // 10,000 IQD
      25000,   // 25,000 IQD
      50000,   // 50,000 IQD
      100000,  // 100,000 IQD
    ];
  }

  /// Get formatted denomination list for UI
  static List<String> getFormattedDenominations() {
    return getCommonDenominations()
        .map((amount) => formatInt(amount))
        .toList();
  }
}
