import 'package:sqflite/sqflite.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:path/path.dart';
import '../models/booking.dart';
import '../models/customer.dart';
import '../models/cashbox_session.dart';
import '../models/cashbox.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  DatabaseHelper._internal();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'studio_management.db');
    
    return await openDatabase(
      path,
      version: 12, // Increased version to add balance_after column to cashbox_movements
      onCreate: _createDatabase,
      onUpgrade: _upgradeDatabase,
      onOpen: (db) async {
        // Enable foreign keys
        await db.execute('PRAGMA foreign_keys = ON');
      },
    );
  }

  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // Drop and recreate customers table with correct schema
      await db.execute('DROP TABLE IF EXISTS customers');
      await db.execute('''
        CREATE TABLE customers (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          phone TEXT NOT NULL,
          email TEXT,
          address TEXT,
          notes TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT
        )
      ''');
    }
    
    if (oldVersion < 3) {
      // Add admins table
      await db.execute('''
        CREATE TABLE admins (
          id TEXT PRIMARY KEY,
          username TEXT UNIQUE NOT NULL,
          password TEXT NOT NULL,
          full_name TEXT NOT NULL,
          phone TEXT NOT NULL,
          email TEXT,
          role TEXT NOT NULL,
          is_active INTEGER DEFAULT 1,
          created_at TEXT NOT NULL,
          updated_at TEXT
        )
      ''');
      
      // Insert default admin
      await _insertDefaultAdmin(db);
    }
    
    if (oldVersion < 4) {
      // Add new fields to bookings table
      await db.execute('ALTER TABLE bookings ADD COLUMN event_description TEXT');
      await db.execute('ALTER TABLE bookings ADD COLUMN event_date TEXT');
      await db.execute('ALTER TABLE bookings ADD COLUMN selected_offers TEXT');
      await db.execute('ALTER TABLE bookings ADD COLUMN additions TEXT');
      await db.execute('ALTER TABLE bookings ADD COLUMN deposit REAL DEFAULT 0');
      await db.execute('ALTER TABLE bookings ADD COLUMN discount REAL DEFAULT 0');
      await db.execute('ALTER TABLE bookings ADD COLUMN booked_by TEXT');
    }
    
    if (oldVersion < 5) {
      // Remove cash_received column if it exists and recreate table without it
      try {
        // Create new table without cash_received
        await db.execute('''
          CREATE TABLE bookings_new (
            id TEXT PRIMARY KEY,
            customer_name TEXT NOT NULL,
            customer_phone TEXT NOT NULL,
            service_type TEXT NOT NULL,
            event_description TEXT,
            event_date TEXT,
            selected_offers TEXT,
            additions TEXT,
            total_amount REAL NOT NULL,
            deposit REAL NOT NULL,
            discount REAL DEFAULT 0,
            booked_by TEXT,
            paid_amount REAL NOT NULL,
            booking_date TEXT NOT NULL,
            delivery_date TEXT NOT NULL,
            status TEXT NOT NULL,
            notes TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT
          )
        ''');
        
        // Copy data from old table (excluding cash_received)
        await db.execute('''
          INSERT INTO bookings_new 
          SELECT id, customer_name, customer_phone, service_type, event_description, 
                 event_date, selected_offers, additions, total_amount, deposit, 
                 discount, booked_by, paid_amount, booking_date, delivery_date, 
                 status, notes, created_at, updated_at 
          FROM bookings
        ''');
        
        // Drop old table and rename new one
        await db.execute('DROP TABLE bookings');
        await db.execute('ALTER TABLE bookings_new RENAME TO bookings');
      } catch (e) {
        // If migration fails, just continue - the table might already be correct
        print('Migration warning: $e');
      }
    }
    
    if (oldVersion < 6) {
      // Add todos table
      await db.execute('''
        CREATE TABLE todos (
          id TEXT PRIMARY KEY,
          title TEXT NOT NULL,
          description TEXT,
          is_completed INTEGER DEFAULT 0,
          priority TEXT NOT NULL,
          due_date TEXT NOT NULL,
          category TEXT NOT NULL,
          created_at TEXT NOT NULL,
          updated_at TEXT
        )
      ''');
    }

    if (oldVersion < 7) {
      // Add hall_name column to bookings table
      try {
        await db.execute('ALTER TABLE bookings ADD COLUMN hall_name TEXT');
      } catch (e) {
        // Column might already exist, ignore error
        print('Migration warning: $e');
      }
    }

    if (oldVersion < 8) {
      // Add permissions column to admins table
      try {
        await db.execute('ALTER TABLE admins ADD COLUMN permissions TEXT');
      } catch (e) {
        // Column might already exist, ignore error
        print('Migration warning: $e');
      }
    }

    if (oldVersion < 9) {
      // Add cashbox_sessions table
      try {
        await db.execute('''
          CREATE TABLE cashbox_sessions (
            id TEXT PRIMARY KEY,
            start_date TEXT NOT NULL,
            end_date TEXT,
            starting_balance REAL NOT NULL,
            ending_balance REAL,
            actual_balance REAL,
            difference REAL,
            notes TEXT,
            opened_by TEXT NOT NULL,
            closed_by TEXT,
            is_active INTEGER NOT NULL DEFAULT 1,
            created_at TEXT NOT NULL,
            updated_at TEXT
          )
        ''');
      } catch (e) {
        // Table might already exist, ignore error
        print('Migration warning: $e');
      }
    }

    if (oldVersion < 10) {
      // Add cashboxes table
      try {
        await db.execute('''
          CREATE TABLE cashboxes (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            description TEXT,
            location TEXT,
            currency TEXT DEFAULT 'IQD',
            is_active INTEGER DEFAULT 1,
            created_by TEXT NOT NULL,
            created_at TEXT NOT NULL,
            updated_at TEXT
          )
        ''');

        // Add cashbox_id column to cashbox_sessions table
        await db.execute('ALTER TABLE cashbox_sessions ADD COLUMN cashbox_id TEXT');

        // Create default cashbox
        final defaultCashboxId = 'default_cashbox_${DateTime.now().millisecondsSinceEpoch}';
        await db.execute('''
          INSERT INTO cashboxes (id, name, description, location, currency, is_active, created_by, created_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', [
          defaultCashboxId,
          'الصندوق الرئيسي',
          'الصندوق الافتراضي للاستوديو',
          'المكتب الرئيسي',
          'IQD',
          1,
          'النظام',
          DateTime.now().toIso8601String(),
        ]);

        // Update existing sessions to use default cashbox
        await db.execute('''
          UPDATE cashbox_sessions 
          SET cashbox_id = ? 
          WHERE cashbox_id IS NULL
        ''', [defaultCashboxId]);

      } catch (e) {
        print('Migration warning for version 10: $e');
      }
    }

    if (oldVersion < 11) {
      // Add cashbox_movements table
      try {
        await db.execute('''
          CREATE TABLE cashbox_movements (
            id TEXT PRIMARY KEY,
            cashbox_id TEXT NOT NULL,
            type TEXT NOT NULL,
            amount REAL NOT NULL,
            description TEXT NOT NULL,
            from_cashbox_id TEXT,
            to_cashbox_id TEXT,
            reference TEXT,
            performed_by TEXT NOT NULL,
            created_at TEXT NOT NULL,
            notes TEXT,
            FOREIGN KEY (cashbox_id) REFERENCES cashboxes(id),
            FOREIGN KEY (from_cashbox_id) REFERENCES cashboxes(id),
            FOREIGN KEY (to_cashbox_id) REFERENCES cashboxes(id)
          )
        ''');
      } catch (e) {
        print('Migration warning for version 11: $e');
      }
    }

    // Version 12: Add balance_after column to cashbox_movements
    if (oldVersion < 12) {
      try {
        await db.execute('''
          ALTER TABLE cashbox_movements ADD COLUMN balance_after REAL
        ''');
        
        print('Added balance_after column to cashbox_movements table');
      } catch (e) {
        print('Migration warning for version 12: $e');
      }
    }
  }

  Future<void> _createDatabase(Database db, int version) async {
    // Create customers table
    await db.execute('''
      CREATE TABLE customers (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        phone TEXT NOT NULL,
        email TEXT,
        address TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT
      )
    ''');

    // Create bookings table
    await db.execute('''
      CREATE TABLE bookings (
        id TEXT PRIMARY KEY,
        customer_name TEXT NOT NULL,
        customer_phone TEXT NOT NULL,
        service_type TEXT NOT NULL,
        event_description TEXT,
        hall_name TEXT,
        event_date TEXT,
        selected_offers TEXT,
        additions TEXT,
        total_amount REAL NOT NULL,
        deposit REAL NOT NULL,
        discount REAL DEFAULT 0,
        booked_by TEXT,
        paid_amount REAL NOT NULL,
        booking_date TEXT NOT NULL,
        delivery_date TEXT NOT NULL,
        status TEXT NOT NULL,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT
      )
    ''');

    // Create services table
    await db.execute('''
      CREATE TABLE services (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        price REAL NOT NULL,
        duration TEXT,
        category TEXT NOT NULL,
        is_popular INTEGER DEFAULT 0,
        features TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT
      )
    ''');

    // Create packages table
    await db.execute('''
      CREATE TABLE packages (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        original_price REAL NOT NULL,
        discounted_price REAL NOT NULL,
        duration TEXT,
        services TEXT,
        valid_until TEXT NOT NULL,
        is_limited INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT
      )
    ''');

    // Create transactions table
    await db.execute('''
      CREATE TABLE transactions (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        amount REAL NOT NULL,
        description TEXT NOT NULL,
        category TEXT NOT NULL,
        date TEXT NOT NULL,
        created_at TEXT NOT NULL
      )
    ''');

    // Create cashboxes table
    await db.execute('''
      CREATE TABLE cashboxes (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        location TEXT,
        currency TEXT DEFAULT 'IQD',
        is_active INTEGER DEFAULT 1,
        created_by TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT
      )
    ''');

    // Create cashbox_movements table
    await db.execute('''
      CREATE TABLE cashbox_movements (
        id TEXT PRIMARY KEY,
        cashbox_id TEXT NOT NULL,
        type TEXT NOT NULL,
        amount REAL NOT NULL,
        description TEXT NOT NULL,
        from_cashbox_id TEXT,
        to_cashbox_id TEXT,
        reference TEXT,
        performed_by TEXT NOT NULL,
        created_at TEXT NOT NULL,
        notes TEXT,
        FOREIGN KEY (cashbox_id) REFERENCES cashboxes(id),
        FOREIGN KEY (from_cashbox_id) REFERENCES cashboxes(id),
        FOREIGN KEY (to_cashbox_id) REFERENCES cashboxes(id)
      )
    ''');

    // Create cashbox_sessions table
    await db.execute('''
      CREATE TABLE cashbox_sessions (
        id TEXT PRIMARY KEY,
        cashbox_id TEXT NOT NULL,
        start_date TEXT NOT NULL,
        end_date TEXT,
        starting_balance REAL NOT NULL,
        ending_balance REAL,
        actual_balance REAL,
        difference REAL,
        notes TEXT,
        opened_by TEXT NOT NULL,
        closed_by TEXT,
        is_active INTEGER NOT NULL DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (cashbox_id) REFERENCES cashboxes(id)
      )
    ''');

    // Create expenses table
    await db.execute('''
      CREATE TABLE expenses (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        amount REAL NOT NULL,
        category TEXT NOT NULL,
        date TEXT NOT NULL,
        receipt_path TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT
      )
    ''');

    // Create notifications table
    await db.execute('''
      CREATE TABLE notifications (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        type TEXT NOT NULL,
        is_read INTEGER DEFAULT 0,
        related_id TEXT,
        created_at TEXT NOT NULL
      )
    ''');

    // Create admins table
    await db.execute('''
      CREATE TABLE admins (
        id TEXT PRIMARY KEY,
        username TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        full_name TEXT NOT NULL,
        phone TEXT NOT NULL,
        email TEXT,
        role TEXT NOT NULL,
        permissions TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT
      )
    ''');

    // Create settings table
    await db.execute('''
      CREATE TABLE settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Create todos table
    await db.execute('''
      CREATE TABLE todos (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        is_completed INTEGER DEFAULT 0,
        priority TEXT NOT NULL,
        due_date TEXT NOT NULL,
        category TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT
      )
    ''');

    // Insert default admin
    await _insertDefaultAdmin(db);
    
    // Insert default cashbox
    await _insertDefaultCashbox(db);
    
    // Insert default settings
    await _insertDefaultSettings(db);
  }

  Future<void> _insertDefaultAdmin(Database db) async {
    final defaultAdmin = {
      'id': 'ADMIN001',
      'username': 'admin',
      'password': 'admin123',
      'full_name': 'مدير النظام',
      'phone': '07501234567',
      'email': '<EMAIL>',
      'role': 'owner',
      'is_active': 1,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };

    await db.insert('admins', defaultAdmin);
  }

  Future<void> _insertDefaultSettings(Database db) async {
    final defaultSettings = [
      {'key': 'studio_name', 'value': 'استوديو التصوير', 'updated_at': DateTime.now().toIso8601String()},
      {'key': 'currency', 'value': 'IQD', 'updated_at': DateTime.now().toIso8601String()},
      {'key': 'tax_rate', 'value': '0', 'updated_at': DateTime.now().toIso8601String()},
      {'key': 'backup_enabled', 'value': 'true', 'updated_at': DateTime.now().toIso8601String()},
    ];

    for (var setting in defaultSettings) {
      await db.insert('settings', setting);
    }
  }

  Future<void> _insertDefaultCashbox(Database db) async {
    final defaultCashbox = {
      'id': 'default_cashbox_001',
      'name': 'الصندوق الرئيسي',
      'description': 'الصندوق الافتراضي للاستوديو',
      'location': 'المكتب الرئيسي',
      'currency': 'IQD',
      'is_active': 1,
      'created_by': 'النظام',
      'created_at': DateTime.now().toIso8601String(),
    };

    await db.insert('cashboxes', defaultCashbox);
  }

  // Customer operations
  Future<String> insertCustomer(Customer customer) async {
    try {
      final db = await database;
      await db.insert(
        'customers', 
        customer.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      return customer.id;
    } catch (e) {
      throw Exception('Failed to insert customer: $e');
    }
  }

  Future<List<Customer>> getAllCustomers() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('customers', orderBy: 'created_at DESC');
    return List.generate(maps.length, (i) => Customer.fromMap(maps[i]));
  }

  Future<Customer?> getCustomer(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Customer.fromMap(maps.first);
    }
    return null;
  }

  Future<void> updateCustomer(Customer customer) async {
    final db = await database;
    await db.update(
      'customers',
      customer.toMap(),
      where: 'id = ?',
      whereArgs: [customer.id],
    );
  }

  Future<void> deleteCustomer(String id) async {
    final db = await database;
    await db.delete(
      'customers',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<List<Customer>> searchCustomers(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'customers',
      where: 'name LIKE ? OR phone LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => Customer.fromMap(maps[i]));
  }

  // Booking operations
  Future<String> insertBooking(Booking booking) async {
    try {
      final db = await database;
      await db.insert(
        'bookings', 
        booking.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      return booking.id;
    } catch (e) {
      throw Exception('Failed to insert booking: $e');
    }
  }

  Future<List<Booking>> getAllBookings() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('bookings', orderBy: 'created_at DESC');
    return List.generate(maps.length, (i) => Booking.fromMap(maps[i]));
  }

  Future<Booking?> getBooking(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'bookings',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Booking.fromMap(maps.first);
    }
    return null;
  }

  Future<void> updateBooking(Booking booking) async {
    final db = await database;
    await db.update(
      'bookings',
      booking.toMap(),
      where: 'id = ?',
      whereArgs: [booking.id],
    );
  }

  Future<void> deleteBooking(String id) async {
    final db = await database;
    await db.delete(
      'bookings',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<List<Booking>> getBookingsByStatus(BookingStatus status) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'bookings',
      where: 'status = ?',
      whereArgs: [status.name],
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => Booking.fromMap(maps[i]));
  }

  Future<List<Booking>> getBookingsByDateRange(DateTime startDate, DateTime endDate) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'bookings',
      where: 'booking_date BETWEEN ? AND ?',
      whereArgs: [startDate.toIso8601String(), endDate.toIso8601String()],
      orderBy: 'booking_date DESC',
    );
    return List.generate(maps.length, (i) => Booking.fromMap(maps[i]));
  }

  // Transaction operations
  Future<String> insertTransaction(Map<String, dynamic> transaction) async {
    final db = await database;
    await db.insert('transactions', transaction);
    return transaction['id'];
  }

  Future<List<Map<String, dynamic>>> getAllTransactions() async {
    final db = await database;
    return await db.query('transactions', orderBy: 'date DESC');
  }

  Future<List<Map<String, dynamic>>> getTransactionsByType(String type) async {
    final db = await database;
    return await db.query(
      'transactions',
      where: 'type = ?',
      whereArgs: [type],
      orderBy: 'date DESC',
    );
  }

  Future<List<Map<String, dynamic>>> getTransactionsByDateRange(DateTime startDate, DateTime endDate) async {
    final db = await database;
    return await db.query(
      'transactions',
      where: 'date BETWEEN ? AND ?',
      whereArgs: [startDate.toIso8601String(), endDate.toIso8601String()],
      orderBy: 'date DESC',
    );
  }

  // Service operations
  Future<String> insertService(Map<String, dynamic> service) async {
    final db = await database;
    await db.insert('services', service);
    return service['id'];
  }

  Future<List<Map<String, dynamic>>> getAllServices() async {
    final db = await database;
    return await db.query('services', orderBy: 'created_at DESC');
  }

  Future<void> updateService(Map<String, dynamic> service) async {
    final db = await database;
    await db.update(
      'services',
      service,
      where: 'id = ?',
      whereArgs: [service['id']],
    );
  }

  Future<void> deleteService(String id) async {
    final db = await database;
    await db.delete(
      'services',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Package operations
  Future<String> insertPackage(Map<String, dynamic> package) async {
    final db = await database;
    await db.insert('packages', package);
    return package['id'];
  }

  Future<List<Map<String, dynamic>>> getAllPackages() async {
    final db = await database;
    return await db.query('packages', orderBy: 'created_at DESC');
  }

  Future<void> updatePackage(Map<String, dynamic> package) async {
    final db = await database;
    await db.update(
      'packages',
      package,
      where: 'id = ?',
      whereArgs: [package['id']],
    );
  }

  Future<void> deletePackage(String id) async {
    final db = await database;
    await db.delete(
      'packages',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Expense operations
  Future<String> insertExpense(Map<String, dynamic> expense) async {
    final db = await database;
    await db.insert('expenses', expense);
    return expense['id'];
  }

  Future<List<Map<String, dynamic>>> getAllExpenses() async {
    final db = await database;
    return await db.query('expenses', orderBy: 'date DESC');
  }

  Future<void> updateExpense(Map<String, dynamic> expense) async {
    final db = await database;
    await db.update(
      'expenses',
      expense,
      where: 'id = ?',
      whereArgs: [expense['id']],
    );
  }

  Future<void> deleteExpense(String id) async {
    final db = await database;
    await db.delete(
      'expenses',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Notification operations
  Future<String> insertNotification(Map<String, dynamic> notification) async {
    final db = await database;
    await db.insert('notifications', notification);
    return notification['id'];
  }

  Future<List<Map<String, dynamic>>> getAllNotifications() async {
    final db = await database;
    return await db.query('notifications', orderBy: 'created_at DESC');
  }

  Future<List<Map<String, dynamic>>> getUnreadNotifications() async {
    final db = await database;
    return await db.query(
      'notifications',
      where: 'is_read = ?',
      whereArgs: [0],
      orderBy: 'created_at DESC',
    );
  }

  Future<void> markNotificationAsRead(String id) async {
    final db = await database;
    await db.update(
      'notifications',
      {'is_read': 1},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> deleteNotification(String id) async {
    final db = await database;
    await db.delete(
      'notifications',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Settings operations
  Future<void> setSetting(String key, String value) async {
    final db = await database;
    await db.insert(
      'settings',
      {
        'key': key,
        'value': value,
        'updated_at': DateTime.now().toIso8601String(),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<String?> getSetting(String key) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'settings',
      where: 'key = ?',
      whereArgs: [key],
    );
    if (maps.isNotEmpty) {
      return maps.first['value'];
    }
    return null;
  }

  Future<Map<String, String>> getAllSettings() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('settings');
    Map<String, String> settings = {};
    for (var map in maps) {
      settings[map['key']] = map['value'];
    }
    return settings;
  }

  // Analytics and Reports
  Future<Map<String, dynamic>> getFinancialSummary() async {
    final db = await database;
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final startOfDay = DateTime(now.year, now.month, now.day);
    
    // Total revenue
    final revenueResult = await db.rawQuery(
      'SELECT SUM(amount) as total FROM transactions WHERE type = ?',
      ['income']
    );
    
    // Total expenses
    final expenseResult = await db.rawQuery(
      'SELECT SUM(amount) as total FROM transactions WHERE type = ?',
      ['expense']
    );
    
    // Monthly revenue
    final monthlyRevenueResult = await db.rawQuery('''
      SELECT SUM(amount) as total FROM transactions 
      WHERE type = ? AND date(date) >= date(?)
    ''', ['income', startOfMonth.toIso8601String()]);
    
    // Daily revenue
    final dailyRevenueResult = await db.rawQuery('''
      SELECT SUM(amount) as total FROM transactions 
      WHERE type = ? AND date(date) >= date(?)
    ''', ['income', startOfDay.toIso8601String()]);
    
    // Monthly expenses
    final monthlyExpenseResult = await db.rawQuery('''
      SELECT SUM(amount) as total FROM transactions 
      WHERE type = ? AND date(date) >= date(?)
    ''', ['expense', startOfMonth.toIso8601String()]);
    
    // Daily expenses
    final dailyExpenseResult = await db.rawQuery('''
      SELECT SUM(amount) as total FROM transactions 
      WHERE type = ? AND date(date) >= date(?)
    ''', ['expense', startOfDay.toIso8601String()]);
    
    return {
      'totalRevenue': revenueResult.first['total'] ?? 0.0,
      'totalExpenses': expenseResult.first['total'] ?? 0.0,
      'monthlyRevenue': monthlyRevenueResult.first['total'] ?? 0.0,
      'dailyRevenue': dailyRevenueResult.first['total'] ?? 0.0,
      'monthlyExpenses': monthlyExpenseResult.first['total'] ?? 0.0,
      'dailyExpenses': dailyExpenseResult.first['total'] ?? 0.0,
    };
  }

  Future<Map<String, dynamic>> getBookingSummary() async {
    final db = await database;
    
    // Total bookings
    final totalResult = await db.rawQuery('SELECT COUNT(*) as count FROM bookings');
    
    // Completed bookings
    final completedResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM bookings WHERE status = ?',
      ['completed']
    );
    
    // In progress bookings
    final inProgressResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM bookings WHERE status = ?',
      ['inProgress']
    );
    
    // This month bookings
    final monthlyResult = await db.rawQuery('''
      SELECT COUNT(*) as count FROM bookings 
      WHERE created_at >= date('now', 'start of month')
    ''');
    
    return {
      'totalBookings': totalResult.first['count'] ?? 0,
      'completedBookings': completedResult.first['count'] ?? 0,
      'inProgressBookings': inProgressResult.first['count'] ?? 0,
      'monthlyBookings': monthlyResult.first['count'] ?? 0,
    };
  }

  // Database maintenance
  Future<void> resetDatabase() async {
    String path = join(await getDatabasesPath(), 'studio_management.db');
    await deleteDatabase(path);
    _database = null; // Reset the instance
  }

  // Admin operations
  Future<String> insertAdmin(Map<String, dynamic> admin) async {
    final db = await database;
    await db.insert('admins', admin);
    return admin['id'];
  }

  Future<List<Map<String, dynamic>>> getAllAdmins() async {
    final db = await database;
    return await db.query('admins', orderBy: 'created_at DESC');
  }

  Future<Map<String, dynamic>?> getAdminById(String id) async {
    final db = await database;
    final results = await db.query('admins', where: 'id = ?', whereArgs: [id]);
    return results.isNotEmpty ? results.first : null;
  }

  Future<Map<String, dynamic>?> getAdminByUsername(String username) async {
    final db = await database;
    final results = await db.query('admins', where: 'username = ?', whereArgs: [username]);
    return results.isNotEmpty ? results.first : null;
  }

  Future<void> updateAdmin(Map<String, dynamic> admin) async {
    final db = await database;
    await db.update(
      'admins',
      admin,
      where: 'id = ?',
      whereArgs: [admin['id']],
    );
  }

  Future<void> deleteAdmin(String id) async {
    final db = await database;
    await db.delete('admins', where: 'id = ?', whereArgs: [id]);
  }

  Future<void> toggleAdminStatus(String id, bool isActive) async {
    final db = await database;
    await db.update(
      'admins',
      {
        'is_active': isActive ? 1 : 0,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<List<Map<String, dynamic>>> searchAdmins(String query) async {
    final db = await database;
    return await db.query(
      'admins',
      where: 'full_name LIKE ? OR username LIKE ? OR phone LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'full_name',
    );
  }

  Future<bool> validateAdminCredentials(String username, String password) async {
    final db = await database;
    final results = await db.query(
      'admins',
      where: 'username = ? AND password = ? AND is_active = 1',
      whereArgs: [username, password],
    );
    return results.isNotEmpty;
  }

  Future<void> backup() async {
    // Implementation for database backup
    // This would typically involve exporting data to a file
  }

  Future<void> restore(String backupPath) async {
    // Implementation for database restore
    // This would typically involve importing data from a backup file
  }

  Future<void> clearAllData() async {
    final db = await database;
    await db.delete('customers');
    await db.delete('bookings');
    await db.delete('transactions');
    await db.delete('expenses');
    await db.delete('notifications');
    await db.delete('todos');
    // Keep services, packages, and settings
  }

  // TODO Management Functions
  Future<String> insertTodo(Map<String, dynamic> todo) async {
    final db = await database;
    await db.insert('todos', todo);
    return todo['id'];
  }

  Future<List<Map<String, dynamic>>> getAllTodos() async {
    final db = await database;
    return await db.query('todos', orderBy: 'created_at DESC');
  }

  Future<Map<String, dynamic>?> getTodoById(String id) async {
    final db = await database;
    final results = await db.query(
      'todos',
      where: 'id = ?',
      whereArgs: [id],
    );
    return results.isNotEmpty ? results.first : null;
  }

  Future<void> updateTodo(String id, Map<String, dynamic> todo) async {
    final db = await database;
    await db.update(
      'todos',
      todo,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> deleteTodo(String id) async {
    final db = await database;
    await db.delete(
      'todos',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> toggleTodoCompletion(String id) async {
    final db = await database;
    final todo = await getTodoById(id);
    if (todo != null) {
      final isCompleted = todo['is_completed'] == 1 ? 0 : 1;
      await db.update(
        'todos',
        {
          'is_completed': isCompleted,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [id],
      );
    }
  }

  Future<List<Map<String, dynamic>>> getTodosByFilter({
    bool? isCompleted,
    String? priority,
    String? category,
    DateTime? dueDate,
  }) async {
    final db = await database;
    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (isCompleted != null) {
      whereClause += 'is_completed = ?';
      whereArgs.add(isCompleted ? 1 : 0);
    }

    if (priority != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'priority = ?';
      whereArgs.add(priority);
    }

    if (category != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'category = ?';
      whereArgs.add(category);
    }

    if (dueDate != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'date(due_date) = date(?)';
      whereArgs.add(dueDate.toIso8601String());
    }

    return await db.query(
      'todos',
      where: whereClause.isNotEmpty ? whereClause : null,
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
      orderBy: 'created_at DESC',
    );
  }

  Future<Map<String, int>> getTodoStats() async {
    final db = await database;
    
    final totalResult = await db.rawQuery('SELECT COUNT(*) as count FROM todos');
    final total = totalResult.first['count'] as int;
    
    final completedResult = await db.rawQuery('SELECT COUNT(*) as count FROM todos WHERE is_completed = 1');
    final completed = completedResult.first['count'] as int;
    
    final pendingResult = await db.rawQuery('SELECT COUNT(*) as count FROM todos WHERE is_completed = 0');
    final pending = pendingResult.first['count'] as int;
    
    final overdueResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM todos WHERE is_completed = 0 AND date(due_date) < date(?)',
      [DateTime.now().toIso8601String()]
    );
    final overdue = overdueResult.first['count'] as int;
    
    return {
      'total': total,
      'completed': completed,
      'pending': pending,
      'overdue': overdue,
    };
  }

  Future<int> getNotificationsCount() async {
    final db = await database;
    final now = DateTime.now();
    final threeDaysLater = now.add(const Duration(days: 3));
    
    // Count bookings with delivery dates within 3 days or overdue
    final result = await db.rawQuery('''
      SELECT COUNT(*) as count 
      FROM bookings 
      WHERE date(delivery_date) <= date(?)
    ''', [threeDaysLater.toIso8601String()]);
    
    return result.first['count'] as int;
  }

  Future<void> closeDatabase() async {
    final db = await database;
    await db.close();
  }

  // =============== Cashbox Sessions Management ===============
  
  /// Get the current active session for a specific cashbox
  Future<Map<String, dynamic>?> getActiveSession({String? cashboxId}) async {
    final db = await database;
    
    String whereClause = 'is_active = ?';
    List<dynamic> whereArgs = [1];
    
    if (cashboxId != null) {
      whereClause += ' AND cashbox_id = ?';
      whereArgs.add(cashboxId);
    }
    
    final result = await db.query(
      'cashbox_sessions',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'created_at DESC',
      limit: 1,
    );
    
    return result.isNotEmpty ? result.first : null;
  }

  /// Create a new cashbox session
  Future<String> createCashboxSession({
    String? cashboxId,
    required double startingBalance,
    required String openedBy,
    String? notes,
  }) async {
    final db = await database;
    
    // Get default cashbox if not specified
    final selectedCashboxId = cashboxId ?? await getDefaultCashboxId();
    
    // Close any existing active session for this cashbox first
    await db.update(
      'cashbox_sessions',
      {'is_active': 0, 'updated_at': DateTime.now().toIso8601String()},
      where: 'cashbox_id = ? AND is_active = ?',
      whereArgs: [selectedCashboxId, 1],
    );
    
    final sessionId = 'S${DateTime.now().millisecondsSinceEpoch}';
    final now = DateTime.now();
    
    await db.insert('cashbox_sessions', {
      'id': sessionId,
      'cashbox_id': selectedCashboxId,
      'start_date': now.toIso8601String(),
      'starting_balance': startingBalance,
      'opened_by': openedBy,
      'notes': notes,
      'is_active': 1,
      'created_at': now.toIso8601String(),
    });
    
    return sessionId;
  }

  /// Close the current active session
  Future<void> closeCashboxSession({
    required double actualBalance,
    required String closedBy,
    String? notes,
  }) async {
    final db = await database;
    
    // Get current active session
    final activeSession = await getActiveSession();
    if (activeSession == null) {
      throw Exception('لا توجد جلسة نشطة لإغلاقها');
    }
    
    // Calculate ending balance from transactions
    final startDate = DateTime.parse(activeSession['start_date']);
    final endDate = DateTime.now();
    
    final financialSummary = await getFinancialSummaryForPeriod(startDate, endDate);
    final sessionIncome = financialSummary['sessionIncome'] ?? 0.0;
    final sessionExpenses = financialSummary['sessionExpenses'] ?? 0.0;
    
    final startingBalance = activeSession['starting_balance'] as double;
    final calculatedEndingBalance = startingBalance + sessionIncome - sessionExpenses;
    final difference = actualBalance - calculatedEndingBalance;
    
    // Update session
    await db.update(
      'cashbox_sessions',
      {
        'end_date': endDate.toIso8601String(),
        'ending_balance': calculatedEndingBalance,
        'actual_balance': actualBalance,
        'difference': difference,
        'closed_by': closedBy,
        'notes': notes,
        'is_active': 0,
        'updated_at': endDate.toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [activeSession['id']],
    );
  }

  /// Get all cashbox sessions
  Future<List<Map<String, dynamic>>> getAllCashboxSessions({
    int? limit,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final db = await database;
    
    String whereClause = '';
    List<dynamic> whereArgs = [];
    
    if (startDate != null && endDate != null) {
      whereClause = 'start_date >= ? AND start_date <= ?';
      whereArgs = [startDate.toIso8601String(), endDate.toIso8601String()];
    }
    
    return await db.query(
      'cashbox_sessions',
      where: whereClause.isNotEmpty ? whereClause : null,
      whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
      orderBy: 'created_at DESC',
      limit: limit,
    );
  }

  /// Get financial summary for a specific period (used for session calculations)
  Future<Map<String, double>> getFinancialSummaryForPeriod(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await database;
    
    // Get income from transactions
    final incomeResult = await db.rawQuery('''
      SELECT COALESCE(SUM(amount), 0) as total_income
      FROM transactions 
      WHERE type = 'income' 
      AND datetime(date) >= datetime(?) 
      AND datetime(date) <= datetime(?)
    ''', [startDate.toIso8601String(), endDate.toIso8601String()]);
    
    // Get expenses from transactions
    final expenseResult = await db.rawQuery('''
      SELECT COALESCE(SUM(amount), 0) as total_expense
      FROM transactions 
      WHERE type = 'expense' 
      AND datetime(date) >= datetime(?) 
      AND datetime(date) <= datetime(?)
    ''', [startDate.toIso8601String(), endDate.toIso8601String()]);
    
    // Get income from bookings (deposits and completions)
    final bookingIncomeResult = await db.rawQuery('''
      SELECT COALESCE(SUM(paid_amount), 0) as booking_income
      FROM bookings 
      WHERE datetime(created_at) >= datetime(?) 
      AND datetime(created_at) <= datetime(?)
    ''', [startDate.toIso8601String(), endDate.toIso8601String()]);
    
    final sessionIncome = (incomeResult.first['total_income'] as num).toDouble() +
                         (bookingIncomeResult.first['booking_income'] as num).toDouble();
    final sessionExpenses = (expenseResult.first['total_expense'] as num).toDouble();
    
    return {
      'sessionIncome': sessionIncome,
      'sessionExpenses': sessionExpenses,
      'netAmount': sessionIncome - sessionExpenses,
    };
  }

  /// Get session statistics
  Future<Map<String, dynamic>> getSessionStatistics() async {
    final db = await database;
    
    // Total sessions
    final totalResult = await db.rawQuery('SELECT COUNT(*) as count FROM cashbox_sessions');
    final totalSessions = totalResult.first['count'] as int;
    
    // Active sessions
    final activeResult = await db.rawQuery('SELECT COUNT(*) as count FROM cashbox_sessions WHERE is_active = 1');
    final activeSessions = activeResult.first['count'] as int;
    
    // Sessions with differences
    final differencesResult = await db.rawQuery('SELECT COUNT(*) as count FROM cashbox_sessions WHERE difference != 0 AND difference IS NOT NULL');
    final sessionsWithDifferences = differencesResult.first['count'] as int;
    
    return {
      'totalSessions': totalSessions,
      'activeSessions': activeSessions,
      'sessionsWithDifferences': sessionsWithDifferences,
    };
  }

  // Get financial data for a specific session
  Future<Map<String, double>> getSessionFinancialData(String sessionId) async {
    final db = await database;
    
    // Get session details
    final sessionResult = await db.query(
      'cashbox_sessions',
      where: 'id = ?',
      whereArgs: [sessionId],
    );
    
    if (sessionResult.isEmpty) {
      return {
        'income': 0.0,
        'expenses': 0.0,
        'net': 0.0,
      };
    }
    
    final session = sessionResult.first;
    final startDate = session['start_date'] as String;
    final endDate = session['end_date'] as String?;
    
    // If session is still active, use current time as end date
    final actualEndDate = endDate ?? DateTime.now().toIso8601String();
    
    // Get transactions during this session
    final transactionResult = await db.rawQuery('''
      SELECT 
        COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END), 0) as total_income,
        COALESCE(SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END), 0) as total_expenses
      FROM transactions 
      WHERE date >= ? AND date <= ?
    ''', [startDate, actualEndDate]);
    
    // Get booking payments during this session
    final bookingResult = await db.rawQuery('''
      SELECT 
        id, customer_name, service_type, deposit, paid_amount, created_at, delivery_date
      FROM bookings 
      WHERE (created_at >= ? AND created_at <= ?) OR (delivery_date >= ? AND delivery_date <= ?)
    ''', [startDate, actualEndDate, startDate, actualEndDate]);
    
    // Calculate booking income separately for deposits and remaining payments
    double bookingIncome = 0.0;
    for (var booking in bookingResult) {
      final deposit = (booking['deposit'] as num? ?? 0.0).toDouble();
      final paidAmount = (booking['paid_amount'] as num? ?? 0.0).toDouble();
      final createdAt = DateTime.parse(booking['created_at'] as String);
      final deliveryDate = DateTime.parse(booking['delivery_date'] as String);
      final sessionStart = DateTime.parse(startDate);
      final sessionEnd = DateTime.parse(actualEndDate);
      
      // Add deposit if it was paid during session
      if (deposit > 0 && createdAt.isAfter(sessionStart) && createdAt.isBefore(sessionEnd)) {
        bookingIncome += deposit;
      }
      
      // Add remaining amount if it was paid during session (delivery)
      if (paidAmount > deposit && deliveryDate.isAfter(sessionStart) && deliveryDate.isBefore(sessionEnd)) {
        final remainingAmount = paidAmount - deposit;
        bookingIncome += remainingAmount;
      }
    }
    
    final income = (transactionResult.first['total_income'] as num? ?? 0.0).toDouble() + bookingIncome;
    final expenses = (transactionResult.first['total_expenses'] as num? ?? 0.0).toDouble();
    
    return {
      'income': income,
      'expenses': expenses,
      'net': income - expenses,
    };
  }

  // =============== Cashbox Management ===============
  
  /// Get all cashboxes
  Future<List<Map<String, dynamic>>> getAllCashboxes() async {
    final db = await database;
    return await db.query(
      'cashboxes',
      where: 'is_active = ?',
      whereArgs: [1],
      orderBy: 'created_at DESC',
    );
  }

  /// Get a specific cashbox
  Future<Map<String, dynamic>?> getCashbox(String cashboxId) async {
    final db = await database;
    final result = await db.query(
      'cashboxes',
      where: 'id = ? AND is_active = ?',
      whereArgs: [cashboxId, 1],
      limit: 1,
    );
    
    return result.isNotEmpty ? result.first : null;
  }

  /// Create a new cashbox
  Future<String> createCashbox({
    required String name,
    required String description,
    required String location,
    String currency = 'IQD',
    required String createdBy,
  }) async {
    final db = await database;
    
    final cashboxId = 'CB${DateTime.now().millisecondsSinceEpoch}';
    final now = DateTime.now();
    
    await db.insert('cashboxes', {
      'id': cashboxId,
      'name': name,
      'description': description,
      'location': location,
      'currency': currency,
      'is_active': 1,
      'created_by': createdBy,
      'created_at': now.toIso8601String(),
    });
    
    return cashboxId;
  }

  /// Update a cashbox
  Future<void> updateCashbox({
    required String cashboxId,
    String? name,
    String? description,
    String? location,
    String? currency,
  }) async {
    final db = await database;
    
    final updateData = <String, dynamic>{
      'updated_at': DateTime.now().toIso8601String(),
    };
    
    if (name != null) updateData['name'] = name;
    if (description != null) updateData['description'] = description;
    if (location != null) updateData['location'] = location;
    if (currency != null) updateData['currency'] = currency;
    
    await db.update(
      'cashboxes',
      updateData,
      where: 'id = ?',
      whereArgs: [cashboxId],
    );
  }

  /// Deactivate a cashbox (soft delete)
  Future<void> deactivateCashbox(String cashboxId) async {
    final db = await database;
    
    // Check if cashbox has active sessions
    final activeSessions = await db.query(
      'cashbox_sessions',
      where: 'cashbox_id = ? AND is_active = ?',
      whereArgs: [cashboxId, 1],
    );
    
    if (activeSessions.isNotEmpty) {
      throw Exception('لا يمكن حذف صندوق يحتوي على جلسات نشطة');
    }
    
    await db.update(
      'cashboxes',
      {
        'is_active': 0,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [cashboxId],
    );
  }

  /// Get cashbox financial summary
  Future<Map<String, dynamic>> getCashboxSummary(String cashboxId) async {
    final db = await database;
    
    // Get all sessions for this cashbox
    final sessions = await db.query(
      'cashbox_sessions',
      where: 'cashbox_id = ?',
      whereArgs: [cashboxId],
      orderBy: 'created_at DESC',
    );
    
    double totalIncome = 0.0;
    double totalExpenses = 0.0;
    int totalSessions = sessions.length;
    int activeSessions = 0;
    
    for (var session in sessions) {
      if ((session['is_active'] as int) == 1) {
        activeSessions++;
      }
      
      // Get financial data for each session
      final sessionData = await getSessionFinancialData(session['id'] as String);
      totalIncome += sessionData['income'] ?? 0.0;
      totalExpenses += sessionData['expenses'] ?? 0.0;
    }
    
    return {
      'cashbox_id': cashboxId,
      'total_income': totalIncome,
      'total_expenses': totalExpenses,
      'net_amount': totalIncome - totalExpenses,
      'total_sessions': totalSessions,
      'active_sessions': activeSessions,
    };
  }

  /// Get the default cashbox ID
  Future<String> getDefaultCashboxId() async {
    final db = await database;
    final result = await db.query(
      'cashboxes',
      where: 'is_active = ?',
      whereArgs: [1],
      orderBy: 'created_at ASC',
      limit: 1,
    );
    
    if (result.isNotEmpty) {
      return result.first['id'] as String;
    }
    
    // If no cashbox exists, create the default one
    return await createCashbox(
      name: 'الصندوق الرئيسي',
      description: 'الصندوق الافتراضي للاستوديو',
      location: 'المكتب الرئيسي',
      createdBy: 'النظام',
    );
  }
  
  // =============== Cashbox Movements Management ===============
  
  /// Record a deposit to a cashbox
  Future<String> recordCashboxDeposit({
    required String cashboxId,
    required double amount,
    required String description,
    required String performedBy,
    String? reference,
    String? notes,
  }) async {
    final db = await database;
    
    // Get current balance
    final currentBalance = await getCashboxBalance(cashboxId);
    final newBalance = currentBalance + amount;
    
    final movementId = 'M${DateTime.now().millisecondsSinceEpoch}';
    
    await db.insert('cashbox_movements', {
      'id': movementId,
      'cashbox_id': cashboxId,
      'type': 'deposit',
      'amount': amount,
      'description': description,
      'reference': reference,
      'performed_by': performedBy,
      'created_at': DateTime.now().toIso8601String(),
      'notes': notes,
      'balance_after': newBalance,
    });
    
    return movementId;
  }

  /// Record a withdrawal from a cashbox
  Future<String> recordCashboxWithdrawal({
    required String cashboxId,
    required double amount,
    required String description,
    required String performedBy,
    String? reference,
    String? notes,
  }) async {
    final db = await database;
    
    // Get current balance
    final currentBalance = await getCashboxBalance(cashboxId);
    final newBalance = currentBalance - amount;
    
    final movementId = 'M${DateTime.now().millisecondsSinceEpoch}';
    
    await db.insert('cashbox_movements', {
      'id': movementId,
      'cashbox_id': cashboxId,
      'type': 'withdrawal',
      'amount': amount,
      'description': description,
      'reference': reference,
      'performed_by': performedBy,
      'created_at': DateTime.now().toIso8601String(),
      'notes': notes,
      'balance_after': newBalance,
    });
    
    return movementId;
  }

  /// Record a transfer between cashboxes
  Future<String> recordCashboxTransfer({
    required String fromCashboxId,
    required String toCashboxId,
    required double amount,
    required String description,
    required String performedBy,
    String? reference,
    String? notes,
  }) async {
    final db = await database;
    
    // Get current balances
    final fromBalance = await getCashboxBalance(fromCashboxId);
    final toBalance = await getCashboxBalance(toCashboxId);
    final newFromBalance = fromBalance - amount;
    final newToBalance = toBalance + amount;
    
    final transferId = 'T${DateTime.now().millisecondsSinceEpoch}';
    final now = DateTime.now().toIso8601String();
    
    // Record withdrawal from source cashbox
    await db.insert('cashbox_movements', {
      'id': '${transferId}_OUT',
      'cashbox_id': fromCashboxId,
      'type': 'transfer',
      'amount': amount,
      'description': 'تحويل إلى صندوق آخر - $description',
      'from_cashbox_id': fromCashboxId,
      'to_cashbox_id': toCashboxId,
      'reference': reference,
      'performed_by': performedBy,
      'created_at': now,
      'notes': notes,
      'balance_after': newFromBalance,
    });
    
    // Record deposit to destination cashbox
    await db.insert('cashbox_movements', {
      'id': '${transferId}_IN',
      'cashbox_id': toCashboxId,
      'type': 'transfer',
      'amount': amount,
      'description': 'تحويل من صندوق آخر - $description',
      'from_cashbox_id': fromCashboxId,
      'to_cashbox_id': toCashboxId,
      'reference': reference,
      'performed_by': performedBy,
      'created_at': now,
      'notes': notes,
      'balance_after': newToBalance,
    });
    
    return transferId;
  }

  /// Get movements for a specific cashbox
  Future<List<Map<String, dynamic>>> getCashboxMovements({
    required String cashboxId,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    final db = await database;
    
    String whereClause = 'cashbox_id = ?';
    List<dynamic> whereArgs = [cashboxId];
    
    if (startDate != null) {
      whereClause += ' AND date(created_at) >= date(?)';
      whereArgs.add(startDate.toIso8601String());
    }
    
    if (endDate != null) {
      whereClause += ' AND date(created_at) <= date(?)';
      whereArgs.add(endDate.toIso8601String());
    }
    
    return await db.query(
      'cashbox_movements',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'created_at DESC',
      limit: limit,
    );
  }

  /// Get all movements across all cashboxes
  Future<List<Map<String, dynamic>>> getAllCashboxMovements({
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    final db = await database;
    
    String whereClause = '1=1';
    List<dynamic> whereArgs = [];
    
    if (startDate != null) {
      whereClause += ' AND date(created_at) >= date(?)';
      whereArgs.add(startDate.toIso8601String());
    }
    
    if (endDate != null) {
      whereClause += ' AND date(created_at) <= date(?)';
      whereArgs.add(endDate.toIso8601String());
    }
    
    final movements = await db.query(
      'cashbox_movements',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'created_at DESC',
      limit: limit,
    );
    
    // Join with cashbox information
    List<Map<String, dynamic>> enrichedMovements = [];
    for (var movement in movements) {
      final cashboxId = movement['cashbox_id'] as String?;
      final cashbox = cashboxId != null ? await getCashbox(cashboxId) : null;
      final enriched = Map<String, dynamic>.from(movement);
      enriched['cashbox_name'] = cashbox?['name'] ?? 'صندوق محذوف';
      
      // Add from/to cashbox names for transfers
      final fromCashboxId = movement['from_cashbox_id'] as String?;
      if (fromCashboxId != null) {
        final fromCashbox = await getCashbox(fromCashboxId);
        enriched['from_cashbox_name'] = fromCashbox?['name'] ?? 'صندوق محذوف';
      }
      
      final toCashboxId = movement['to_cashbox_id'] as String?;
      if (toCashboxId != null) {
        final toCashbox = await getCashbox(toCashboxId);
        enriched['to_cashbox_name'] = toCashbox?['name'] ?? 'صندوق محذوف';
      }
      
      enrichedMovements.add(enriched);
    }
    
    return enrichedMovements;
  }

  /// Get cashbox balance including movements
  Future<double> getCashboxBalance(String cashboxId) async {
    final db = await database;
    
    // Get sum of deposits and incoming transfers
    final depositResult = await db.rawQuery('''
      SELECT COALESCE(SUM(amount), 0) as total
      FROM cashbox_movements 
      WHERE cashbox_id = ? AND (type = 'deposit' OR (type = 'transfer' AND to_cashbox_id = ?))
    ''', [cashboxId, cashboxId]);
    
    final deposits = (depositResult.first['total'] as num?)?.toDouble() ?? 0.0;
    
    // Get sum of withdrawals and outgoing transfers
    final withdrawalResult = await db.rawQuery('''
      SELECT COALESCE(SUM(amount), 0) as total
      FROM cashbox_movements 
      WHERE cashbox_id = ? AND (type = 'withdrawal' OR (type = 'transfer' AND from_cashbox_id = ?))
    ''', [cashboxId, cashboxId]);
    
    final withdrawals = (withdrawalResult.first['total'] as num?)?.toDouble() ?? 0.0;
    
    return deposits - withdrawals;
  }

  /// Get movement statistics for a cashbox
  Future<Map<String, dynamic>> getCashboxMovementStats(String cashboxId) async {
    final db = await database;
    
    final result = await db.rawQuery('''
      SELECT 
        type,
        COUNT(*) as count,
        COALESCE(SUM(amount), 0) as total_amount
      FROM cashbox_movements 
      WHERE cashbox_id = ?
      GROUP BY type
    ''', [cashboxId]);
    
    Map<String, dynamic> stats = {
      'deposit_count': 0,
      'deposit_amount': 0.0,
      'withdrawal_count': 0,
      'withdrawal_amount': 0.0,
      'transfer_count': 0,
      'transfer_amount': 0.0,
    };
    
    for (var row in result) {
      final type = row['type'] as String;
      final count = row['count'] as int;
      final amount = (row['total_amount'] as num).toDouble();
      
      switch (type) {
        case 'deposit':
          stats['deposit_count'] = count;
          stats['deposit_amount'] = amount;
          break;
        case 'withdrawal':
          stats['withdrawal_count'] = count;
          stats['withdrawal_amount'] = amount;
          break;
        case 'transfer':
          stats['transfer_count'] = count;
          stats['transfer_amount'] = amount;
          break;
      }
    }
    
    return stats;
  }
}
