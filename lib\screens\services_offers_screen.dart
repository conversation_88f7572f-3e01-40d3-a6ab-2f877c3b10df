import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../utils/app_colors.dart';
import '../utils/currency_formatter.dart';
import '../models/service_offer.dart';
import '../services/database_helper.dart';

class ServicesOffersScreen extends StatefulWidget {
  const ServicesOffersScreen({super.key});

  @override
  State<ServicesOffersScreen> createState() => _ServicesOffersScreenState();
}

class _ServicesOffersScreenState extends State<ServicesOffersScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  List<ServiceOffer> _offers = [];
  List<ServiceOffer> _services = [];
  List<Addition> _additions = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load data from SQLite database
      final dbHelper = DatabaseHelper();
      
      // Load all services from database
      final servicesData = await dbHelper.getAllServices();
      
      // Separate offers, services, and additions based on category and is_popular flag
      _offers = [];
      _services = [];
      _additions = [];
      
      for (final serviceMap in servicesData) {
        final category = serviceMap['category'] ?? 'عام';
        
        // If category starts with 'إضافة', it's an addition
        if (category.startsWith('إضافة')) {
          final addition = Addition(
            id: serviceMap['id'] ?? '',
            name: serviceMap['name'] ?? '',
            description: serviceMap['description'] ?? '',
            price: (serviceMap['price'] ?? 0).toDouble(),
            category: category.replaceFirst('إضافة - ', ''),
          );
          _additions.add(addition);
        } else {
          final serviceOffer = ServiceOffer(
            id: serviceMap['id'] ?? '',
            name: serviceMap['name'] ?? '',
            description: serviceMap['description'] ?? '',
            price: (serviceMap['price'] ?? 0).toDouble(),
            duration: serviceMap['duration'] ?? '30 دقيقة',
            category: category,
            isPopular: (serviceMap['is_popular'] ?? 0) == 1,
            features: serviceMap['features']?.split(',') ?? [],
          );
          
          // If it's marked as popular or category contains 'عرض', put it in offers
          if (serviceOffer.isPopular || 
              serviceOffer.category.contains('عرض') || 
              serviceOffer.id.startsWith('offer_')) {
            _offers.add(serviceOffer);
          } else {
            _services.add(serviceOffer);
          }
        }
      }
      
    } catch (e) {
      debugPrint('Error loading data: $e');
      // Initialize empty lists on error
      _offers = [];
      _services = [];
      _additions = [];
    }

    setState(() {
      _isLoading = false;
    });
  }

  void _showAddOfferDialog() {
    showDialog(
      context: context,
      builder: (context) => AddOfferDialog(
        onOfferAdded: () {
          // إعادة تحميل البيانات من قاعدة البيانات لإظهار العرض الجديد
          _loadData();
        },
      ),
    );
  }

  void _showAddServiceDialog() {
    showDialog(
      context: context,
      builder: (context) => AddServiceDialog(
        onServiceAdded: () {
          // إعادة تحميل البيانات من قاعدة البيانات لإظهار الخدمة الجديدة
          _loadData();
        },
      ),
    );
  }

  void _showAddAdditionDialog() {
    showDialog(
      context: context,
      builder: (context) => AddAdditionDialog(
        onAdditionAdded: () {
          // إعادة تحميل البيانات من قاعدة البيانات لإظهار الإضافة الجديدة
          _loadData();
        },
      ),
    );
  }

  void _showEditOfferDialog(ServiceOffer offer) {
    showDialog(
      context: context,
      builder: (context) => AddOfferDialog(
        offer: offer,
        onOfferAdded: () {
          _loadData();
        },
      ),
    );
  }

  void _showEditServiceDialog(ServiceOffer service) {
    showDialog(
      context: context,
      builder: (context) => AddServiceDialog(
        service: service,
        onServiceAdded: () {
          _loadData();
        },
      ),
    );
  }

  void _showEditAdditionDialog(Addition addition) {
    showDialog(
      context: context,
      builder: (context) => AddAdditionDialog(
        addition: addition,
        onAdditionAdded: () {
          _loadData();
        },
      ),
    );
  }

  Future<void> _deleteOffer(ServiceOffer offer) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف العرض "${offer.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final dbHelper = DatabaseHelper();
        await dbHelper.deleteService(offer.id);
        _loadData();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف العرض بنجاح'),
              backgroundColor: AppColors.success,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف العرض: $e'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }

  Future<void> _deleteService(ServiceOffer service) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الخدمة "${service.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final dbHelper = DatabaseHelper();
        await dbHelper.deleteService(service.id);
        _loadData();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف الخدمة بنجاح'),
              backgroundColor: AppColors.success,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف الخدمة: $e'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }

  Future<void> _deleteAddition(Addition addition) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الإضافة "${addition.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final dbHelper = DatabaseHelper();
        await dbHelper.deleteService(addition.id);
        _loadData();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف الإضافة بنجاح'),
              backgroundColor: AppColors.success,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف الإضافة: $e'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('العروض والخدمات'),
        elevation: 0,
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.local_offer), text: 'العروض'),
            Tab(icon: Icon(Icons.camera_alt), text: 'الخدمات'),
            Tab(icon: Icon(Icons.add_circle_outline), text: 'الإضافات'),
          ],
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [AppColors.veryLightBlue, AppColors.offWhite],
          ),
        ),
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildOffersTab(),
            _buildServicesTab(),
            _buildAdditionsTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildOffersTab() {
    return Column(
      children: [
        // Add button
        Padding(
          padding: const EdgeInsets.all(16),
          child: SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _showAddOfferDialog,
              icon: const Icon(Icons.add),
              label: const Text('إضافة عرض جديد'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryBlue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
          ),
        ),
        
        // Offers list
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _offers.isEmpty
                  ? const Center(
                      child: Text(
                        'لا توجد عروض',
                        style: TextStyle(fontSize: 16, color: AppColors.secondaryText),
                      ),
                    )
                  : AnimationLimiter(
                      child: ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: _offers.length,
                        itemBuilder: (context, index) {
                          return AnimationConfiguration.staggeredList(
                            position: index,
                            duration: const Duration(milliseconds: 375),
                            child: SlideAnimation(
                              verticalOffset: 50.0,
                              child: FadeInAnimation(
                                child: OfferCard(
                                  offer: _offers[index],
                                  onEdit: () => _showEditOfferDialog(_offers[index]),
                                  onDelete: () => _deleteOffer(_offers[index]),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
        ),
      ],
    );
  }

  Widget _buildServicesTab() {
    return Column(
      children: [
        // Add button
        Padding(
          padding: const EdgeInsets.all(16),
          child: SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _showAddServiceDialog,
              icon: const Icon(Icons.add),
              label: const Text('إضافة خدمة جديدة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
          ),
        ),
        
        // Services list
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _services.isEmpty
                  ? const Center(
                      child: Text(
                        'لا توجد خدمات',
                        style: TextStyle(fontSize: 16, color: AppColors.secondaryText),
                      ),
                    )
                  : AnimationLimiter(
                      child: ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: _services.length,
                        itemBuilder: (context, index) {
                          return AnimationConfiguration.staggeredList(
                            position: index,
                            duration: const Duration(milliseconds: 375),
                            child: SlideAnimation(
                              verticalOffset: 50.0,
                              child: FadeInAnimation(
                                child: ServiceCard(
                                  service: _services[index],
                                  onEdit: () => _showEditServiceDialog(_services[index]),
                                  onDelete: () => _deleteService(_services[index]),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
        ),
      ],
    );
  }

  Widget _buildAdditionsTab() {
    return Column(
      children: [
        // Add button
        Padding(
          padding: const EdgeInsets.all(16),
          child: SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _showAddAdditionDialog,
              icon: const Icon(Icons.add),
              label: const Text('إضافة إضافة جديدة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.warning,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
          ),
        ),
        
        // Additions list
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _additions.isEmpty
                  ? const Center(
                      child: Text(
                        'لا توجد إضافات',
                        style: TextStyle(fontSize: 16, color: AppColors.secondaryText),
                      ),
                    )
                  : AnimationLimiter(
                      child: ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: _additions.length,
                        itemBuilder: (context, index) {
                          return AnimationConfiguration.staggeredList(
                            position: index,
                            duration: const Duration(milliseconds: 375),
                            child: SlideAnimation(
                              verticalOffset: 50.0,
                              child: FadeInAnimation(
                                child: AdditionCard(
                                  addition: _additions[index],
                                  onEdit: () => _showEditAdditionDialog(_additions[index]),
                                  onDelete: () => _deleteAddition(_additions[index]),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
        ),
      ],
    );
  }
}

// Offer Card Widget
class OfferCard extends StatelessWidget {
  final ServiceOffer offer;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const OfferCard({super.key, required this.offer, this.onEdit, this.onDelete});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: offer.isPopular ? AppColors.warning : AppColors.primaryBlue,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    offer.isPopular ? Icons.star : Icons.local_offer,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        offer.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        offer.category,
                        style: const TextStyle(
                          color: AppColors.secondaryText,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                if (offer.isPopular)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.warning,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'الأكثر طلباً',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                const SizedBox(width: 8),
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert, color: AppColors.secondaryText),
                  onSelected: (value) {
                    if (value == 'edit') {
                      onEdit?.call();
                    } else if (value == 'delete') {
                      onDelete?.call();
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, color: AppColors.primaryBlue),
                          SizedBox(width: 8),
                          Text('تعديل'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: AppColors.error),
                          SizedBox(width: 8),
                          Text('حذف'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              offer.description,
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.schedule, size: 16, color: AppColors.secondaryText),
                const SizedBox(width: 4),
                Text(
                  offer.duration,
                  style: const TextStyle(
                    color: AppColors.secondaryText,
                    fontSize: 14,
                  ),
                ),
                const Spacer(),
                Text(
                  CurrencyFormatter.format(offer.price),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryBlue,
                  ),
                ),
              ],
            ),
            if (offer.features.isNotEmpty) ...[
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                children: offer.features.map((feature) {
                  return Chip(
                    label: Text(
                      feature,
                      style: const TextStyle(fontSize: 12),
                    ),
                    backgroundColor: AppColors.veryLightBlue,
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// Service Card Widget
class ServiceCard extends StatelessWidget {
  final ServiceOffer service;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const ServiceCard({super.key, required this.service, this.onEdit, this.onDelete});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: const BoxDecoration(
                    color: AppColors.success,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.camera_alt,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        service.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        service.category,
                        style: const TextStyle(
                          color: AppColors.secondaryText,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  CurrencyFormatter.format(service.price),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.success,
                  ),
                ),
                const SizedBox(width: 8),
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert, color: AppColors.secondaryText),
                  onSelected: (value) {
                    if (value == 'edit') {
                      onEdit?.call();
                    } else if (value == 'delete') {
                      onDelete?.call();
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, color: AppColors.success),
                          SizedBox(width: 8),
                          Text('تعديل'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: AppColors.error),
                          SizedBox(width: 8),
                          Text('حذف'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              service.description,
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.schedule, size: 16, color: AppColors.secondaryText),
                const SizedBox(width: 4),
                Text(
                  service.duration,
                  style: const TextStyle(
                    color: AppColors.secondaryText,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            if (service.features.isNotEmpty) ...[
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                children: service.features.map((feature) {
                  return Chip(
                    label: Text(
                      feature,
                      style: const TextStyle(fontSize: 12),
                    ),
                    backgroundColor: AppColors.success.withOpacity(0.2),
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// Addition Card Widget
class AdditionCard extends StatelessWidget {
  final Addition addition;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const AdditionCard({super.key, required this.addition, this.onEdit, this.onDelete});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: const BoxDecoration(
                color: AppColors.warning,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.add_circle_outline,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    addition.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    addition.description,
                    style: const TextStyle(
                      color: AppColors.secondaryText,
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    addition.category,
                    style: const TextStyle(
                      color: AppColors.secondaryText,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            Text(
              CurrencyFormatter.format(addition.price),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.warning,
              ),
            ),
            const SizedBox(width: 8),
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert, color: AppColors.secondaryText),
              onSelected: (value) {
                if (value == 'edit') {
                  onEdit?.call();
                } else if (value == 'delete') {
                  onDelete?.call();
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, color: AppColors.warning),
                      SizedBox(width: 8),
                      Text('تعديل'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: AppColors.error),
                      SizedBox(width: 8),
                      Text('حذف'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

// Add Dialogs
class AddOfferDialog extends StatefulWidget {
  final VoidCallback onOfferAdded;
  final ServiceOffer? offer; // للتعديل

  const AddOfferDialog({super.key, required this.onOfferAdded, this.offer});

  @override
  State<AddOfferDialog> createState() => _AddOfferDialogState();
}

class _AddOfferDialogState extends State<AddOfferDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _categoryController = TextEditingController();
  
  bool _isPopular = false;
  bool _isLoading = false;
  final List<String> _features = [];
  final _featureController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // إذا كان هناك عرض للتعديل، املأ الحقول
    if (widget.offer != null) {
      _nameController.text = widget.offer!.name;
      _descriptionController.text = widget.offer!.description;
      _priceController.text = widget.offer!.price.toString();
      _categoryController.text = widget.offer!.category;
      _isPopular = widget.offer!.isPopular;
      _features.addAll(widget.offer!.features);
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _categoryController.dispose();
    _featureController.dispose();
    super.dispose();
  }

  void _addFeature() {
    if (_featureController.text.trim().isNotEmpty) {
      setState(() {
        _features.add(_featureController.text.trim());
        _featureController.clear();
      });
    }
  }

  void _removeFeature(int index) {
    setState(() {
      _features.removeAt(index);
    });
  }

  Future<void> _saveOffer() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // تحديد ما إذا كان هذا تعديل أم إضافة جديدة
      final isEdit = widget.offer != null;
      
      // إنشاء كائن العرض الجديد أو المحدث
      final offer = ServiceOffer(
        id: isEdit ? widget.offer!.id : 'offer_${DateTime.now().millisecondsSinceEpoch}',
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        price: double.parse(_priceController.text.trim()),
        duration: '1 يوم', // مدة افتراضية للعروض
        category: _categoryController.text.trim().isEmpty ? 'عروض عامة' : _categoryController.text.trim(),
        isPopular: _isPopular,
        features: List.from(_features),
      );
      
      // حفظ أو تحديث في قاعدة البيانات
      final dbHelper = DatabaseHelper();
      final data = {
        'id': offer.id,
        'name': offer.name,
        'description': offer.description,
        'price': offer.price,
        'duration': offer.duration,
        'category': offer.category,
        'is_popular': _isPopular ? 1 : 0,
        'features': offer.features.join(','),
        'created_at': isEdit ? null : DateTime.now().toIso8601String(),
      };
      
      if (isEdit) {
        data.remove('created_at'); // لا نحديث تاريخ الإنشاء عند التعديل
        data['id'] = offer.id; // تأكد من وجود الـ ID
        await dbHelper.updateService(data);
      } else {
        await dbHelper.insertService(data);
      }
      
      if (mounted) {
        Navigator.pop(context);
        widget.onOfferAdded();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isEdit ? 'تم تعديل العرض بنجاح' : 'تم إضافة العرض بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في ${widget.offer != null ? 'تعديل' : 'إضافة'} العرض: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 700),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: AppColors.primaryBlue,
                borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.local_offer, color: Colors.white),
                  const SizedBox(width: 8),
                  Text(
                    widget.offer != null ? 'تعديل العرض' : 'إضافة عرض جديد',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
            ),
            
            // Form
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Offer Name
                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'اسم العرض *',
                          prefixIcon: Icon(Icons.title),
                          border: OutlineInputBorder(),
                          hintText: 'مثال: عرض الزفاف الذهبي',
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال اسم العرض';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      // Description
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'وصف العرض *',
                          prefixIcon: Icon(Icons.description),
                          border: OutlineInputBorder(),
                          hintText: 'وصف تفصيلي للعرض والخدمات المتضمنة',
                        ),
                        maxLines: 3,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال وصف العرض';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      // Price
                      TextFormField(
                        controller: _priceController,
                        decoration: const InputDecoration(
                          labelText: 'سعر العرض *',
                          prefixIcon: Icon(Icons.attach_money),
                          border: OutlineInputBorder(),
                          suffixText: 'د.ع',
                          hintText: '250000',
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال السعر';
                          }
                          if (double.tryParse(value) == null) {
                            return 'يرجى إدخال رقم صحيح';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      const SizedBox(height: 16),
                      
                      // Popular checkbox
                      CheckboxListTile(
                        title: const Text('عرض مميز (الأكثر طلباً)'),
                        subtitle: const Text('سيظهر العرض بعلامة مميزة'),
                        value: _isPopular,
                        onChanged: (value) {
                          setState(() {
                            _isPopular = value ?? false;
                          });
                        },
                        activeColor: AppColors.primaryBlue,
                      ),
                      const SizedBox(height: 16),
                      
                      // Features section
                      const Text(
                        'المميزات والخدمات المتضمنة:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      
                      // Add feature
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _featureController,
                              decoration: const InputDecoration(
                                labelText: 'إضافة ميزة',
                                hintText: 'مثال: فيديو 4K',
                                border: OutlineInputBorder(),
                              ),
                              onFieldSubmitted: (_) => _addFeature(),
                            ),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: _addFeature,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.primaryBlue,
                              foregroundColor: Colors.white,
                            ),
                            child: const Icon(Icons.add),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      
                      // Features list
                      if (_features.isNotEmpty) ...[
                        Wrap(
                          spacing: 8,
                          children: _features.asMap().entries.map((entry) {
                            final index = entry.key;
                            final feature = entry.value;
                            return Chip(
                              label: Text(feature),
                              deleteIcon: const Icon(Icons.close, size: 18),
                              onDeleted: () => _removeFeature(index),
                              backgroundColor: AppColors.veryLightBlue,
                            );
                          }).toList(),
                        ),
                        const SizedBox(height: 16),
                      ],
                    ],
                  ),
                ),
              ),
            ),
            
            // Actions
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                border: Border(top: BorderSide(color: AppColors.lightGray)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: _isLoading ? null : () => Navigator.pop(context),
                    child: const Text('إلغاء'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _saveOffer,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryBlue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(widget.offer != null ? 'حفظ التعديل' : 'حفظ العرض'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AddServiceDialog extends StatefulWidget {
  final VoidCallback onServiceAdded;
  final ServiceOffer? service; // للتعديل

  const AddServiceDialog({super.key, required this.onServiceAdded, this.service});

  @override
  State<AddServiceDialog> createState() => _AddServiceDialogState();
}

class _AddServiceDialogState extends State<AddServiceDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _categoryController = TextEditingController();
  
  bool _isLoading = false;
  final List<String> _features = [];
  final _featureController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // إذا كانت هناك خدمة للتعديل، املأ الحقول
    if (widget.service != null) {
      _nameController.text = widget.service!.name;
      _descriptionController.text = widget.service!.description;
      _priceController.text = widget.service!.price.toString();
      _categoryController.text = widget.service!.category;
      _features.addAll(widget.service!.features);
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _categoryController.dispose();
    _featureController.dispose();
    super.dispose();
  }

  void _addFeature() {
    if (_featureController.text.trim().isNotEmpty) {
      setState(() {
        _features.add(_featureController.text.trim());
        _featureController.clear();
      });
    }
  }

  void _removeFeature(int index) {
    setState(() {
      _features.removeAt(index);
    });
  }

  Future<void> _saveService() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // تحديد ما إذا كان هذا تعديل أم إضافة جديدة
      final isEdit = widget.service != null;
      
      // إنشاء كائن الخدمة الجديد أو المحدث
      final service = ServiceOffer(
        id: isEdit ? widget.service!.id : 'service_${DateTime.now().millisecondsSinceEpoch}',
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        price: double.parse(_priceController.text.trim()),
        duration: '30 دقيقة', // default duration for services
        category: _categoryController.text.trim().isEmpty ? 'خدمات عامة' : _categoryController.text.trim(),
        isPopular: false,
        features: List.from(_features),
      );
      
      // حفظ أو تحديث في قاعدة البيانات
      final dbHelper = DatabaseHelper();
      final data = {
        'id': service.id,
        'name': service.name,
        'description': service.description,
        'price': service.price,
        'duration': service.duration,
        'category': service.category,
        'is_popular': 0,
        'features': service.features.join(','),
        'created_at': isEdit ? null : DateTime.now().toIso8601String(),
      };
      
      if (isEdit) {
        data.remove('created_at'); // لا نحديث تاريخ الإنشاء عند التعديل
        data['id'] = service.id; // تأكد من وجود الـ ID
        await dbHelper.updateService(data);
      } else {
        await dbHelper.insertService(data);
      }
      
      if (mounted) {
        Navigator.pop(context);
        widget.onServiceAdded();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isEdit ? 'تم تعديل الخدمة بنجاح' : 'تم إضافة الخدمة بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في ${widget.service != null ? 'تعديل' : 'إضافة'} الخدمة: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: AppColors.success,
                borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.camera_alt, color: Colors.white),
                  const SizedBox(width: 8),
                  Text(
                    widget.service != null ? 'تعديل الخدمة' : 'إضافة خدمة جديدة',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
            ),
            
            // Form
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Service Name
                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'اسم الخدمة *',
                          prefixIcon: Icon(Icons.title),
                          border: OutlineInputBorder(),
                          hintText: 'مثال: تصوير شخصي',
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال اسم الخدمة';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      // Description
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'وصف الخدمة *',
                          prefixIcon: Icon(Icons.description),
                          border: OutlineInputBorder(),
                          hintText: 'وصف تفصيلي للخدمة المقدمة',
                        ),
                        maxLines: 3,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال وصف الخدمة';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      // Price
                      TextFormField(
                        controller: _priceController,
                        decoration: const InputDecoration(
                          labelText: 'سعر الخدمة *',
                          prefixIcon: Icon(Icons.attach_money),
                          border: OutlineInputBorder(),
                          suffixText: 'د.ع',
                          hintText: '25000',
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال السعر';
                          }
                          if (double.tryParse(value) == null) {
                            return 'يرجى إدخال رقم صحيح';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      // Category
                      TextFormField(
                        controller: _categoryController,
                        decoration: const InputDecoration(
                          labelText: 'الفئة',
                          prefixIcon: Icon(Icons.category),
                          border: OutlineInputBorder(),
                          hintText: 'تصوير فردي، تصوير عائلي',
                        ),
                      ),
                      const SizedBox(height: 16),
                      
                      // Features section
                      const Text(
                        'المميزات المتضمنة:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      
                      // Add feature
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _featureController,
                              decoration: const InputDecoration(
                                labelText: 'إضافة ميزة',
                                hintText: 'مثال: إضاءة احترافية',
                                border: OutlineInputBorder(),
                              ),
                              onFieldSubmitted: (_) => _addFeature(),
                            ),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: _addFeature,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.success,
                              foregroundColor: Colors.white,
                            ),
                            child: const Icon(Icons.add),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      
                      // Features list
                      if (_features.isNotEmpty) ...[
                        Wrap(
                          spacing: 8,
                          children: _features.asMap().entries.map((entry) {
                            final index = entry.key;
                            final feature = entry.value;
                            return Chip(
                              label: Text(feature),
                              deleteIcon: const Icon(Icons.close, size: 18),
                              onDeleted: () => _removeFeature(index),
                              backgroundColor: AppColors.success.withOpacity(0.2),
                            );
                          }).toList(),
                        ),
                        const SizedBox(height: 16),
                      ],
                    ],
                  ),
                ),
              ),
            ),
            
            // Actions
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                border: Border(top: BorderSide(color: AppColors.lightGray)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: _isLoading ? null : () => Navigator.pop(context),
                    child: const Text('إلغاء'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _saveService,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.success,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(widget.service != null ? 'حفظ التعديل' : 'حفظ الخدمة'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AddAdditionDialog extends StatefulWidget {
  final VoidCallback onAdditionAdded;
  final Addition? addition; // للتعديل

  const AddAdditionDialog({super.key, required this.onAdditionAdded, this.addition});

  @override
  State<AddAdditionDialog> createState() => _AddAdditionDialogState();
}

class _AddAdditionDialogState extends State<AddAdditionDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _categoryController = TextEditingController();
  
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // إذا كانت هناك إضافة للتعديل، املأ الحقول
    if (widget.addition != null) {
      _nameController.text = widget.addition!.name;
      _descriptionController.text = widget.addition!.description;
      _priceController.text = widget.addition!.price.toString();
      _categoryController.text = widget.addition!.category;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _categoryController.dispose();
    super.dispose();
  }

  Future<void> _saveAddition() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // تحديد ما إذا كان هذا تعديل أم إضافة جديدة
      final isEdit = widget.addition != null;
      
      // إنشاء كائن الإضافة الجديد أو المحدث
      final addition = Addition(
        id: isEdit ? widget.addition!.id : 'addition_${DateTime.now().millisecondsSinceEpoch}',
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        price: double.parse(_priceController.text.trim()),
        category: _categoryController.text.trim().isEmpty ? 'عام' : _categoryController.text.trim(),
      );
      
      // حفظ أو تحديث في قاعدة البيانات
      final dbHelper = DatabaseHelper();
      final data = {
        'id': addition.id,
        'name': addition.name,
        'description': addition.description,
        'price': addition.price,
        'duration': 'إضافة',
        'category': 'إضافة - ${addition.category}',
        'is_popular': 0,
        'features': '',
        'created_at': isEdit ? null : DateTime.now().toIso8601String(),
      };
      
      if (isEdit) {
        data.remove('created_at'); // لا نحديث تاريخ الإنشاء عند التعديل
        data['id'] = addition.id; // تأكد من وجود الـ ID
        await dbHelper.updateService(data);
      } else {
        await dbHelper.insertService(data);
      }
      
      if (mounted) {
        Navigator.pop(context);
        widget.onAdditionAdded();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isEdit ? 'تم تعديل الإضافة بنجاح' : 'تم إضافة الإضافة بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في ${widget.addition != null ? 'تعديل' : 'إضافة'} الإضافة: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 500),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: AppColors.warning,
                borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.add_circle_outline, color: Colors.white),
                  const SizedBox(width: 8),
                  Text(
                    widget.addition != null ? 'تعديل الإضافة' : 'إضافة إضافة جديدة',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
            ),
            
            // Form
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Addition Name
                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'اسم الإضافة *',
                          prefixIcon: Icon(Icons.title),
                          border: OutlineInputBorder(),
                          hintText: 'مثال: طباعة فورية',
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال اسم الإضافة';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      // Description
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'وصف الإضافة *',
                          prefixIcon: Icon(Icons.description),
                          border: OutlineInputBorder(),
                          hintText: 'وصف تفصيلي للإضافة والفائدة منها',
                        ),
                        maxLines: 3,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال وصف الإضافة';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      // Price
                      TextFormField(
                        controller: _priceController,
                        decoration: const InputDecoration(
                          labelText: 'سعر الإضافة *',
                          prefixIcon: Icon(Icons.attach_money),
                          border: OutlineInputBorder(),
                          suffixText: 'د.ع',
                          hintText: '5000',
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال السعر';
                          }
                          if (double.tryParse(value) == null) {
                            return 'يرجى إدخال رقم صحيح';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      
                      // Category
                      TextFormField(
                        controller: _categoryController,
                        decoration: const InputDecoration(
                          labelText: 'الفئة',
                          prefixIcon: Icon(Icons.category),
                          border: OutlineInputBorder(),
                          hintText: 'خدمات إضافية',
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            // Actions
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                border: Border(top: BorderSide(color: AppColors.lightGray)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: _isLoading ? null : () => Navigator.pop(context),
                    child: const Text('إلغاء'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _saveAddition,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.warning,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(widget.addition != null ? 'حفظ التعديل' : 'حفظ الإضافة'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
