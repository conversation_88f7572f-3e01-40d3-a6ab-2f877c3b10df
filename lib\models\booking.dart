import 'package:uuid/uuid.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum BookingStatus {
  inProgress('قيد العمل'),
  completed('تم التسليم'),
  cancelled('ملغي');

  const BookingStatus(this.arabicName);
  final String arabicName;
}

class Booking {
  final String id;
  final String customerName;
  final String customerPhone;
  final String serviceType;
  final String? eventDescription;  // وصف المناسبة
  final String? hallName;          // اسم القاعة
  final DateTime? eventDate;       // تاريخ المناسبة
  final List<String>? selectedOffers; // العروض المختارة
  final List<String>? additions;   // الإضافات
  final double totalAmount;        // المبلغ الكلي
  final double deposit;           // العربون
  final double discount;          // الخصم
  final double paidAmount;        // للتوافق مع النسخة القديمة
  final DateTime bookingDate;     // تاريخ الحجز
  final DateTime deliveryDate;
  final String? bookedBy;         // حجز بواسطة
  final BookingStatus status;
  final String? notes;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Booking({
    String? id,
    required this.customerName,
    required this.customerPhone,
    required this.serviceType,
    this.eventDescription,
    this.hallName,
    this.eventDate,
    this.selectedOffers,
    this.additions,
    required this.totalAmount,
    required this.deposit,
    this.discount = 0.0,
    this.bookedBy,
    double? paidAmount,
    required this.bookingDate,
    required this.deliveryDate,
    required this.status,
    this.notes,
    DateTime? createdAt,
    this.updatedAt,
  }) : 
    id = id ?? const Uuid().v4(),
    createdAt = createdAt ?? DateTime.now(),
    paidAmount = paidAmount ?? deposit;

  double get calculatedRemainingAmount => totalAmount - discount - paidAmount;
  bool get isFullyPaid => calculatedRemainingAmount <= 0;

  // دالة للحصول على رقم الطلب المنسق
  Future<String> get formattedReceiptNumber async {
    final prefs = await SharedPreferences.getInstance();
    final createdDate = createdAt;
    final year = createdDate.year.toString().substring(2); // آخر رقمين من السنة
    final month = createdDate.month.toString().padLeft(2, '0');
    final day = createdDate.day.toString().padLeft(2, '0');
    
    // البحث عن رقم تسلسلي مخزن لهذا اليوم
    final datePrefix = '$year$month$day';
    final existingNumber = prefs.getString('receipt_$id');
    
    if (existingNumber != null) {
      return existingNumber;
    }
    
    // إنشاء رقم جديد إذا لم يكن موجود
    final dailyCountKey = 'daily_count_$datePrefix';
    int dailyCount = prefs.getInt(dailyCountKey) ?? 0;
    dailyCount++;
    
    final sequentialNumber = dailyCount.toString().padLeft(2, '0');
    final receiptNumber = '$datePrefix$sequentialNumber';
    
    // حفظ الرقم للمرجع المستقبلي
    await prefs.setInt(dailyCountKey, dailyCount);
    await prefs.setString('receipt_$id', receiptNumber);
    
    return receiptNumber;
  }

  factory Booking.fromMap(Map<String, dynamic> map) {
    return Booking(
      id: map['id'],
      customerName: map['customer_name'],
      customerPhone: map['customer_phone'],
      serviceType: map['service_type'],
      eventDescription: map['event_description'],
      hallName: map['hall_name'],
      eventDate: map['event_date'] != null ? DateTime.parse(map['event_date']) : null,
      selectedOffers: map['selected_offers'] != null ? List<String>.from(map['selected_offers'].split(',')) : null,
      additions: map['additions'] != null ? List<String>.from(map['additions'].split(',')) : null,
      totalAmount: (map['total_amount']).toDouble(),
      deposit: (map['deposit'] ?? 0).toDouble(),
      discount: (map['discount'] ?? 0).toDouble(),
      bookedBy: map['booked_by'],
      paidAmount: (map['paid_amount']).toDouble(),
      bookingDate: DateTime.parse(map['booking_date']),
      deliveryDate: DateTime.parse(map['delivery_date']),
      status: BookingStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => BookingStatus.inProgress,
      ),
      notes: map['notes'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'customer_name': customerName,
      'customer_phone': customerPhone,
      'service_type': serviceType,
      'event_description': eventDescription,
      'hall_name': hallName,
      'event_date': eventDate?.toIso8601String(),
      'selected_offers': selectedOffers?.join(','),
      'additions': additions?.join(','),
      'total_amount': totalAmount,
      'deposit': deposit,
      'discount': discount,
      'booked_by': bookedBy,
      'paid_amount': paidAmount,
      'booking_date': bookingDate.toIso8601String(),
      'delivery_date': deliveryDate.toIso8601String(),
      'status': status.name,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  Booking copyWith({
    String? id,
    String? customerName,
    String? customerPhone,
    String? serviceType,
    String? eventDescription,
    String? hallName,
    DateTime? eventDate,
    List<String>? selectedOffers,
    List<String>? additions,
    double? totalAmount,
    double? deposit,
    double? discount,
    String? bookedBy,
    double? paidAmount,
    DateTime? bookingDate,
    DateTime? deliveryDate,
    BookingStatus? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Booking(
      id: id ?? this.id,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      serviceType: serviceType ?? this.serviceType,
      eventDescription: eventDescription ?? this.eventDescription,
      hallName: hallName ?? this.hallName,
      eventDate: eventDate ?? this.eventDate,
      selectedOffers: selectedOffers ?? this.selectedOffers,
      additions: additions ?? this.additions,
      totalAmount: totalAmount ?? this.totalAmount,
      deposit: deposit ?? this.deposit,
      discount: discount ?? this.discount,
      bookedBy: bookedBy ?? this.bookedBy,
      paidAmount: paidAmount ?? this.paidAmount,
      bookingDate: bookingDate ?? this.bookingDate,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Booking(id: $id, customerName: $customerName, serviceType: $serviceType, status: ${status.arabicName})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Booking && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}
