import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:file_picker/file_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path/path.dart' as path;
import 'dart:io';
import 'dart:convert';
import '../utils/app_colors.dart';
import '../services/database_helper.dart';

class BackupScreen extends StatefulWidget {
  const BackupScreen({super.key});

  @override
  State<BackupScreen> createState() => _BackupScreenState();
}

class _BackupScreenState extends State<BackupScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  bool _isLoading = false;
  String? _backupLocation;
  bool _autoBackupEnabled = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _loadBackupSettings();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadBackupSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _backupLocation = prefs.getString('backup_location');
      _autoBackupEnabled = prefs.getBool('auto_backup_enabled') ?? false;
    });
  }

  Future<void> _saveBackupSettings() async {
    final prefs = await SharedPreferences.getInstance();
    if (_backupLocation != null) {
      await prefs.setString('backup_location', _backupLocation!);
    }
    await prefs.setBool('auto_backup_enabled', _autoBackupEnabled);
  }

  Future<void> _selectBackupLocation() async {
    try {
      String? selectedDirectory = await FilePicker.platform.getDirectoryPath();
      
      if (selectedDirectory != null) {
        setState(() {
          _backupLocation = selectedDirectory;
        });
        await _saveBackupSettings();
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم تحديد موقع النسخ الاحتياطي: $selectedDirectory'),
              backgroundColor: AppColors.success,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في اختيار المجلد: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _createBackup() async {
    if (_backupLocation == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى تحديد موقع النسخ الاحتياطي أولاً'),
          backgroundColor: AppColors.warning,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // إنشاء مجلد النسخة الاحتياطية مع الطابع الزمني
      final now = DateTime.now();
      final timestamp = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}';
      final backupFolderName = 'studio_backup_$timestamp';
      final backupFolderPath = path.join(_backupLocation!, backupFolderName);
      
      final backupDirectory = Directory(backupFolderPath);
      await backupDirectory.create(recursive: true);

      // تصدير البيانات من قاعدة البيانات
      final backupData = <String, dynamic>{};
      
      // تصدير الحجوزات
      final bookings = await _databaseHelper.getAllBookings();
      backupData['bookings'] = bookings.map((booking) => booking.toMap()).toList();
      
      // تصدير العملاء
      final customers = await _databaseHelper.getAllCustomers();
      backupData['customers'] = customers.map((customer) => customer.toMap()).toList();
      
      // تصدير الخدمات والعروض
      final services = await _databaseHelper.getAllServices();
      backupData['services'] = services;
      
      // تصدير الإشعارات
      final notifications = await _databaseHelper.getAllNotifications();
      backupData['notifications'] = notifications;
      
      // تصدير المصروفات
      final expenses = await _databaseHelper.getAllExpenses();
      backupData['expenses'] = expenses;

      // إضافة معلومات النسخة الاحتياطية
      backupData['backup_info'] = {
        'created_at': now.toIso8601String(),
        'version': '1.0',
        'app_name': 'Studio Management',
        'total_records': {
          'bookings': bookings.length,
          'customers': customers.length,
          'services': services.length,
          'notifications': notifications.length,
          'expenses': expenses.length,
        },
      };

      // حفظ البيانات في ملف JSON
      final backupFilePath = path.join(backupFolderPath, 'studio_data.json');
      final backupFile = File(backupFilePath);
      final jsonString = const JsonEncoder.withIndent('  ').convert(backupData);
      await backupFile.writeAsString(jsonString);

      // إنشاء ملف README
      final readmeContent = '''
# نسخة احتياطية لنظام إدارة الاستوديو

تاريخ الإنشاء: ${now.toString()}
إجمالي السجلات:
- الحجوزات: ${bookings.length}
- العملاء: ${customers.length}  
- الخدمات: ${services.length}
- الإشعارات: ${notifications.length}
- المصروفات: ${expenses.length}

## الملفات المضمنة:
- studio_data.json: جميع بيانات النظام
- README.txt: هذا الملف

## كيفية الاستعادة:
استخدم خيار "استعادة نسخة احتياطية" في النظام واختر ملف studio_data.json
''';
      
      final readmeFilePath = path.join(backupFolderPath, 'README.txt');
      final readmeFile = File(readmeFilePath);
      await readmeFile.writeAsString(readmeContent);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('تم إنشاء النسخة الاحتياطية بنجاح!'),
                const SizedBox(height: 4),
                Text('الموقع: $backupFolderPath'),
                const SizedBox(height: 4),
                Text('إجمالي السجلات: ${bookings.length + customers.length + services.length + notifications.length + expenses.length}'),
              ],
            ),
            backgroundColor: AppColors.success,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'فتح المجلد',
              textColor: Colors.white,
              onPressed: () async {
                try {
                  await Process.start('explorer', [backupFolderPath], runInShell: true);
                } catch (e) {
                  // فشل في فتح المجلد
                }
              },
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في إنشاء النسخة الاحتياطية: $e'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _restoreBackup() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // اختيار ملف النسخة الاحتياطية
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
        dialogTitle: 'اختيار ملف النسخة الاحتياطية',
      );

      if (result != null && result.files.single.path != null) {
        final filePath = result.files.single.path!;
        final file = File(filePath);
        
        if (!await file.exists()) {
          throw Exception('الملف غير موجود');
        }

        // قراءة ملف النسخة الاحتياطية
        final jsonString = await file.readAsString();
        final backupData = jsonDecode(jsonString) as Map<String, dynamic>;

        // التحقق من صحة النسخة الاحتياطية
        if (!backupData.containsKey('backup_info')) {
          throw Exception('ملف النسخة الاحتياطية غير صالح');
        }

        final backupInfo = backupData['backup_info'] as Map<String, dynamic>;
        
        // عرض معلومات النسخة الاحتياطية للمستخدم
        final confirmed = await _showRestoreConfirmationDialog(backupInfo);
        if (!confirmed) {
          setState(() {
            _isLoading = false;
          });
          return;
        }

        // بدء عملية الاستعادة
        final db = await _databaseHelper.database;
        
        // حذف البيانات الحالية (مع تأكيد المستخدم)
        await db.transaction((txn) async {
          await txn.delete('bookings');
          await txn.delete('customers');
          await txn.delete('services');
          await txn.delete('notifications');
          await txn.delete('expenses');
        });

        int restoredRecords = 0;

        // استعادة العملاء
        if (backupData.containsKey('customers')) {
          final customers = backupData['customers'] as List;
          for (final customerData in customers) {
            final customerMap = customerData as Map<String, dynamic>;
            await db.insert('customers', customerMap);
            restoredRecords++;
          }
        }

        // استعادة الحجوزات
        if (backupData.containsKey('bookings')) {
          final bookings = backupData['bookings'] as List;
          for (final bookingData in bookings) {
            final bookingMap = bookingData as Map<String, dynamic>;
            await db.insert('bookings', bookingMap);
            restoredRecords++;
          }
        }

        // استعادة الخدمات
        if (backupData.containsKey('services')) {
          final services = backupData['services'] as List;
          for (final serviceData in services) {
            await db.insert('services', serviceData);
            restoredRecords++;
          }
        }

        // استعادة الإشعارات
        if (backupData.containsKey('notifications')) {
          final notifications = backupData['notifications'] as List;
          for (final notificationData in notifications) {
            await db.insert('notifications', notificationData);
            restoredRecords++;
          }
        }

        // استعادة المصروفات
        if (backupData.containsKey('expenses')) {
          final expenses = backupData['expenses'] as List;
          for (final expenseData in expenses) {
            await db.insert('expenses', expenseData);
            restoredRecords++;
          }
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('تم استعادة النسخة الاحتياطية بنجاح!'),
                  const SizedBox(height: 4),
                  Text('تم استعادة $restoredRecords سجل'),
                  const SizedBox(height: 4),
                  Text('تاريخ النسخة: ${backupInfo['created_at']}'),
                ],
              ),
              backgroundColor: AppColors.success,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في استعادة النسخة الاحتياطية: $e'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<bool> _showRestoreConfirmationDialog(Map<String, dynamic> backupInfo) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        icon: const Icon(
          Icons.warning,
          color: AppColors.warning,
          size: 48,
        ),
        title: const Text('تأكيد استعادة النسخة الاحتياطية'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'هذا الإجراء سيحذف جميع البيانات الحالية ويستبدلها ببيانات النسخة الاحتياطية.',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.error,
              ),
            ),
            const SizedBox(height: 16),
            const Text('معلومات النسخة الاحتياطية:'),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.lightGray,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('تاريخ الإنشاء: ${backupInfo['created_at']}'),
                  Text('الإصدار: ${backupInfo['version']}'),
                  if (backupInfo['total_records'] != null) ...[
                    const SizedBox(height: 8),
                    const Text('عدد السجلات:'),
                    ...((backupInfo['total_records'] as Map<String, dynamic>).entries.map(
                      (entry) => Text('  ${entry.key}: ${entry.value}'),
                    )),
                  ],
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'هل أنت متأكد من المتابعة؟',
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('استعادة'),
          ),
        ],
      ),
    ) ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('النسخ الاحتياطي'),
        elevation: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.veryLightBlue,
              AppColors.offWhite,
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: AnimationLimiter(
            child: Column(
              children: [
                // Header Section
                AnimationConfiguration.staggeredList(
                  position: 0,
                  duration: const Duration(milliseconds: 375),
                  child: SlideAnimation(
                    verticalOffset: -50.0,
                    child: FadeInAnimation(
                      child: Card(
                        child: Padding(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            children: [
                              const Icon(
                                Icons.backup,
                                size: 64,
                                color: AppColors.primaryBlue,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'إدارة النسخ الاحتياطي',
                                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primaryBlue,
                                ),
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                'احم بياناتك من خلال إنشاء نسخ احتياطية دورية',
                                style: TextStyle(
                                  color: AppColors.secondaryText,
                                  fontSize: 16,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Backup Options
                Expanded(
                  child: ListView(
                    children: [
                      // Create Backup
                      AnimationConfiguration.staggeredList(
                        position: 1,
                        duration: const Duration(milliseconds: 375),
                        child: SlideAnimation(
                          verticalOffset: 50.0,
                          child: FadeInAnimation(
                            child: Card(
                              child: ListTile(
                                leading: Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: const BoxDecoration(
                                    color: AppColors.success,
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.add_to_drive,
                                    color: Colors.white,
                                  ),
                                ),
                                title: const Text(
                                  'إنشاء نسخة احتياطية',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                subtitle: const Text(
                                  'تصدير جميع البيانات إلى ملف JSON في الموقع المحدد',
                                ),
                                trailing: _isLoading
                                    ? const CircularProgressIndicator()
                                    : const Icon(Icons.arrow_forward_ios),
                                onTap: _isLoading ? null : _createBackup,
                              ),
                            ),
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 12),
                      
                      // Restore Backup
                      AnimationConfiguration.staggeredList(
                        position: 2,
                        duration: const Duration(milliseconds: 375),
                        child: SlideAnimation(
                          verticalOffset: 50.0,
                          child: FadeInAnimation(
                            child: Card(
                              child: ListTile(
                                leading: Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: const BoxDecoration(
                                    color: AppColors.warning,
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.restore,
                                    color: Colors.white,
                                  ),
                                ),
                                title: const Text(
                                  'استعادة نسخة احتياطية',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                subtitle: const Text(
                                  'استيراد البيانات من ملف نسخة احتياطية JSON',
                                ),
                                trailing: _isLoading
                                    ? const CircularProgressIndicator()
                                    : const Icon(Icons.arrow_forward_ios),
                                onTap: _isLoading ? null : _restoreBackup,
                              ),
                            ),
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 12),
                      
                      // Backup location settings
                      AnimationConfiguration.staggeredList(
                        position: 3,
                        duration: const Duration(milliseconds: 375),
                        child: SlideAnimation(
                          verticalOffset: 50.0,
                          child: FadeInAnimation(
                            child: Card(
                              child: Padding(
                                padding: const EdgeInsets.all(16),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.all(8),
                                          decoration: const BoxDecoration(
                                            color: AppColors.primaryBlue,
                                            shape: BoxShape.circle,
                                          ),
                                          child: const Icon(
                                            Icons.folder,
                                            color: Colors.white,
                                            size: 20,
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        const Expanded(
                                          child: Text(
                                            'موقع النسخ الاحتياطي',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 12),
                                    Container(
                                      width: double.infinity,
                                      padding: const EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: _backupLocation != null 
                                          ? AppColors.success.withOpacity(0.1)
                                          : AppColors.warning.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color: _backupLocation != null 
                                            ? AppColors.success.withOpacity(0.3)
                                            : AppColors.warning.withOpacity(0.3),
                                        ),
                                      ),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              Icon(
                                                _backupLocation != null 
                                                  ? Icons.check_circle 
                                                  : Icons.warning,
                                                color: _backupLocation != null 
                                                  ? AppColors.success 
                                                  : AppColors.warning,
                                                size: 16,
                                              ),
                                              const SizedBox(width: 8),
                                              Text(
                                                _backupLocation != null 
                                                  ? 'تم تحديد الموقع'
                                                  : 'لم يتم تحديد موقع',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  color: _backupLocation != null 
                                                    ? AppColors.success 
                                                    : AppColors.warning,
                                                ),
                                              ),
                                            ],
                                          ),
                                          if (_backupLocation != null) ...[
                                            const SizedBox(height: 8),
                                            Text(
                                              _backupLocation!,
                                              style: const TextStyle(
                                                fontSize: 12,
                                                color: AppColors.secondaryText,
                                                fontFamily: 'monospace',
                                              ),
                                            ),
                                          ],
                                        ],
                                      ),
                                    ),
                                    const SizedBox(height: 12),
                                    SizedBox(
                                      width: double.infinity,
                                      child: ElevatedButton.icon(
                                        onPressed: _selectBackupLocation,
                                        icon: const Icon(Icons.folder_open),
                                        label: Text(_backupLocation != null 
                                          ? 'تغيير الموقع' 
                                          : 'اختيار موقع النسخ الاحتياطي'),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: AppColors.primaryBlue,
                                          foregroundColor: Colors.white,
                                          padding: const EdgeInsets.all(12),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    // Auto backup toggle
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.schedule,
                                          color: Colors.grey[600],
                                          size: 20,
                                        ),
                                        const SizedBox(width: 8),
                                        const Expanded(
                                          child: Text(
                                            'النسخ الاحتياطي التلقائي',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                        Switch(
                                          value: _autoBackupEnabled,
                                          onChanged: (value) async {
                                            if (value && _backupLocation == null) {
                                              ScaffoldMessenger.of(context).showSnackBar(
                                                const SnackBar(
                                                  content: Text('يرجى تحديد موقع النسخ الاحتياطي أولاً'),
                                                  backgroundColor: AppColors.warning,
                                                ),
                                              );
                                              return;
                                            }
                                            setState(() {
                                              _autoBackupEnabled = value;
                                            });
                                            await _saveBackupSettings();
                                          },
                                          activeThumbColor: AppColors.success,
                                        ),
                                      ],
                                    ),
                                    if (_autoBackupEnabled) ...[
                                      const SizedBox(height: 8),
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: AppColors.info.withOpacity(0.1),
                                          borderRadius: BorderRadius.circular(6),
                                        ),
                                        child: const Row(
                                          children: [
                                            Icon(
                                              Icons.info,
                                              color: AppColors.info,
                                              size: 16,
                                            ),
                                            SizedBox(width: 8),
                                            Expanded(
                                              child: Text(
                                                'سيتم إنشاء نسخة احتياطية تلقائياً كل يوم',
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color: AppColors.info,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 12),
                      
                      // Backup Settings (updated position)
                      AnimationConfiguration.staggeredList(
                        position: 4,
                        duration: const Duration(milliseconds: 375),
                        child: SlideAnimation(
                          verticalOffset: 50.0,
                          child: FadeInAnimation(
                            child: Card(
                              child: ListTile(
                                leading: Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: const BoxDecoration(
                                    color: AppColors.info,
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.settings,
                                    color: Colors.white,
                                  ),
                                ),
                                title: const Text(
                                  'إعدادات النسخ الاحتياطي المتقدمة',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                subtitle: const Text(
                                  'تخصيص جدولة النسخ الاحتياطي وضغط البيانات',
                                ),
                                trailing: const Icon(Icons.arrow_forward_ios),
                                onTap: () {
                                  // سيتم إضافة شاشة الإعدادات المتقدمة لاحقاً
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('سيتم إضافة هذه الميزة قريباً'),
                                      backgroundColor: AppColors.info,
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // Warning Card
                      AnimationConfiguration.staggeredList(
                        position: 5,
                        duration: const Duration(milliseconds: 375),
                        child: SlideAnimation(
                          verticalOffset: 50.0,
                          child: FadeInAnimation(
                            child: Card(
                              color: AppColors.warning.withOpacity(0.1),
                              child: const Padding(
                                padding: EdgeInsets.all(16),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.warning,
                                      color: AppColors.warning,
                                    ),
                                    SizedBox(width: 12),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'تنبيه مهم',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              color: AppColors.warning,
                                            ),
                                          ),
                                          SizedBox(height: 4),
                                          Text(
                                            'تأكد من إنشاء نسخ احتياطية دورية لحماية بياناتك من الفقدان. يُنصح بحفظ النسخ الاحتياطية في مواقع متعددة.',
                                            style: TextStyle(
                                              color: AppColors.primaryText,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
