# دليل تحرير الصور والكتابة عليها

## مقدمة
يوفر نظام إدارة الاستوديو إمكانيات متقدمة لتحرير الصور والكتابة عليها. يمكنك إنشاء وصولات مخصصة مع تأثيرات بصرية متنوعة.

## الإمكانيات المتاحة

### 1. الكتابة على الصور
- **نصوص متعددة**: إضافة عدة نصوص في مواضع مختلفة
- **تنسيق متقدم**: تحكم في الخط، الحجم، اللون، المحاذاة
- **ظلال النص**: إضافة ظلال للنصوص لتحسين الوضوح
- **خلفيات شفافة**: إضافة خلفيات شبه شفافة خلف النصوص
- **دعم عربي كامل**: كتابة صحيحة من اليمين لليسار

### 2. تحرير الخلفيات
- **صور خلفية**: استخدام أي صورة كخلفية للوصل
- **طبقات ملونة**: إضافة طبقات ملونة شفافة فوق الخلفية
- **حدود زخرفية**: إضافة حدود ملونة حول الوصل
- **علامات مائية**: إضافة شعار أو نص كعلامة مائية

### 3. المرشحات والتأثيرات
- **السطوع**: تعديل إضاءة الصورة
- **التباين**: تحسين وضوح الصورة
- **تأثير قديم (Sepia)**: إضافة لون بني كلاسيكي
- **رمادي**: تحويل الصورة لدرجات الرمادي

## كيفية الاستخدام

### إنشاء وصل مخصص بتأثيرات بصرية

```dart
// إنشاء وصل مع حدود زرقاء وطبقة شفافة
final imagePath = await PrintService.createCustomReceiptImage(
  booking: booking,
  template: template,
  receiptType: 'booking',
  
  // تأثيرات بصرية
  overlayColor: Colors.blue,
  overlayOpacity: 0.1,
  addBorder: true,
  borderColor: Color(0xFF2196F3),
  borderWidth: 10.0,
  addWatermark: true,
);
```

### إضافة نصوص على صورة موجودة

```dart
// قائمة النصوص المراد إضافتها
final textOverlays = [
  TextOverlay(
    text: 'نص إضافي',
    x: 100,
    y: 200,
    fontSize: 32,
    color: Colors.red,
    bold: true,
    addShadow: true,
    backgroundColor: Colors.white.withOpacity(0.8),
  ),
  TextOverlay(
    text: 'ملاحظة مهمة',
    x: 100,
    y: 300,
    fontSize: 24,
    color: Colors.green,
    fontFamily: 'Cairo',
  ),
];

// تطبيق النصوص على الصورة
final editedPath = await PrintService.editExistingImage(
  imagePath: 'path/to/existing/image.png',
  textOverlays: textOverlays,
);
```

### تطبيق مرشحات على الصورة

```dart
// تطبيق مرشح السطوع
final brighterImage = await PrintService.applyImageFilter(
  imagePath: 'path/to/image.png',
  filter: ImageFilter(
    type: FilterType.brightness,
    value: 1.2, // زيادة السطوع بـ 20%
  ),
);

// تطبيق تأثير قديم
final sepiaImage = await PrintService.applyImageFilter(
  imagePath: 'path/to/image.png',
  filter: ImageFilter(
    type: FilterType.sepia,
    value: 1.0,
  ),
);

// تحويل لرمادي
final grayscaleImage = await PrintService.applyImageFilter(
  imagePath: 'path/to/image.png',
  filter: ImageFilter(
    type: FilterType.grayscale,
    value: 1.0,
  ),
);
```

## خصائص النص المتقدمة

### TextOverlay Properties
- `text`: النص المراد كتابته
- `x`, `y`: موضع النص على الصورة
- `fontSize`: حجم الخط
- `color`: لون النص
- `bold`: جعل النص عريض
- `fontFamily`: نوع الخط (Cairo, Arial, etc.)
- `textAlign`: محاذاة النص (يمين، وسط، يسار)
- `maxWidth`: أقصى عرض للنص
- `backgroundColor`: لون خلفية النص
- `addShadow`: إضافة ظل للنص

## التأثيرات البصرية المتاحة

### 1. الطبقات الملونة (Overlay)
```dart
overlayColor: Colors.blue,      // اللون
overlayOpacity: 0.1,           // الشفافية (0.0 - 1.0)
```

### 2. الحدود الزخرفية
```dart
addBorder: true,
borderColor: Color(0xFF2196F3),
borderWidth: 8.0,
```

### 3. العلامة المائية
```dart
addWatermark: true,
customWatermark: 'اسم الاستوديو',
```

## أمثلة عملية

### وصل حجز فاخر
```dart
final luxuryReceipt = await PrintService.createCustomReceiptImage(
  booking: booking,
  template: template,
  receiptType: 'booking',
  overlayColor: Colors.gold,
  overlayOpacity: 0.05,
  addBorder: true,
  borderColor: Colors.gold,
  borderWidth: 12.0,
);
```

### وصل دفع بسيط
```dart
final simplePayment = await PrintService.createCustomReceiptImage(
  booking: booking,
  template: template,
  receiptType: 'payment',
  paidAmount: 1000.0,
  addBorder: true,
  borderColor: Colors.green,
  borderWidth: 6.0,
);
```

### إضافة ملاحظة عاجلة
```dart
final urgentNote = TextOverlay(
  text: 'عاجل - يرجى التواصل',
  x: 50,
  y: 50,
  fontSize: 36,
  color: Colors.red,
  bold: true,
  addShadow: true,
  backgroundColor: Colors.yellow.withOpacity(0.8),
);

final updatedImage = await PrintService.editExistingImage(
  imagePath: receiptPath,
  textOverlays: [urgentNote],
);
```

## نصائح للحصول على أفضل النتائج

### 1. اختيار الألوان
- استخدم ألوان متباينة للنص والخلفية
- تجنب الألوان الفاتحة جداً على خلفيات بيضاء
- استخدم الظلال لتحسين وضوح النص

### 2. أحجام الخطوط
- الحد الأدنى: 18 للنصوص العادية
- الحد المثالي: 24-32 للنصوص المهمة
- العناوين: 36-48

### 3. المواضع
- اترك مساحة كافية من الحواف (50+ بكسل)
- تجنب وضع النصوص في مناطق معقدة من الخلفية
- استخدم التوسيط للعناوين

### 4. الأداء
- الصور الكبيرة تحتاج وقت أطول للمعالجة
- استخدم المرشحات بحذر لتجنب البطء
- احفظ نسخة احتياطية قبل التعديل

## قيود النظام

1. **حجم الصورة**: أقصى حجم 50 ميجابايت
2. **التنسيقات المدعومة**: PNG, JPG, BMP
3. **عدد النصوص**: حتى 20 نص في الصورة الواحدة
4. **أحجام الخطوط**: 8-200 بكسل

## الملفات المُنتجة

- الصور الأصلية تبقى بدون تعديل
- الصور المُحررة تُحفظ بأسماء جديدة
- التنسيق: PNG بجودة عالية
- المجلد: Documents/receipts/

يوفر هذا النظام مرونة كاملة في تصميم وتحرير الوصولات بحيث تلبي جميع احتياجاتك الإبداعية!
