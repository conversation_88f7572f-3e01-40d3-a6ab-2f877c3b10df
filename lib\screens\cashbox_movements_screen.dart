import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../services/database_helper.dart';
import '../services/auth_service.dart';
import '../utils/currency_formatter.dart';
import '../utils/app_colors.dart';
import '../utils/date_formatter.dart';

class CashboxMovementsScreen extends StatefulWidget {
  final String cashboxId;
  final String cashboxName;

  const CashboxMovementsScreen({
    Key? key,
    required this.cashboxId,
    required this.cashboxName,
  }) : super(key: key);

  @override
  State<CashboxMovementsScreen> createState() => _CashboxMovementsScreenState();
}

class _CashboxMovementsScreenState extends State<CashboxMovementsScreen> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<Map<String, dynamic>> _movements = [];
  List<Map<String, dynamic>> _allCashboxes = [];
  bool _isLoading = true;
  double _currentBalance = 0.0;
  Map<String, dynamic>? _stats;
  
  // Scroll controllers for enhanced scrolling
  final ScrollController _verticalScrollController = ScrollController();
  final ScrollController _horizontalScrollController = ScrollController();

  // Get current user name from AuthService
  Future<String> get _currentUserName async {
    // احصل على الاسم الكامل أولاً، وإذا لم يوجد استخدم اسم المستخدم
    final fullName = await AuthService.getCurrentFullName();
    if (fullName != null && fullName.isNotEmpty) {
      return fullName;
    }
    
    final username = await AuthService.getCurrentUsername();
    if (username != null && username.isNotEmpty) {
      // تحويل اسم المستخدم إلى عربي إذا كان admin
      if (username == 'admin') {
        return 'المدير';
      }
      return username;
    }
    
    return 'مستخدم غير معروف';
  }

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _verticalScrollController.dispose();
    _horizontalScrollController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      await Future.wait([
        _loadMovements(),
        _loadCashboxes(),
        _loadBalance(),
        _loadStats(),
      ]);
    } catch (e) {
      print('Error loading data: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadMovements() async {
    final movements = await _databaseHelper.getCashboxMovements(
      cashboxId: widget.cashboxId,
      limit: 50,
    );
    setState(() => _movements = movements);
  }

  Future<void> _loadCashboxes() async {
    final cashboxes = await _databaseHelper.getAllCashboxes();
    setState(() => _allCashboxes = cashboxes);
  }

  Future<void> _loadBalance() async {
    final balance = await _databaseHelper.getCashboxBalance(widget.cashboxId);
    setState(() => _currentBalance = balance);
  }

  Future<void> _loadStats() async {
    final stats = await _databaseHelper.getCashboxMovementStats(widget.cashboxId);
    setState(() => _stats = stats);
  }

  void _showDepositDialog() {
    final amountController = TextEditingController();
    final descriptionController = TextEditingController();
    final notesController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.add_circle, color: AppColors.success),
            SizedBox(width: 12),
            Text('إضافة مبلغ إلى الصندوق'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: amountController,
                decoration: InputDecoration(
                  labelText: 'المبلغ *',
                  suffixText: 'د.ع',
                  prefixIcon: Icon(Icons.attach_money, color: AppColors.success),
                  border: OutlineInputBorder(),
                  hintText: 'أدخل المبلغ المراد إضافته',
                ),
                keyboardType: TextInputType.number,
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: descriptionController,
                decoration: InputDecoration(
                  labelText: 'الوصف *',
                  prefixIcon: Icon(Icons.description),
                  border: OutlineInputBorder(),
                  hintText: 'مثال: إيداع نقدي، تسوية حسابات...',
                ),
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: notesController,
                decoration: InputDecoration(
                  labelText: 'ملاحظات (اختيارية)',
                  prefixIcon: Icon(Icons.note),
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => _performDeposit(
              amountController.text,
              descriptionController.text,
              notesController.text,
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.success,
              foregroundColor: Colors.white,
            ),
            child: Text('إضافة'),
          ),
        ],
      ),
    );
  }

  void _showWithdrawalDialog() {
    final amountController = TextEditingController();
    final descriptionController = TextEditingController();
    final notesController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.remove_circle, color: AppColors.error),
            SizedBox(width: 12),
            Text('سحب مبلغ من الصندوق'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.veryLightBlue,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: AppColors.info),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'الرصيد الحالي: ${CurrencyFormatter.format(_currentBalance)}',
                        style: TextStyle(
                          color: AppColors.info,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: amountController,
                decoration: InputDecoration(
                  labelText: 'المبلغ *',
                  suffixText: 'د.ع',
                  prefixIcon: Icon(Icons.attach_money, color: AppColors.error),
                  border: OutlineInputBorder(),
                  hintText: 'أدخل المبلغ المراد سحبه',
                ),
                keyboardType: TextInputType.number,
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: descriptionController,
                decoration: InputDecoration(
                  labelText: 'الوصف *',
                  prefixIcon: Icon(Icons.description),
                  border: OutlineInputBorder(),
                  hintText: 'مثال: مصاريف تشغيلية، سحب نقدي...',
                ),
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: notesController,
                decoration: InputDecoration(
                  labelText: 'ملاحظات (اختيارية)',
                  prefixIcon: Icon(Icons.note),
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => _performWithdrawal(
              amountController.text,
              descriptionController.text,
              notesController.text,
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: Text('سحب'),
          ),
        ],
      ),
    );
  }

  void _showTransferDialog() {
    final amountController = TextEditingController();
    final descriptionController = TextEditingController();
    final notesController = TextEditingController();
    String? selectedToCashboxId;

    // Filter out current cashbox from available targets
    final availableCashboxes = _allCashboxes
        .where((box) => box['id'] != widget.cashboxId)
        .toList();

    if (availableCashboxes.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('لا توجد صناديق أخرى متاحة للتحويل إليها'),
          backgroundColor: AppColors.warning,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setStateDialog) => AlertDialog(
          title: Row(
            children: [
              Icon(Icons.swap_horiz, color: AppColors.primaryBlue),
              SizedBox(width: 12),
              Text('تحويل مبلغ إلى صندوق آخر'),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.veryLightBlue,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(Icons.info_outline, color: AppColors.info),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'من: ${widget.cashboxName}',
                              style: TextStyle(
                                color: AppColors.info,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 4),
                      Row(
                        children: [
                          SizedBox(width: 32),
                          Expanded(
                            child: Text(
                              'الرصيد الحالي: ${CurrencyFormatter.format(_currentBalance)}',
                              style: TextStyle(
                                color: AppColors.info,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: selectedToCashboxId,
                  decoration: InputDecoration(
                    labelText: 'التحويل إلى *',
                    prefixIcon: Icon(Icons.account_balance_wallet),
                    border: OutlineInputBorder(),
                  ),
                  items: availableCashboxes.map((cashbox) {
                    return DropdownMenuItem<String>(
                      value: cashbox['id'],
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(cashbox['name']),
                          if (cashbox['location'] != null)
                            Text(
                              cashbox['location'],
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setStateDialog(() {
                      selectedToCashboxId = value;
                    });
                  },
                ),
                SizedBox(height: 16),
                TextFormField(
                  controller: amountController,
                  decoration: InputDecoration(
                    labelText: 'المبلغ *',
                    suffixText: 'د.ع',
                    prefixIcon: Icon(Icons.attach_money, color: AppColors.primaryBlue),
                    border: OutlineInputBorder(),
                    hintText: 'أدخل المبلغ المراد تحويله',
                  ),
                  keyboardType: TextInputType.number,
                ),
                SizedBox(height: 16),
                TextFormField(
                  controller: descriptionController,
                  decoration: InputDecoration(
                    labelText: 'الوصف *',
                    prefixIcon: Icon(Icons.description),
                    border: OutlineInputBorder(),
                    hintText: 'سبب التحويل...',
                  ),
                ),
                SizedBox(height: 16),
                TextFormField(
                  controller: notesController,
                  decoration: InputDecoration(
                    labelText: 'ملاحظات (اختيارية)',
                    prefixIcon: Icon(Icons.note),
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: selectedToCashboxId != null
                  ? () => _performTransfer(
                        selectedToCashboxId!,
                        amountController.text,
                        descriptionController.text,
                        notesController.text,
                      )
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryBlue,
                foregroundColor: Colors.white,
              ),
              child: Text('تحويل'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _performDeposit(String amountText, String description, String notes) async {
    if (amountText.trim().isEmpty || description.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى ملء جميع الحقول المطلوبة'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    final amount = double.tryParse(amountText);
    if (amount == null || amount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى إدخال مبلغ صالح'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    try {
      final currentUser = await _currentUserName;
      await _databaseHelper.recordCashboxDeposit(
        cashboxId: widget.cashboxId,
        amount: amount,
        description: description.trim(),
        performedBy: currentUser,
        notes: notes.trim().isEmpty ? null : notes.trim(),
      );

      Navigator.pop(context);
      await _loadData();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم إضافة المبلغ بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إضافة المبلغ: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _performWithdrawal(String amountText, String description, String notes) async {
    if (amountText.trim().isEmpty || description.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى ملء جميع الحقول المطلوبة'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    final amount = double.tryParse(amountText);
    if (amount == null || amount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى إدخال مبلغ صالح'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    if (amount > _currentBalance) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('المبلغ المطلوب سحبه أكبر من الرصيد المتاح'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    try {
      final currentUser = await _currentUserName;
      await _databaseHelper.recordCashboxWithdrawal(
        cashboxId: widget.cashboxId,
        amount: amount,
        description: description.trim(),
        performedBy: currentUser,
        notes: notes.trim().isEmpty ? null : notes.trim(),
      );

      Navigator.pop(context);
      await _loadData();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم سحب المبلغ بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في سحب المبلغ: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _performTransfer(String toCashboxId, String amountText, String description, String notes) async {
    if (amountText.trim().isEmpty || description.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى ملء جميع الحقول المطلوبة'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    final amount = double.tryParse(amountText);
    if (amount == null || amount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى إدخال مبلغ صالح'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    if (amount > _currentBalance) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('المبلغ المطلوب تحويله أكبر من الرصيد المتاح'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    try {
      final currentUser = await _currentUserName;
      await _databaseHelper.recordCashboxTransfer(
        fromCashboxId: widget.cashboxId,
        toCashboxId: toCashboxId,
        amount: amount,
        description: description.trim(),
        performedBy: currentUser,
        notes: notes.trim().isEmpty ? null : notes.trim(),
      );

      Navigator.pop(context);
      await _loadData();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم تحويل المبلغ بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحويل المبلغ: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('حركات ${widget.cashboxName}'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
          PopupMenuButton<String>(
            icon: Icon(Icons.add),
            tooltip: 'عمليات الصندوق',
            onSelected: (value) {
              switch (value) {
                case 'deposit':
                  _showDepositDialog();
                  break;
                case 'withdrawal':
                  _showWithdrawalDialog();
                  break;
                case 'transfer':
                  _showTransferDialog();
                  break;
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'deposit',
                child: Row(
                  children: [
                    Icon(Icons.add_circle, color: AppColors.success),
                    SizedBox(width: 8),
                    Text('إضافة مبلغ'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'withdrawal',
                child: Row(
                  children: [
                    Icon(Icons.remove_circle, color: AppColors.error),
                    SizedBox(width: 8),
                    Text('سحب مبلغ'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'transfer',
                child: Row(
                  children: [
                    Icon(Icons.swap_horiz, color: AppColors.primaryBlue),
                    SizedBox(width: 8),
                    Text('تحويل مبلغ'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Focus(
        autofocus: true,
        onKey: (node, event) {
          // Add keyboard shortcuts for scrolling
          if (event.isKeyPressed(LogicalKeyboardKey.pageUp)) {
            _verticalScrollController.animateTo(
              _verticalScrollController.offset - 300,
              duration: Duration(milliseconds: 200),
              curve: Curves.easeOut,
            );
            return KeyEventResult.handled;
          } else if (event.isKeyPressed(LogicalKeyboardKey.pageDown)) {
            _verticalScrollController.animateTo(
              _verticalScrollController.offset + 300,
              duration: Duration(milliseconds: 200),
              curve: Curves.easeOut,
            );
            return KeyEventResult.handled;
          } else if (event.isKeyPressed(LogicalKeyboardKey.home)) {
            _verticalScrollController.animateTo(
              0,
              duration: Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
            return KeyEventResult.handled;
          } else if (event.isKeyPressed(LogicalKeyboardKey.end)) {
            _verticalScrollController.animateTo(
              _verticalScrollController.position.maxScrollExtent,
              duration: Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
            return KeyEventResult.handled;
          }
          return KeyEventResult.ignored;
        },
        child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.veryLightBlue,
              AppColors.offWhite,
            ],
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  // Balance and Stats Cards in single row
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: _buildCombinedBalanceAndStatsCard(),
                  ),
                  
                  // Movements List
                  Expanded(
                    child: _movements.isEmpty
                        ? _buildEmptyState()
                        : _buildMovementsTable(),
                  ),
                ],
              ),
        ),
      ),
      floatingActionButton: _movements.isNotEmpty
          ? FloatingActionButton(
              onPressed: () {
                _verticalScrollController.animateTo(
                  0,
                  duration: Duration(milliseconds: 500),
                  curve: Curves.easeInOut,
                );
              },
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: Colors.white,
              tooltip: 'العودة إلى الأعلى',
              child: Icon(Icons.keyboard_arrow_up),
            )
          : null,
    );
  }

  Widget _buildCombinedBalanceAndStatsCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: const [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 15,
            offset: Offset(0, 8),
          ),
        ],
      ),
      child: Row(
        children: [
          // Balance section
          Expanded(
            flex: 2,
            child: Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.primaryBlue,
                    AppColors.info,
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.account_balance_wallet,
                    color: Colors.white,
                    size: 24,
                  ),
                  SizedBox(height: 8),
                  Text(
                    'الرصيد الحالي',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    CurrencyFormatter.format(_currentBalance),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          if (_stats != null) ...[
            SizedBox(width: 12),
            // Stats section
            Expanded(
              flex: 3,
              child: Row(
                children: [
                  Expanded(
                    child: _buildCompactStatItem(
                      'الإيداعات',
                      _stats!['deposit_count'] ?? 0,
                      _stats!['deposit_amount'] ?? 0.0,
                      AppColors.success,
                      Icons.add_circle,
                    ),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: _buildCompactStatItem(
                      'السحوبات',
                      _stats!['withdrawal_count'] ?? 0,
                      _stats!['withdrawal_amount'] ?? 0.0,
                      AppColors.error,
                      Icons.remove_circle,
                    ),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: _buildCompactStatItem(
                      'التحويلات',
                      _stats!['transfer_count'] ?? 0,
                      _stats!['transfer_amount'] ?? 0.0,
                      AppColors.primaryBlue,
                      Icons.swap_horiz,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCompactStatItem(String title, int count, double amount, Color color, IconData icon) {
    return Container(
      padding: EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 16),
          SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: AppColors.secondaryText,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 2),
          Text(
            '$count',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            CurrencyFormatter.format(amount),
            style: TextStyle(
              fontSize: 8,
              color: AppColors.secondaryText,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long,
            size: 64,
            color: AppColors.lightGray,
          ),
          SizedBox(height: 16),
          Text(
            'لا توجد حركات في هذا الصندوق',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.secondaryText,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'ابدأ بإضافة أو سحب مبلغ',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.secondaryText,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMovementsTable() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 8,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Table Header
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.table_chart,
                  color: Colors.white,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  'سجل حركات الصندوق',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Spacer(),
                Text(
                  '${_movements.length} عملية',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          
          // Table Content
          Expanded(
            child: Scrollbar(
              controller: _verticalScrollController,
              thumbVisibility: true,
              trackVisibility: true,
              thickness: 8,
              radius: Radius.circular(4),
              child: Scrollbar(
                controller: _horizontalScrollController,
                thumbVisibility: true,
                trackVisibility: true,
                thickness: 8,
                radius: Radius.circular(4),
                notificationPredicate: (ScrollNotification notification) {
                  return notification.depth == 1;
                },
                child: SingleChildScrollView(
                  controller: _verticalScrollController,
                  scrollDirection: Axis.vertical,
                  physics: BouncingScrollPhysics(),
                  padding: EdgeInsets.only(bottom: 16),
                  child: SingleChildScrollView(
                    controller: _horizontalScrollController,
                    scrollDirection: Axis.horizontal,
                    physics: BouncingScrollPhysics(),
                    padding: EdgeInsets.only(right: 16),
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        minWidth: MediaQuery.of(context).size.width - 32,
                      ),
                      child: DataTable(
                        columnSpacing: 30,
                        headingRowHeight: 60,
                        dataRowHeight: 80,
                        headingTextStyle: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppColors.primaryText,
                          fontSize: 14,
                        ),
                        dataTextStyle: TextStyle(
                          color: AppColors.primaryText,
                          fontSize: 13,
                        ),
                        border: TableBorder.all(
                          color: AppColors.lightGray.withOpacity(0.3),
                          width: 1,
                        ),
                        headingRowColor: MaterialStateProperty.all(
                          AppColors.veryLightBlue,
                        ),
                        columns: [
                          DataColumn(
                            label: Text(
                              'رقم العملية',
                              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                            ),
                            numeric: true,
                          ),
                          DataColumn(
                            label: Text(
                              'نوع العملية',
                              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                            ),
                          ),
                          DataColumn(
                            label: Text(
                              'المبلغ',
                              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                            ),
                            numeric: true,
                          ),
                          DataColumn(
                            label: Container(
                              width: 140, // توسيع العمود لاستيعاب المعلومات الإضافية
                              child: Text(
                                'قيمة الصندوق\n(قبل ← بعد)',
                                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            numeric: true,
                          ),
                          DataColumn(
                            label: Text(
                              'المشرف',
                              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                            ),
                          ),
                          DataColumn(
                            label: Text(
                              'تاريخ ووقت العملية',
                              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                            ),
                          ),
                              ],
                        rows: _movements.asMap().entries.map((entry) {
                          final index = entry.key;
                          final movement = entry.value;
                          return _buildMovementDataRow(movement, index);
                        }).toList(),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  DataRow _buildMovementDataRow(Map<String, dynamic> movement, int index) {
    final type = movement['type'] as String;
    final amount = (movement['amount'] as num).toDouble();
    final description = movement['description'] as String;
    final createdAt = DateTime.parse(movement['created_at']);
    final performedBy = movement['performed_by'] as String? ?? 'غير محدد';
    final balanceAfter = (movement['balance_after'] as num?)?.toDouble();
    
    // Determine operation type and color
    Color typeColor;
    String typeText;
    IconData typeIcon;
    
    switch (type) {
      case 'deposit':
        typeColor = AppColors.success;
        typeText = 'إيداع';
        typeIcon = Icons.add_circle;
        break;
      case 'withdrawal':
        typeColor = AppColors.error;
        typeText = 'سحب';
        typeIcon = Icons.remove_circle;
        break;
      case 'transfer':
        final toCashboxId = movement['to_cashbox_id'] as String?;
        final isIncoming = toCashboxId == widget.cashboxId;
        typeColor = isIncoming ? AppColors.success : AppColors.error;
        typeText = isIncoming ? 'تحويل وارد' : 'تحويل صادر';
        typeIcon = Icons.swap_horiz;
        break;
      default:
        typeColor = AppColors.secondaryText;
        typeText = 'غير محدد';
        typeIcon = Icons.help;
    }

    return DataRow(
      color: MaterialStateProperty.resolveWith<Color?>(
        (Set<MaterialState> states) {
          if (states.contains(MaterialState.hovered)) {
            return typeColor.withOpacity(0.1);
          }
          return index.isEven ? Colors.grey[50] : Colors.white;
        },
      ),
      cells: [
        // رقم العملية
        DataCell(
          Container(
            padding: EdgeInsets.symmetric(vertical: 12),
            child: Text(
              '#${movement['id']}',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: AppColors.primaryBlue,
                fontSize: 13,
              ),
            ),
          ),
        ),
        
        // نوع العملية
        DataCell(
          Container(
            padding: EdgeInsets.symmetric(vertical: 12),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  typeIcon,
                  color: typeColor,
                  size: 18,
                ),
                SizedBox(width: 8),
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        typeText,
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: typeColor,
                          fontSize: 13,
                        ),
                      ),
                      if (description.isNotEmpty)
                        Text(
                          description,
                          style: TextStyle(
                            fontSize: 11,
                            color: AppColors.secondaryText,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        
        // المبلغ
        DataCell(
          Container(
            padding: EdgeInsets.symmetric(vertical: 12),
            child: Text(
              CurrencyFormatter.format(amount),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: typeColor,
                fontSize: 14,
              ),
            ),
          ),
        ),
        
        // قيمة الصندوق
        DataCell(
          Container(
            padding: EdgeInsets.symmetric(vertical: 12),
            child: balanceAfter != null 
                ? _buildBalanceChangeWidget(movement, balanceAfter)
                : Text(
                    'غير محدد',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: AppColors.primaryText,
                      fontSize: 13,
                    ),
                  ),
          ),
        ),
        
        // المشرف
        DataCell(
          Container(
            padding: EdgeInsets.symmetric(vertical: 12),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.person,
                  color: AppColors.info,
                  size: 16,
                ),
                SizedBox(width: 6),
                Flexible(
                  child: Text(
                    performedBy,
                    style: TextStyle(
                      color: AppColors.info,
                      fontSize: 13,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        
        // تاريخ ووقت العملية
        DataCell(
          Container(
            padding: EdgeInsets.symmetric(vertical: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  DateFormat('dd/MM/yyyy').format(createdAt),
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 13,
                  ),
                ),
                SizedBox(height: 2),
                Text(
                  DateFormatter.formatTime12Hour(createdAt),
                  style: TextStyle(
                    color: AppColors.secondaryText,
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBalanceChangeWidget(Map<String, dynamic> movement, double balanceAfter) {
    final type = movement['type'] as String;
    final amount = (movement['amount'] as num).toDouble();
    
    // حساب القيمة القديمة للصندوق قبل العملية
    double balanceBefore;
    switch (type) {
      case 'deposit':
        balanceBefore = balanceAfter - amount;
        break;
      case 'withdrawal':
        balanceBefore = balanceAfter + amount;
        break;
      case 'transfer':
        final toCashboxId = movement['to_cashbox_id'] as String?;
        final isIncoming = toCashboxId == widget.cashboxId;
        if (isIncoming) {
          // تحويل وارد: القيمة السابقة = القيمة الحالية - المبلغ
          balanceBefore = balanceAfter - amount;
        } else {
          // تحويل صادر: القيمة السابقة = القيمة الحالية + المبلغ
          balanceBefore = balanceAfter + amount;
        }
        break;
      default:
        balanceBefore = balanceAfter;
    }

    // تحديد اللون بناءً على نوع التغيير
    Color changeColor;
    if (balanceAfter > balanceBefore) {
      changeColor = AppColors.success; // أخضر للزيادة
    } else if (balanceAfter < balanceBefore) {
      changeColor = AppColors.error; // أحمر للنقصان
    } else {
      changeColor = AppColors.primaryText; // رمادي للثبات
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // عرض التغيير: القيمة القديمة → القيمة الجديدة
        RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            children: [
              TextSpan(
                text: CurrencyFormatter.format(balanceBefore),
                style: TextStyle(
                  color: AppColors.secondaryText,
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                ),
              ),
              TextSpan(
                text: ' ← ',
                style: TextStyle(
                  color: changeColor,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextSpan(
                text: CurrencyFormatter.format(balanceAfter),
                style: TextStyle(
                  color: changeColor,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 2),
        // عرض مقدار التغيير
        if (balanceAfter != balanceBefore)
          Text(
            '${balanceAfter > balanceBefore ? '+' : ''}${CurrencyFormatter.format(balanceAfter - balanceBefore)}',
            style: TextStyle(
              color: changeColor,
              fontSize: 10,
              fontWeight: FontWeight.w600,
            ),
          ),
      ],
    );
  }
}
