// Database Management
class DatabaseManager {
    constructor() {
        this.dbName = 'studio_management';
        this.version = 1;
        this.db = null;
        this.init();
    }

    async init() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);

            request.onerror = () => {
                console.error('Database error:', request.error);
                reject(request.error);
            };

            request.onsuccess = () => {
                this.db = request.result;
                console.log('Database opened successfully');
                resolve(this.db);
            };

            request.onupgradeneeded = (event) => {
                this.db = event.target.result;
                this.createTables();
            };
        });
    }

    createTables() {
        // Users table
        if (!this.db.objectStoreNames.contains('users')) {
            const userStore = this.db.createObjectStore('users', { keyPath: 'id', autoIncrement: true });
            userStore.createIndex('username', 'username', { unique: true });
            userStore.createIndex('email', 'email', { unique: true });
        }

        // Customers table
        if (!this.db.objectStoreNames.contains('customers')) {
            const customerStore = this.db.createObjectStore('customers', { keyPath: 'id', autoIncrement: true });
            customerStore.createIndex('phone', 'phone', { unique: true });
            customerStore.createIndex('name', 'name', { unique: false });
        }

        // Bookings table
        if (!this.db.objectStoreNames.contains('bookings')) {
            const bookingStore = this.db.createObjectStore('bookings', { keyPath: 'id', autoIncrement: true });
            bookingStore.createIndex('customerPhone', 'customerPhone', { unique: false });
            bookingStore.createIndex('status', 'status', { unique: false });
            bookingStore.createIndex('bookingDate', 'bookingDate', { unique: false });
        }

        // Services table
        if (!this.db.objectStoreNames.contains('services')) {
            const serviceStore = this.db.createObjectStore('services', { keyPath: 'id', autoIncrement: true });
            serviceStore.createIndex('name', 'name', { unique: true });
            serviceStore.createIndex('category', 'category', { unique: false });
        }

        // Cashbox table
        if (!this.db.objectStoreNames.contains('cashbox')) {
            const cashboxStore = this.db.createObjectStore('cashbox', { keyPath: 'id', autoIncrement: true });
            cashboxStore.createIndex('type', 'type', { unique: false });
            cashboxStore.createIndex('date', 'date', { unique: false });
        }

        // Settings table
        if (!this.db.objectStoreNames.contains('settings')) {
            const settingsStore = this.db.createObjectStore('settings', { keyPath: 'key' });
        }

        // Initialize default data
        this.initializeDefaultData();
    }

    async initializeDefaultData() {
        // Create default admin user
        const defaultAdmin = {
            username: 'admin',
            password: 'admin123', // In production, this should be hashed
            email: '<EMAIL>',
            role: 'admin',
            permissions: ['all'],
            createdAt: new Date().toISOString()
        };

        await this.insert('users', defaultAdmin);

        // Create default settings
        const defaultSettings = [
            { key: 'companyName', value: 'استوديو التصوير' },
            { key: 'companyPhone', value: '0123456789' },
            { key: 'companyAddress', value: 'العنوان' },
            { key: 'currency', value: 'دينار عراقي' },
            { key: 'taxRate', value: 15 },
            { key: 'receiptTemplate', value: 'modern' }
        ];

        for (const setting of defaultSettings) {
            await this.insert('settings', setting);
        }

        // Create default services
        const defaultServices = [
            {
                name: 'تصوير زفاف',
                category: 'زفاف',
                price: 500000,
                description: 'تصوير حفل الزفاف الكامل مع الفيديو والصور',
                duration: 8,
                createdAt: new Date().toISOString()
            },
            {
                name: 'تصوير تخرج',
                category: 'تخرج',
                price: 150000,
                description: 'تصوير حفل التخرج والصور التذكارية',
                duration: 4,
                createdAt: new Date().toISOString()
            },
            {
                name: 'تصوير خطوبة',
                category: 'خطوبة',
                price: 200000,
                description: 'تصوير حفل الخطوبة',
                duration: 4,
                createdAt: new Date().toISOString()
            },
            {
                name: 'تصوير عيد ميلاد',
                category: 'مناسبات',
                price: 100000,
                description: 'تصوير حفل عيد الميلاد',
                duration: 3,
                createdAt: new Date().toISOString()
            },
            {
                name: 'جلسة تصوير شخصية',
                category: 'شخصي',
                price: 75000,
                description: 'جلسة تصوير شخصية في الاستوديو',
                duration: 2,
                createdAt: new Date().toISOString()
            }
        ];

        for (const service of defaultServices) {
            await this.insert('services', service);
        }
    }

    async insert(storeName, data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.add(data);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async update(storeName, data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.put(data);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async delete(storeName, id) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.delete(id);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async get(storeName, id) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.get(id);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getAll(storeName) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.getAll();

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getByIndex(storeName, indexName, value) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const index = store.index(indexName);
            const request = index.get(value);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getAllByIndex(storeName, indexName, value) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const index = store.index(indexName);
            const request = index.getAll(value);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async count(storeName) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.count();

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async clear(storeName) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.clear();

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }
}

// Global database instance
const db = new DatabaseManager();
