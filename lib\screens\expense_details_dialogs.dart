import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../utils/app_colors.dart';
import '../utils/currency_formatter.dart';
import '../services/database_helper.dart';

// Today Expense Details Dialog
class TodayExpenseDetailsDialog extends StatefulWidget {
  final DatabaseHelper databaseHelper;

  const TodayExpenseDetailsDialog({
    super.key,
    required this.databaseHelper,
  });

  @override
  State<TodayExpenseDetailsDialog> createState() => _TodayExpenseDetailsDialogState();
}

class _TodayExpenseDetailsDialogState extends State<TodayExpenseDetailsDialog> {
  List<ExpenseDetail> _expenseDetails = [];
  bool _isLoading = true;
  double _totalExpense = 0.0;

  @override
  void initState() {
    super.initState();
    _loadTodayExpenseDetails();
  }

  Future<void> _loadTodayExpenseDetails() async {
    try {
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);

      List<ExpenseDetail> details = [];

      // Load expense transactions for today
      final transactions = await widget.databaseHelper.getTransactionsByDateRange(
        startOfDay,
        endOfDay,
      );

      for (var transaction in transactions) {
        if (transaction['type'] == 'expense') {
          details.add(ExpenseDetail(
            id: transaction['id'],
            amount: transaction['amount'].toDouble(),
            description: transaction['description'],
            category: transaction['category'],
            date: DateTime.parse(transaction['date']),
            type: ExpenseType.transaction,
          ));
        }
      }

      // Sort by date (newest first)
      details.sort((a, b) => b.date.compareTo(a.date));

      double total = details.fold(0.0, (sum, detail) => sum + detail.amount);

      setState(() {
        _expenseDetails = details;
        _totalExpense = total;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading today expense details: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                const Icon(
                  Icons.trending_down,
                  color: AppColors.error,
                  size: 28,
                ),
                const SizedBox(width: 12),
                const Text(
                  'تفاصيل مصاريف اليوم',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryText,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Total Summary
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.error,
                    Color(0xFFE57373),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  const Text(
                    'إجمالي المصاريف لهذا اليوم',
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    CurrencyFormatter.format(_totalExpense),
                    style: const TextStyle(
                      color: AppColors.white,
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${_expenseDetails.length} عملية مصروف',
                    style: const TextStyle(
                      color: AppColors.white,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Details List
            if (_isLoading)
              const Expanded(
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              )
            else if (_expenseDetails.isEmpty)
              const Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.inbox,
                        size: 64,
                        color: AppColors.lightGray,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'لا توجد مصاريف لهذا اليوم',
                        style: TextStyle(
                          color: AppColors.secondaryText,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              Expanded(
                child: ListView.builder(
                  itemCount: _expenseDetails.length,
                  itemBuilder: (context, index) {
                    final detail = _expenseDetails[index];
                    return _buildExpenseDetailTile(detail);
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpenseDetailTile(ExpenseDetail detail) {
    IconData icon;
    Color iconColor;
    String typeText;

    switch (detail.type) {
      case ExpenseType.transaction:
        icon = _getCategoryIcon(detail.category);
        iconColor = AppColors.error;
        typeText = detail.category;
        break;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: iconColor.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: const [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Icon
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),

          // Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  detail.description,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 15,
                    color: AppColors.primaryText,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: iconColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        typeText,
                        style: TextStyle(
                          color: iconColor,
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      DateFormat('HH:mm').format(detail.date),
                      style: const TextStyle(
                        color: AppColors.secondaryText,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Amount
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '-${CurrencyFormatter.format(detail.amount)}',
                style: TextStyle(
                  color: iconColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              Text(
                detail.id,
                style: const TextStyle(
                  color: AppColors.secondaryText,
                  fontSize: 10,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'معدات':
        return Icons.build;
      case 'مصاريف تشغيلية':
        return Icons.settings;
      case 'مرتبات':
        return Icons.people;
      case 'إيجار':
        return Icons.home;
      case 'فواتير':
        return Icons.receipt_long;
      default:
        return Icons.money_off;
    }
  }
}

// Monthly Expense Details Dialog
class MonthlyExpenseDetailsDialog extends StatefulWidget {
  final DatabaseHelper databaseHelper;

  const MonthlyExpenseDetailsDialog({
    super.key,
    required this.databaseHelper,
  });

  @override
  State<MonthlyExpenseDetailsDialog> createState() => _MonthlyExpenseDetailsDialogState();
}

class _MonthlyExpenseDetailsDialogState extends State<MonthlyExpenseDetailsDialog> {
  List<ExpenseDetail> _expenseDetails = [];
  bool _isLoading = true;
  double _totalExpense = 0.0;

  @override
  void initState() {
    super.initState();
    _loadMonthlyExpenseDetails();
  }

  Future<void> _loadMonthlyExpenseDetails() async {
    try {
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final endOfMonth = DateTime(now.year, now.month + 1, 1).subtract(const Duration(days: 1));

      List<ExpenseDetail> details = [];

      // Load expense transactions for this month
      final transactions = await widget.databaseHelper.getTransactionsByDateRange(
        startOfMonth,
        endOfMonth,
      );

      for (var transaction in transactions) {
        if (transaction['type'] == 'expense') {
          details.add(ExpenseDetail(
            id: transaction['id'],
            amount: transaction['amount'].toDouble(),
            description: transaction['description'],
            category: transaction['category'],
            date: DateTime.parse(transaction['date']),
            type: ExpenseType.transaction,
          ));
        }
      }

      // Sort by date (newest first)
      details.sort((a, b) => b.date.compareTo(a.date));

      double total = details.fold(0.0, (sum, detail) => sum + detail.amount);

      setState(() {
        _expenseDetails = details;
        _totalExpense = total;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading monthly expense details: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                const Icon(
                  Icons.money_off,
                  color: AppColors.warning,
                  size: 28,
                ),
                const SizedBox(width: 12),
                const Text(
                  'تفاصيل مصاريف الشهر',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryText,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Total Summary
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.warning,
                    Color(0xFFFFB74D),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  const Text(
                    'إجمالي المصاريف لهذا الشهر',
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    CurrencyFormatter.format(_totalExpense),
                    style: const TextStyle(
                      color: AppColors.white,
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${_expenseDetails.length} عملية مصروف',
                    style: const TextStyle(
                      color: AppColors.white,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Details List
            if (_isLoading)
              const Expanded(
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              )
            else if (_expenseDetails.isEmpty)
              const Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.inbox,
                        size: 64,
                        color: AppColors.lightGray,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'لا توجد مصاريف لهذا الشهر',
                        style: TextStyle(
                          color: AppColors.secondaryText,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              Expanded(
                child: ListView.builder(
                  itemCount: _expenseDetails.length,
                  itemBuilder: (context, index) {
                    final detail = _expenseDetails[index];
                    return _buildExpenseDetailTile(detail);
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpenseDetailTile(ExpenseDetail detail) {
    IconData icon;
    Color iconColor;
    String typeText;

    switch (detail.type) {
      case ExpenseType.transaction:
        icon = _getCategoryIcon(detail.category);
        iconColor = AppColors.warning;
        typeText = detail.category;
        break;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: iconColor.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: const [
          BoxShadow(
            color: AppColors.cardShadow,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Icon
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),

          // Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  detail.description,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 15,
                    color: AppColors.primaryText,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: iconColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        typeText,
                        style: TextStyle(
                          color: iconColor,
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      DateFormat('dd/MM/yyyy').format(detail.date),
                      style: const TextStyle(
                        color: AppColors.secondaryText,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Amount
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '-${CurrencyFormatter.format(detail.amount)}',
                style: TextStyle(
                  color: iconColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              Text(
                detail.id,
                style: const TextStyle(
                  color: AppColors.secondaryText,
                  fontSize: 10,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'معدات':
        return Icons.build;
      case 'مصاريف تشغيلية':
        return Icons.settings;
      case 'مرتبات':
        return Icons.people;
      case 'إيجار':
        return Icons.home;
      case 'فواتير':
        return Icons.receipt_long;
      default:
        return Icons.money_off;
    }
  }
}

// Expense Detail Model
class ExpenseDetail {
  final String id;
  final double amount;
  final String description;
  final String category;
  final DateTime date;
  final ExpenseType type;

  ExpenseDetail({
    required this.id,
    required this.amount,
    required this.description,
    required this.category,
    required this.date,
    required this.type,
  });
}

enum ExpenseType {
  transaction, // معاملة مالية
}
