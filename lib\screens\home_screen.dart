import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../services/auth_service.dart';
import '../services/database_helper.dart';
import '../utils/app_colors.dart';
import '../widgets/animated_menu_card.dart';
import 'login_screen.dart';
import 'booking_receipt_screen.dart';
import 'delivery_receipt_screen.dart';
import 'bookings_screen.dart';
import 'cashbox_screen.dart';
import 'services_offers_screen.dart';
import 'customers_screen.dart';
import 'reports_screen.dart';
import 'notifications_screen.dart';
import 'admins_screen.dart';
import 'todo_screen.dart';
import 'backup_screen.dart';
import 'company_settings_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  String? _username;
  int _notificationsCount = 0;
  List<String> _userPermissions = [];

  void _navigateToBookingReceipt() async {
    final hasPermission = await AuthService.hasPermission('booking_receipt');
    if (!hasPermission) {
      _showPermissionDeniedDialog('وصل الحجز');
      return;
    }
    
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const BookingReceiptScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1.0, 0.0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );
        },
      ),
    );
  }

  void _navigateToDeliveryReceipt() async {
    final hasPermission = await AuthService.hasPermission('delivery');
    if (!hasPermission) {
      _showPermissionDeniedDialog('وصل التسليم');
      return;
    }
    
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const DeliveryReceiptScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1.0, 0.0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );
        },
      ),
    );
  }

  void _navigateToBookings() async {
    final hasPermission = await AuthService.hasPermission('bookings');
    if (!hasPermission) {
      _showPermissionDeniedDialog('الحجوزات');
      return;
    }
    
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const BookingsScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1.0, 0.0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );
        },
      ),
    );
  }

  void _navigateToCashbox() async {
    final hasPermission = await AuthService.hasPermission('cashbox');
    if (!hasPermission) {
      _showPermissionDeniedDialog('الصندوق');
      return;
    }
    
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const CashboxScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1.0, 0.0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );
        },
      ),
    );
  }

  void _navigateToServicesOffers() async {
    final hasPermission = await AuthService.hasPermission('services');
    if (!hasPermission) {
      _showPermissionDeniedDialog('عروض وخدمات');
      return;
    }
    
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const ServicesOffersScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1.0, 0.0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );
        },
      ),
    );
  }

  void _navigateToCustomers() async {
    final hasPermission = await AuthService.hasPermission('customers');
    if (!hasPermission) {
      _showPermissionDeniedDialog('الزبائن');
      return;
    }
    
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const CustomersScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1.0, 0.0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );
        },
      ),
    );
  }

  void _navigateToReports() async {
    final hasPermission = await AuthService.hasPermission('reports');
    if (!hasPermission) {
      _showPermissionDeniedDialog('التقارير');
      return;
    }
    
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const ReportsScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1.0, 0.0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );
        },
      ),
    );
  }

  void _navigateToNotifications() async {
    final hasPermission = await AuthService.hasPermission('notifications');
    if (!hasPermission) {
      _showPermissionDeniedDialog('الاشعارات');
      return;
    }
    
    await Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const NotificationsScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1.0, 0.0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );
        },
      ),
    );
    // Reload notifications count when returning
    _loadNotificationsCount();
  }

  void _navigateToAdmins() async {
    final hasPermission = await AuthService.hasPermission('admins');
    if (!hasPermission) {
      _showPermissionDeniedDialog('المشرفين');
      return;
    }
    
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const AdminsScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1.0, 0.0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );
        },
      ),
    );
  }

  void _navigateToTodo() async {
    final hasPermission = await AuthService.hasPermission('todo');
    if (!hasPermission) {
      _showPermissionDeniedDialog('قائمة المهام');
      return;
    }
    
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const TodoScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1.0, 0.0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );
        },
      ),
    );
  }

  void _navigateToBackup() async {
    final hasPermission = await AuthService.hasPermission('backup');
    if (!hasPermission) {
      _showPermissionDeniedDialog('النسخ الاحتياطي');
      return;
    }
    
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const BackupScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1.0, 0.0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );
        },
      ),
    );
  }

  void _navigateToCompanySettings() {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const CompanySettingsScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1.0, 0.0),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          );
        },
      ),
    );
  }

  List<MenuOption> get _menuOptions {
    final List<MenuOption> allOptions = [
      MenuOption(
        title: 'وصل حجز',
        icon: Icons.receipt_long,
        color: AppColors.primaryBlue,
        onTap: _navigateToBookingReceipt,
        permission: 'booking_receipt',
      ),
      MenuOption(
        title: 'وصل تسليم',
        icon: Icons.assignment_turned_in,
        color: AppColors.success,
        onTap: _navigateToDeliveryReceipt,
        permission: 'delivery',
      ),
      MenuOption(
        title: 'الحجوزات',
        icon: Icons.calendar_today,
        color: AppColors.warning,
        onTap: _navigateToBookings,
        permission: 'bookings',
      ),
      MenuOption(
        title: 'الصندوق',
        icon: Icons.account_balance_wallet,
        color: AppColors.info,
        onTap: _navigateToCashbox,
        permission: 'cashbox',
      ),
      MenuOption(
        title: 'عروض وخدمات',
        icon: Icons.local_offer,
        color: AppColors.primaryBlue,
        onTap: _navigateToServicesOffers,
        permission: 'services',
      ),
      MenuOption(
        title: 'الزبائن',
        icon: Icons.people,
        color: AppColors.success,
        onTap: _navigateToCustomers,
        permission: 'customers',
      ),
      MenuOption(
        title: 'التقارير',
        icon: Icons.analytics,
        color: AppColors.warning,
        onTap: _navigateToReports,
        permission: 'reports',
      ),
      MenuOption(
        title: 'قائمة المهام',
        icon: Icons.task_alt,
        color: AppColors.info,
        onTap: _navigateToTodo,
        permission: 'todo',
      ),
      MenuOption(
        title: 'الاشعارات',
        icon: Icons.notifications,
        color: AppColors.warning,
        onTap: _navigateToNotifications,
        badgeCount: _notificationsCount > 0 ? _notificationsCount : null,
        permission: 'notifications',
      ),
      MenuOption(
        title: 'المشرفين',
        icon: Icons.admin_panel_settings,
        color: AppColors.primaryBlue,
        onTap: _navigateToAdmins,
        permission: 'admins',
      ),
      MenuOption(
        title: 'النسخ الاحتياطي',
        icon: Icons.backup,
        color: AppColors.info,
        onTap: _navigateToBackup,
        permission: 'backup',
      ),
    ];

    // تصفية الخيارات حسب صلاحيات المستخدم
    return allOptions.where((option) {
      return option.permission == null || _userPermissions.contains(option.permission);
    }).toList();
  }

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _loadUserData();
    _loadNotificationsCount();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    final username = await AuthService.getCurrentUsername();
    final permissions = await AuthService.getCurrentUserPermissions();
    setState(() {
      _username = username;
      _userPermissions = permissions;
    });
  }

  Future<void> _loadNotificationsCount() async {
    try {
      final count = await DatabaseHelper().getNotificationsCount();
      setState(() {
        _notificationsCount = count;
      });
    } catch (e) {
      // Handle error silently
      setState(() {
        _notificationsCount = 0;
      });
    }
  }

  Future<void> _logout() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await AuthService.logout();
              if (mounted) {
                Navigator.of(context).pushReplacement(
                  PageRouteBuilder(
                    pageBuilder: (context, animation, secondaryAnimation) =>
                        const LoginScreen(),
                    transitionsBuilder:
                        (context, animation, secondaryAnimation, child) {
                      return SlideTransition(
                        position: Tween<Offset>(
                          begin: const Offset(-1.0, 0.0),
                          end: Offset.zero,
                        ).animate(animation),
                        child: child,
                      );
                    },
                  ),
                );
              }
            },
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }

  void _showPermissionDeniedDialog(String featureName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.block, color: AppColors.error),
            SizedBox(width: 8),
            Text('غير مسموح'),
          ],
        ),
        content: Text(
          'ليس لديك صلاحية للوصول إلى "$featureName".\nيرجى مراجعة المدير لمنحك الصلاحية المطلوبة.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة استوديو التصوير'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _loadUserData();
              _loadNotificationsCount();
            },
            tooltip: 'تحديث',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _navigateToCompanySettings,
            tooltip: 'إعدادات الشركة',
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _logout,
            tooltip: 'تسجيل الخروج',
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.veryLightBlue,
              AppColors.offWhite,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Welcome section
              Container(
                width: double.infinity,
                padding: const EdgeInsets.fromLTRB(8, 2, 8, 1),
                child: AnimationConfiguration.staggeredList(
                  position: 0,
                  duration: const Duration(milliseconds: 375),
                  child: SlideAnimation(
                    verticalOffset: -50.0,
                    child: FadeInAnimation(
                      child: Column(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(5),
                            decoration: const BoxDecoration(
                              color: AppColors.white,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.cardShadow,
                                  blurRadius: 4,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.camera_alt,
                              size: 28,
                              color: AppColors.primaryBlue,
                            ),
                          ),
                          const SizedBox(height: 3),
                          Text(
                            'مرحباً ${_username ?? 'المدير'}',
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              color: AppColors.primaryBlue,
                              fontWeight: FontWeight.bold,
                              fontSize: 15,
                            ),
                          ),
                          const SizedBox(height: 1),
                          Text(
                            'اختر الخدمة المطلوبة',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              
              // Menu grid
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: AnimationLimiter(
                    child: GridView.builder(
                      physics: const NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 4,
                        childAspectRatio: 2.2,
                        crossAxisSpacing: 8,
                        mainAxisSpacing: 6,
                      ),
                      itemCount: _menuOptions.length,
                      itemBuilder: (context, index) {
                        return AnimationConfiguration.staggeredGrid(
                          position: index,
                          duration: const Duration(milliseconds: 375),
                          columnCount: 3,
                          child: ScaleAnimation(
                            child: FadeInAnimation(
                              child: AnimatedMenuCard(
                                option: _menuOptions[index],
                                delay: Duration(milliseconds: index * 100),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
              
              // Footer
              Container(
                padding: const EdgeInsets.all(1),
                child: Text(
                  '© 2025 استوديو التصوير',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.secondaryText,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class MenuOption {
  final String title;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;
  final int? badgeCount;
  final String? permission;

  MenuOption({
    required this.title,
    required this.icon,
    required this.color,
    required this.onTap,
    this.badgeCount,
    this.permission,
  });
}
