// Booking Receipt Screen
class BookingReceiptScreen {
    constructor() {
        this.currentBooking = null;
        this.services = [];
        this.customers = [];
    }

    async render() {
        await this.loadData();
        
        const content = `
            <div class="booking-receipt-container">
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="fas fa-receipt"></i>
                        وصل حجز جديد
                    </h1>
                    <div class="page-actions">
                        <button class="btn btn-success" onclick="bookingReceiptScreen.saveBooking()">
                            <i class="fas fa-save"></i>
                            حفظ
                        </button>
                        <button class="btn btn-primary" onclick="bookingReceiptScreen.printReceipt()">
                            <i class="fas fa-print"></i>
                            طباعة
                        </button>
                        <button class="btn btn-secondary" onclick="app.showScreen('home')">
                            <i class="fas fa-arrow-right"></i>
                            العودة
                        </button>
                    </div>
                </div>

                <div class="booking-form-container">
                    <form id="booking-form" class="booking-form">
                        <div class="form-section">
                            <h3 class="section-title">بيانات العميل</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="customer-name">اسم العميل *</label>
                                    <input type="text" id="customer-name" name="customerName" required>
                                </div>
                                <div class="form-group">
                                    <label for="customer-phone">رقم الهاتف *</label>
                                    <input type="tel" id="customer-phone" name="customerPhone" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3 class="section-title">تفاصيل الحجز</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="service-type">نوع الخدمة *</label>
                                    <select id="service-type" name="serviceType" required>
                                        <option value="">اختر نوع الخدمة</option>
                                        ${this.services.map(service => 
                                            `<option value="${service.name}" data-price="${service.price}">${service.name} - ${Utils.formatCurrency(service.price)}</option>`
                                        ).join('')}
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="event-date">تاريخ المناسبة</label>
                                    <input type="date" id="event-date" name="eventDate">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="event-description">وصف المناسبة</label>
                                    <input type="text" id="event-description" name="eventDescription" placeholder="مثال: حفل زفاف، عيد ميلاد، خطوبة">
                                </div>
                                <div class="form-group">
                                    <label for="hall-name">اسم القاعة</label>
                                    <input type="text" id="hall-name" name="hallName" placeholder="اسم القاعة أو المكان">
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3 class="section-title">التكلفة والدفع</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="total-amount">المبلغ الإجمالي *</label>
                                    <input type="number" id="total-amount" name="totalAmount" step="0.01" required>
                                </div>
                                <div class="form-group">
                                    <label for="deposit">العربون المدفوع</label>
                                    <input type="number" id="deposit" name="deposit" step="0.01" value="0">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="discount">الخصم</label>
                                    <input type="number" id="discount" name="discount" step="0.01" value="0">
                                </div>
                                <div class="form-group">
                                    <label for="delivery-date">تاريخ التسليم *</label>
                                    <input type="date" id="delivery-date" name="deliveryDate" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3 class="section-title">ملاحظات إضافية</h3>
                            <div class="form-group">
                                <label for="notes">الملاحظات</label>
                                <textarea id="notes" name="notes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                            </div>
                        </div>

                        <div class="summary-section">
                            <h3 class="section-title">ملخص الحجز</h3>
                            <div class="summary-card">
                                <div class="summary-row">
                                    <span class="summary-label">المبلغ الإجمالي:</span>
                                    <span class="summary-value" id="summary-total">0 دينار عراقي</span>
                                </div>
                                <div class="summary-row">
                                    <span class="summary-label">الخصم:</span>
                                    <span class="summary-value" id="summary-discount">0 دينار عراقي</span>
                                </div>
                                <div class="summary-row">
                                    <span class="summary-label">المبلغ بعد الخصم:</span>
                                    <span class="summary-value" id="summary-after-discount">0 دينار عراقي</span>
                                </div>
                                <div class="summary-row">
                                    <span class="summary-label">العربون المدفوع:</span>
                                    <span class="summary-value" id="summary-deposit">0 دينار عراقي</span>
                                </div>
                                <div class="summary-row total-row">
                                    <span class="summary-label">المبلغ المتبقي:</span>
                                    <span class="summary-value" id="summary-remaining">0 دينار عراقي</span>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        `;

        return content;
    }

    async loadData() {
        try {
            this.services = await db.getAll('services');
            this.customers = await db.getAll('customers');
        } catch (error) {
            console.error('Error loading data:', error);
            Utils.showNotification('خطأ في تحميل البيانات', 'error');
        }
    }

    onShow() {
        this.addEventListeners();
        this.setDefaultValues();
    }

    onHide() {
        this.removeEventListeners();
    }

    addEventListeners() {
        // Service type change
        const serviceSelect = document.getElementById('service-type');
        if (serviceSelect) {
            serviceSelect.addEventListener('change', this.handleServiceChange.bind(this));
        }

        // Amount calculations
        const amountFields = ['total-amount', 'deposit', 'discount'];
        amountFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.addEventListener('input', this.updateSummary.bind(this));
            }
        });

        // Customer phone lookup
        const phoneField = document.getElementById('customer-phone');
        if (phoneField) {
            phoneField.addEventListener('blur', this.lookupCustomer.bind(this));
        }

        // Form submission
        const form = document.getElementById('booking-form');
        if (form) {
            form.addEventListener('submit', this.handleFormSubmit.bind(this));
        }
    }

    removeEventListeners() {
        // Remove event listeners to prevent memory leaks
    }

    setDefaultValues() {
        // Set default delivery date (7 days from now)
        const deliveryDate = new Date();
        deliveryDate.setDate(deliveryDate.getDate() + 7);
        const deliveryField = document.getElementById('delivery-date');
        if (deliveryField) {
            deliveryField.value = Utils.formatDate(deliveryDate, 'YYYY-MM-DD');
        }

        // Set default event date (today)
        const eventDate = document.getElementById('event-date');
        if (eventDate) {
            eventDate.value = Utils.formatDate(new Date(), 'YYYY-MM-DD');
        }
    }

    handleServiceChange(e) {
        const selectedOption = e.target.selectedOptions[0];
        if (selectedOption && selectedOption.dataset.price) {
            const totalAmountField = document.getElementById('total-amount');
            if (totalAmountField) {
                totalAmountField.value = selectedOption.dataset.price;
                this.updateSummary();
            }
        }
    }

    updateSummary() {
        const totalAmount = parseFloat(document.getElementById('total-amount')?.value || 0);
        const discount = parseFloat(document.getElementById('discount')?.value || 0);
        const deposit = parseFloat(document.getElementById('deposit')?.value || 0);

        const afterDiscount = totalAmount - discount;
        const remaining = afterDiscount - deposit;

        // Update summary display
        document.getElementById('summary-total').textContent = Utils.formatCurrency(totalAmount);
        document.getElementById('summary-discount').textContent = Utils.formatCurrency(discount);
        document.getElementById('summary-after-discount').textContent = Utils.formatCurrency(afterDiscount);
        document.getElementById('summary-deposit').textContent = Utils.formatCurrency(deposit);
        document.getElementById('summary-remaining').textContent = Utils.formatCurrency(remaining);
    }

    async lookupCustomer(e) {
        const phone = e.target.value;
        if (!phone) return;

        try {
            const customer = await db.getByIndex('customers', 'phone', phone);
            if (customer) {
                const nameField = document.getElementById('customer-name');
                if (nameField && !nameField.value) {
                    nameField.value = customer.name;
                }
            }
        } catch (error) {
            console.error('Error looking up customer:', error);
        }
    }

    handleFormSubmit(e) {
        e.preventDefault();
        this.saveBooking();
    }

    async saveBooking() {
        try {
            const formData = new FormData(document.getElementById('booking-form'));
            const bookingData = {};
            
            for (let [key, value] of formData.entries()) {
                bookingData[key] = value;
            }

            // Validate required fields
            if (!bookingData.customerName || !bookingData.customerPhone || !bookingData.serviceType || !bookingData.totalAmount || !bookingData.deliveryDate) {
                Utils.showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
                return;
            }

            // Create booking object
            const booking = {
                customerName: bookingData.customerName,
                customerPhone: bookingData.customerPhone,
                serviceType: bookingData.serviceType,
                eventDescription: bookingData.eventDescription || null,
                hallName: bookingData.hallName || null,
                eventDate: bookingData.eventDate || null,
                totalAmount: parseFloat(bookingData.totalAmount),
                deposit: parseFloat(bookingData.deposit || 0),
                discount: parseFloat(bookingData.discount || 0),
                deliveryDate: bookingData.deliveryDate,
                bookingDate: Utils.formatDate(new Date(), 'YYYY-MM-DD'),
                status: 'inProgress',
                notes: bookingData.notes || null,
                bookedBy: auth.getCurrentUser()?.username,
                createdAt: new Date().toISOString()
            };

            // Save to database
            const bookingId = await db.insert('bookings', booking);
            
            // Save customer if new
            await this.saveCustomerIfNew(bookingData.customerName, bookingData.customerPhone);

            // Record payment if deposit was made
            if (booking.deposit > 0) {
                await this.recordDeposit(bookingId, booking.deposit, booking.customerName);
            }

            this.currentBooking = { ...booking, id: bookingId };
            
            Utils.showNotification('تم حفظ الحجز بنجاح', 'success');
            
            // Ask if user wants to print
            const shouldPrint = await Components.confirm('هل تريد طباعة وصل الحجز؟');
            if (shouldPrint) {
                this.printReceipt();
            }

        } catch (error) {
            console.error('Error saving booking:', error);
            Utils.showNotification('خطأ في حفظ الحجز', 'error');
        }
    }

    async saveCustomerIfNew(name, phone) {
        try {
            const existingCustomer = await db.getByIndex('customers', 'phone', phone);
            if (!existingCustomer) {
                const customer = {
                    name: name,
                    phone: phone,
                    createdAt: new Date().toISOString()
                };
                await db.insert('customers', customer);
            }
        } catch (error) {
            console.error('Error saving customer:', error);
        }
    }

    async recordDeposit(bookingId, amount, customerName) {
        try {
            const payment = {
                type: 'income',
                amount: amount,
                description: `عربون حجز - ${customerName}`,
                bookingId: bookingId,
                date: new Date().toISOString(),
                createdBy: auth.getCurrentUser()?.username
            };
            await db.insert('cashbox', payment);
        } catch (error) {
            console.error('Error recording deposit:', error);
        }
    }

    printReceipt() {
        if (!this.currentBooking) {
            Utils.showNotification('لا يوجد حجز للطباعة', 'warning');
            return;
        }

        // Create print window with receipt content
        const printWindow = window.open('', '_blank');
        printWindow.document.write(this.generateReceiptHTML());
        printWindow.document.close();
        printWindow.print();
    }

    generateReceiptHTML() {
        if (!this.currentBooking) return '';

        return `
            <!DOCTYPE html>
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>وصل حجز</title>
                <style>
                    body { font-family: Arial, sans-serif; direction: rtl; }
                    .receipt { max-width: 400px; margin: 0 auto; padding: 20px; }
                    .header { text-align: center; margin-bottom: 20px; }
                    .row { display: flex; justify-content: space-between; margin: 10px 0; }
                    .total { font-weight: bold; border-top: 1px solid #ccc; padding-top: 10px; }
                </style>
            </head>
            <body>
                <div class="receipt">
                    <div class="header">
                        <h2>وصل حجز</h2>
                        <p>استوديو التصوير</p>
                    </div>
                    <div class="row"><span>العميل:</span><span>${this.currentBooking.customerName}</span></div>
                    <div class="row"><span>الهاتف:</span><span>${this.currentBooking.customerPhone}</span></div>
                    <div class="row"><span>الخدمة:</span><span>${this.currentBooking.serviceType}</span></div>
                    <div class="row"><span>تاريخ الحجز:</span><span>${Utils.formatDate(this.currentBooking.bookingDate, 'DD/MM/YYYY')}</span></div>
                    <div class="row"><span>تاريخ التسليم:</span><span>${Utils.formatDate(this.currentBooking.deliveryDate, 'DD/MM/YYYY')}</span></div>
                    <div class="row"><span>المبلغ الإجمالي:</span><span>${Utils.formatCurrency(this.currentBooking.totalAmount)}</span></div>
                    <div class="row"><span>الخصم:</span><span>${Utils.formatCurrency(this.currentBooking.discount)}</span></div>
                    <div class="row"><span>العربون:</span><span>${Utils.formatCurrency(this.currentBooking.deposit)}</span></div>
                    <div class="row total"><span>المبلغ المتبقي:</span><span>${Utils.formatCurrency(this.currentBooking.totalAmount - this.currentBooking.discount - this.currentBooking.deposit)}</span></div>
                </div>
            </body>
            </html>
        `;
    }
}

// Global booking receipt screen instance
const bookingReceiptScreen = new BookingReceiptScreen();
