## 🎨 نظام إعدادات الشركة وشعار الشركة

تم تطوير نظام شامل لإدارة إعدادات الشركة واختيار شعار الشركة في الوصولات المطبوعة.

### ✨ الميزات الجديدة:

#### 📋 **صفحة إعدادات الشركة:**
- **الموقع:** القائمة الرئيسية ← إعدادات الشركة
- **المسار:** `lib/screens/company_settings_screen.dart`

#### 🖼️ **إدارة شعار الشركة:**

##### 1. **اختيار صورة الشعار:**
- دعم للصيغ: PNG, JPG, JPEG, GIF, SVG
- معاينة مباشرة للشعار المختار
- تحويل تلقائي إلى Base64 للطباعة
- التحقق من صحة الملف

##### 2. **الرموز التعبيرية البديلة:**
- مجموعة من الرموز: 📸, 🎥, 🖼️, 📷, 🎨, ✨, ⭐, 💎
- سهولة الاختيار بنقرة واحدة
- عرض مباشر في الواجهة

##### 3. **معلومات الشركة:**
- اسم الشركة *
- عنوان الشركة
- رقم الهاتف *
- البريد الإلكتروني

### 🔧 التطبيق التقني:

#### **خدمة إعدادات الشركة:**
```dart
// lib/services/company_settings_service.dart
class CompanySettingsService {
  static Future<Map<String, dynamic>> getCompanySettings()
  static Future<bool> saveCompanySettings(...)
  static Future<String> getCompanyName()
  static Future<Map<String, String?>> getCompanyLogo()
  static Future<bool> isCompanyConfigured()
}
```

#### **تكامل الوصولات:**
```dart
// الحصول على إعدادات الشركة تلقائياً
final companySettings = await CompanySettingsService.getCompanySettings();

// استخدام الإعدادات في الوصل
await HtmlReceiptService.generateHtmlReceipt(
  booking: booking,
  templateName: 'modern',
  // لا حاجة لتمرير customData - سيتم تحميلها تلقائياً
);
```

### 📱 واجهة المستخدم:

#### **أقسام الصفحة:**

1. **قسم الشعار:**
   - معاينة الشعار الحالي
   - زر اختيار صورة جديدة
   - زر حذف الشعار (إذا كان موجوداً)
   - اختيار رمز تعبيري بديل

2. **معلومات الشركة:**
   - حقول إدخال منسقة
   - التحقق من الحقول المطلوبة
   - حفظ تلقائي في التخزين المحلي

3. **معلومات الاتصال:**
   - رقم الهاتف مع تنسيق خاص
   - البريد الإلكتروني مع التحقق

4. **أزرار الإجراءات:**
   - حفظ الإعدادات
   - معاينة الوصل (قادمة قريباً)

### 🎯 مزايا النظام:

1. **سهولة الاستخدام:**
   - واجهة بديهية باللغة العربية
   - معاينة مباشرة للتغييرات
   - تحديثات فورية

2. **دعم تقني متقدم:**
   - تحويل الصور إلى Base64 للطباعة
   - دعم متعدد الصيغ
   - معالجة الأخطاء

3. **التكامل التلقائي:**
   - تحديث جميع الوصولات تلقائياً
   - لا حاجة لإعادة تكوين الإعدادات
   - حفظ آمن في التخزين المحلي

### 📝 مثال على الاستخدام:

```dart
// في أي مكان في التطبيق
final settings = await CompanySettingsService.getCompanySettings();

// طباعة وصل مع إعدادات الشركة
await HtmlReceiptService.printHtmlReceiptDirect(
  booking: booking,
  templateName: 'modern',
  // الإعدادات ستُحمّل تلقائياً
);
```

### 🚀 الخطوات التالية:

1. ✅ **تم:** إنشاء واجهة إعدادات الشركة
2. ✅ **تم:** تطوير خدمة حفظ الإعدادات
3. ✅ **تم:** تكامل نظام الشعار
4. 🔄 **قادم:** معاينة الوصل المباشرة
5. 🔄 **قادم:** تصدير إعدادات الشركة
6. 🔄 **قادم:** قوالب متعددة للشعار

---

### 📞 كيفية الوصول:
**القائمة الرئيسية** → **إعدادات الشركة** → **تخصيص الشعار والمعلومات**
