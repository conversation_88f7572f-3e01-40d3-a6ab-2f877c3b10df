class Cashbox {
  final String id;
  final String name;
  final String description;
  final String location;
  final String currency;
  final bool isActive;
  final String createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Cashbox({
    required this.id,
    required this.name,
    required this.description,
    required this.location,
    this.currency = 'IQD',
    this.isActive = true,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'location': location,
      'currency': currency,
      'is_active': isActive ? 1 : 0,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  factory Cashbox.fromMap(Map<String, dynamic> map) {
    return Cashbox(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      location: map['location'] ?? '',
      currency: map['currency'] ?? 'IQD',
      isActive: (map['is_active'] ?? 1) == 1,
      createdBy: map['created_by'] ?? '',
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }

  Cashbox copyWith({
    String? id,
    String? name,
    String? description,
    String? location,
    String? currency,
    bool? isActive,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Cashbox(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      location: location ?? this.location,
      currency: currency ?? this.currency,
      isActive: isActive ?? this.isActive,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Cashbox(id: $id, name: $name, description: $description, location: $location, currency: $currency, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Cashbox && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
