enum CashboxMovementType {
  deposit,    // إضافة
  withdrawal, // سحب
  transfer,   // تحويل
}

class CashboxMovement {
  final String id;
  final String cashboxId;
  final CashboxMovementType type;
  final double amount;
  final String description;
  final String? fromCashboxId;  // للتحويل فقط
  final String? toCashboxId;    // للتحويل فقط
  final String? reference;      // مرجع العملية
  final String performedBy;
  final DateTime createdAt;
  final String? notes;

  CashboxMovement({
    required this.id,
    required this.cashboxId,
    required this.type,
    required this.amount,
    required this.description,
    this.fromCashboxId,
    this.toCashboxId,
    this.reference,
    required this.performedBy,
    required this.createdAt,
    this.notes,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'cashbox_id': cashboxId,
      'type': type.name,
      'amount': amount,
      'description': description,
      'from_cashbox_id': fromCashboxId,
      'to_cashbox_id': toCashboxId,
      'reference': reference,
      'performed_by': performedBy,
      'created_at': createdAt.toIso8601String(),
      'notes': notes,
    };
  }

  factory CashboxMovement.fromMap(Map<String, dynamic> map) {
    return CashboxMovement(
      id: map['id'] ?? '',
      cashboxId: map['cashbox_id'] ?? '',
      type: CashboxMovementType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => CashboxMovementType.deposit,
      ),
      amount: (map['amount'] ?? 0.0).toDouble(),
      description: map['description'] ?? '',
      fromCashboxId: map['from_cashbox_id'],
      toCashboxId: map['to_cashbox_id'],
      reference: map['reference'],
      performedBy: map['performed_by'] ?? '',
      createdAt: DateTime.parse(map['created_at']),
      notes: map['notes'],
    );
  }

  String get typeDisplayName {
    switch (type) {
      case CashboxMovementType.deposit:
        return 'إضافة';
      case CashboxMovementType.withdrawal:
        return 'سحب';
      case CashboxMovementType.transfer:
        return 'تحويل';
    }
  }

  String get typeIcon {
    switch (type) {
      case CashboxMovementType.deposit:
        return '⬇️';
      case CashboxMovementType.withdrawal:
        return '⬆️';
      case CashboxMovementType.transfer:
        return '↔️';
    }
  }

  bool get isPositive => type == CashboxMovementType.deposit || 
                        (type == CashboxMovementType.transfer && toCashboxId == cashboxId);

  @override
  String toString() {
    return 'CashboxMovement(id: $id, type: $type, amount: $amount, cashboxId: $cashboxId)';
  }
}
