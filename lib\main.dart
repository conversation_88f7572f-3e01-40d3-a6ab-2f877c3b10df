import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:window_manager/window_manager.dart';
import 'dart:io';
import 'services/auth_service.dart';
import 'screens/login_screen.dart';
import 'screens/home_screen.dart';
import 'utils/app_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize window manager for desktop platforms
  if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
    await windowManager.ensureInitialized();
    
    // Configure window options
    WindowOptions windowOptions = const WindowOptions(
      size: Size(1200, 800),
      minimumSize: Size(800, 600),
      center: true,
      backgroundColor: Colors.transparent,
      skipTaskbar: false,
      titleBarStyle: TitleBarStyle.normal,
      windowButtonVisibility: false, // Hide default window buttons
    );
    
    windowManager.waitUntilReadyToShow(windowOptions, () async {
      await windowManager.show();
      await windowManager.focus();
      await windowManager.setResizable(false); // Disable manual resizing
      await windowManager.setMaximizable(true); // Allow maximize
      await windowManager.setMinimizable(true); // Allow minimize
    });
    
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  }
  
  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);
  
  runApp(const StudioManagementApp());
}

class StudioManagementApp extends StatelessWidget {
  const StudioManagementApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'إدارة استوديو التصوير',
      theme: AppTheme.lightTheme,
      debugShowCheckedModeBanner: false,
      
      // Arabic localization support
      localizationsDelegates: [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('ar', 'SA'), // Arabic - Saudi Arabia
        Locale('en', 'US'), // English - United States
      ],
      locale: const Locale('ar', 'SA'),
      
      home: const AppWrapper(),
    );
  }
}

class AppWrapper extends StatefulWidget {
  const AppWrapper({super.key});

  @override
  State<AppWrapper> createState() => _AppWrapperState();
}

class _AppWrapperState extends State<AppWrapper> {
  bool isMaximized = false;
  bool isFullScreen = false;

  @override
  void initState() {
    super.initState();
    _checkWindowState();
  }

  Future<void> _checkWindowState() async {
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      final maximized = await windowManager.isMaximized();
      final fullScreen = await windowManager.isFullScreen();
      setState(() {
        isMaximized = maximized;
        isFullScreen = fullScreen;
      });
    }
  }

  Future<void> _toggleMaximize() async {
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      if (isFullScreen) {
        await windowManager.setFullScreen(false);
        setState(() {
          isFullScreen = false;
          isMaximized = false;
        });
      } else if (isMaximized) {
        await windowManager.unmaximize();
        setState(() {
          isMaximized = false;
        });
      } else {
        await windowManager.maximize();
        setState(() {
          isMaximized = true;
        });
      }
    }
  }

  Future<void> _toggleFullScreen() async {
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      if (isFullScreen) {
        await windowManager.setFullScreen(false);
        setState(() {
          isFullScreen = false;
        });
      } else {
        await windowManager.setFullScreen(true);
        setState(() {
          isFullScreen = true;
          isMaximized = false;
        });
      }
    }
  }

  Future<void> _minimizeWindow() async {
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      await windowManager.minimize();
    }
  }

  Future<void> _closeWindow() async {
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      await windowManager.close();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GestureDetector(
        // Double tap to toggle fullscreen
        onDoubleTap: _toggleFullScreen,
        child: RawKeyboardListener(
          focusNode: FocusNode(),
          autofocus: true,
          onKey: (RawKeyEvent event) {
            // Press Escape to exit fullscreen
            if (event.isKeyPressed(LogicalKeyboardKey.escape) && isFullScreen) {
              _toggleFullScreen();
            }
          },
          child: Column(
            children: [
              // Custom title bar for desktop (hide in fullscreen)
              if ((Platform.isWindows || Platform.isLinux || Platform.isMacOS) && !isFullScreen)
                Container(
              height: 35,
              decoration: const BoxDecoration(
                color: Color(0xFF2196F3),
              ),
              child: Row(
                children: [
                  // App icon and title
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8),
                    child: Row(
                      children: [
                        Icon(
                          Icons.camera_alt,
                          color: Colors.white,
                          size: 20,
                        ),
                        SizedBox(width: 8),
                        Text(
                          'إدارة استوديو التصوير',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  // Window controls
                  _buildWindowButton(
                    Icons.minimize,
                    'تصغير',
                    _minimizeWindow,
                    Colors.orange,
                  ),
                  _buildWindowButton(
                    isMaximized ? Icons.fullscreen_exit : Icons.fullscreen,
                    isMaximized ? 'تصغير النافذة' : 'تكبير النافذة',
                    _toggleMaximize,
                    Colors.green,
                  ),
                  _buildWindowButton(
                    isFullScreen ? Icons.fullscreen_exit : Icons.crop_free,
                    isFullScreen ? 'خروج من ملء الشاشة' : 'ملء الشاشة',
                    _toggleFullScreen,
                    Colors.blue,
                  ),
                  _buildWindowButton(
                    Icons.close,
                    'إغلاق',
                    _closeWindow,
                    Colors.red,
                  ),
                ],
              ),
            ),
          // Main app content
          const Expanded(
            child: SplashScreen(),
          ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWindowButton(
    IconData icon,
    String tooltip,
    VoidCallback onPressed,
    Color hoverColor,
  ) {
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: onPressed,
        hoverColor: hoverColor.withOpacity(0.2),
        child: Container(
          width: 45,
          height: 35,
          alignment: Alignment.center,
          child: Icon(
            icon,
            color: Colors.white,
            size: 16,
          ),
        ),
      ),
    );
  }
}

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeInOut),
      ),
    );
    
    _scaleAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.5, curve: Curves.elasticOut),
      ),
    );
    
    _animationController.forward();
    _checkAuthStatus();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _checkAuthStatus() async {
    await Future.delayed(const Duration(milliseconds: 2500));
    
    final isLoggedIn = await AuthService.isLoggedIn();
    
    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              isLoggedIn ? const HomeScreen() : const LoginScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF64B5F6),
              Color(0xFF2196F3),
              Color(0xFF1976D2),
            ],
          ),
        ),
        child: Center(
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: ScaleTransition(
                  scale: _scaleAnimation,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(32),
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black26,
                              blurRadius: 20,
                              offset: Offset(0, 10),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.camera_alt,
                          size: 80,
                          color: Color(0xFF2196F3),
                        ),
                      ),
                      const SizedBox(height: 32),
                      const Text(
                        'إدارة استوديو التصوير',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'نظام متكامل لإدارة الاستوديو',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
