class ServiceOffer {
  final String id;
  final String name;
  final String description;
  final double price;
  final String duration;
  final String category;
  final bool isPopular;
  final List<String> features;
  final bool isActive;

  ServiceOffer({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.duration,
    required this.category,
    this.isPopular = false,
    this.features = const [],
    this.isActive = true,
  });

  factory ServiceOffer.fromMap(Map<String, dynamic> map) {
    return ServiceOffer(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      price: (map['price']).toDouble(),
      duration: map['duration'],
      category: map['category'],
      isPopular: map['is_popular'] == 1,
      features: map['features'] != null ? List<String>.from(map['features'].split(',')) : [],
      isActive: map['is_active'] == 1,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'duration': duration,
      'category': category,
      'is_popular': isPopular ? 1 : 0,
      'features': features.join(','),
      'is_active': isActive ? 1 : 0,
    };
  }

  @override
  String toString() {
    return 'ServiceOffer(id: $id, name: $name, price: $price)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ServiceOffer && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}

class Addition {
  final String id;
  final String name;
  final String description;
  final double price;
  final String category;
  final bool isActive;

  Addition({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.category,
    this.isActive = true,
  });

  factory Addition.fromMap(Map<String, dynamic> map) {
    return Addition(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      price: (map['price']).toDouble(),
      category: map['category'],
      isActive: map['is_active'] == 1,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'category': category,
      'is_active': isActive ? 1 : 0,
    };
  }

  @override
  String toString() {
    return 'Addition(id: $id, name: $name, price: $price)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Addition && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}
