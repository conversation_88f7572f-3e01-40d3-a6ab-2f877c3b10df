import 'dart:io';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import '../models/booking.dart';
import 'company_settings_service.dart';

import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';

class DeliveryReceiptService {
  static const String _templatesDir = 'delivery_templates';
  static const String _receiptsDir = 'delivery_receipts_html';

  /// Print HTML delivery receipt directly without saving file
  static Future<void> printDeliveryReceiptDirect({
    required Booking booking,
    required String templateName,
    String deliveryMethod = 'استلام من الاستوديو',
    String deliveryNotes = '',
    double additionalCost = 0.0,
    double paidOnDelivery = 0.0,
    Map<String, dynamic>? customData,
  }) async {
    final htmlTemplate = await _loadTemplate(templateName);
    final htmlContent = await _replacePlaceholders(
      htmlTemplate,
      booking,
      deliveryMethod: deliveryMethod,
      deliveryNotes: deliveryNotes,
      additionalCost: additionalCost,
      paidOnDelivery: paidOnDelivery,
      customData: customData,
    );
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async {
        return await Printing.convertHtml(
          format: format,
          html: htmlContent,
        );
      },
    );
  }

  /// Generate HTML delivery receipt from booking data
  static Future<String> generateDeliveryReceipt({
    required Booking booking,
    required String templateName,
    String deliveryMethod = 'استلام من الاستوديو',
    String deliveryNotes = '',
    double additionalCost = 0.0,
    double paidOnDelivery = 0.0,
    Map<String, dynamic>? customData,
  }) async {
    try {
      final htmlTemplate = await _loadTemplate(templateName);
      final htmlContent = await _replacePlaceholders(
        htmlTemplate,
        booking,
        deliveryMethod: deliveryMethod,
        deliveryNotes: deliveryNotes,
        additionalCost: additionalCost,
        paidOnDelivery: paidOnDelivery,
        customData: customData,
      );
      final htmlFilePath = await _saveHtmlFile(htmlContent, booking);
      return htmlFilePath;
    } catch (e) {
      print('Error generating delivery receipt: $e');
      rethrow;
    }
  }

  static Future<String> _loadTemplate(String templateName) async {
    try {
      final assetPath = 'assets/templates/delivery/$templateName/index.html';
      final htmlContent = await rootBundle.loadString(assetPath);
      final cssPath = 'assets/templates/delivery/$templateName/styles.css';
      final cssContent = await rootBundle.loadString(cssPath);
      final updatedHtml = htmlContent.replaceFirst(
        '<link rel="stylesheet" href="styles.css">',
        '<style>$cssContent</style>',
      );
      return updatedHtml;
    } catch (e) {
      print('Failed to load delivery template from assets: $e');
      try {
        final directory = await getApplicationDocumentsDirectory();
        final templatePath = '${directory.path}/$_templatesDir/$templateName.html';
        final templateFile = File(templatePath);
        if (await templateFile.exists()) {
          return await templateFile.readAsString();
        }
      } catch (e) {
        print('Failed to load custom delivery template: $e');
      }
      return await _getBuiltInDeliveryTemplate();
    }
  }

  static Future<String> _replacePlaceholders(
    String htmlTemplate,
    Booking booking, {
    String deliveryMethod = 'استلام من الاستوديو',
    String deliveryNotes = '',
    double additionalCost = 0.0,
    double paidOnDelivery = 0.0,
    Map<String, dynamic>? customData,
  }) async {
    final now = DateTime.now();
    final dateFormat = DateFormat('yyyy/MM/dd', 'ar');
    final timeFormat = DateFormat('HH:mm', 'ar');
    
    // Load company settings from CompanySettingsService
    final companySettings = await CompanySettingsService.getCompanySettings();
    
    // Debug: طباعة إعدادات الشركة للتحقق
    print('===== DELIVERY RECEIPT SERVICE DEBUG =====');
    print('Company settings loaded: $companySettings');
    
    final companyName = customData?['companyName'] ?? companySettings['companyName'] ?? 'استوديو الذكريات الجميلة';
    final companyLogo = customData?['companyLogo'] ?? companySettings['companyLogo'] ?? '📸';
    final companyLogoPath = customData?['companyLogoPath'] ?? companySettings['companyLogoPath'];
    final companyAddress = customData?['companyAddress'] ?? companySettings['companyAddress'] ?? 'شارع الرئيسي، المدينة، العراق';
    final companyPhone = customData?['companyPhone'] ?? companySettings['companyPhone'] ?? '0555123456';
    final companyEmail = customData?['companyEmail'] ?? companySettings['companyEmail'] ?? '<EMAIL>';
    final deliveredBy = booking.bookedBy ?? customData?['deliveredBy'] ?? 'غير محدد'; // اسم الموظف الذي قام بالتسليم
    
    print('Company name in delivery receipt: "$companyName"');
    print('Company address in delivery receipt: "$companyAddress"');
    print('==========================================');
    
    // Generate logo HTML based on whether we have an image path or not
    final logoHtml = await _generateLogoHtml(companyLogoPath, companyLogo);
    
    final totalAmount = booking.totalAmount;
    final finalAmount = totalAmount + additionalCost;
    final receiptNumber = await booking.formattedReceiptNumber; // استخدام رقم الحجز الأصلي
    
    // Calculate payment details
    final totalPaid = booking.deposit + paidOnDelivery;
    final remainingAmount = finalAmount - totalPaid;
    
    final selectedOffers = _formatDeliveryItemsAsTable(booking.selectedOffers ?? [], additionalCost);
    final selectedOffersText = (booking.selectedOffers ?? []).join(', ');
    
    final replacements = <String, String>{
      '{{COMPANY_NAME}}': companyName,
      '{{COMPANY_LOGO}}': logoHtml,
      '{{COMPANY_ADDRESS}}': companyAddress,
      '{{COMPANY_PHONE}}': companyPhone,
      '{{COMPANY_EMAIL}}': companyEmail,
      '{{DELIVERED_BY}}': deliveredBy,
      '{{RECEIPT_TITLE}}': 'وصل تسليم',
      '{{RECEIPT_NUMBER}}': receiptNumber,
      '{{DELIVERY_DATE}}': dateFormat.format(now),
      '{{DELIVERY_TIME}}': timeFormat.format(now),
      '{{CUSTOMER_NAME}}': booking.customerName,
      '{{CUSTOMER_PHONE}}': booking.customerPhone,
      '{{SERVICE_TYPE}}': booking.serviceType,
      '{{HALL_NAME}}': booking.hallName ?? '',
      '{{BOOKING_DATE}}': dateFormat.format(booking.eventDate ?? booking.bookingDate),
      '{{ORIGINAL_DELIVERY_DATE}}': dateFormat.format(booking.deliveryDate),
      '{{EVENT_DESCRIPTION}}': booking.eventDescription ?? '',
      '{{DELIVERY_METHOD}}': deliveryMethod,
      '{{DELIVERY_NOTES}}': deliveryNotes,
      '{{SELECTED_OFFERS}}': selectedOffers,
      '{{SELECTED_OFFERS_TEXT}}': selectedOffersText,
      '{{ORIGINAL_AMOUNT}}': totalAmount.toStringAsFixed(2),
      '{{ADDITIONAL_COST}}': additionalCost.toStringAsFixed(2),
      '{{FINAL_AMOUNT}}': finalAmount.toStringAsFixed(2),
      '{{DEPOSIT_PAID}}': booking.deposit.toStringAsFixed(2),
      '{{PAID_ON_DELIVERY}}': paidOnDelivery.toStringAsFixed(2),
      '{{TOTAL_PAID}}': totalPaid.toStringAsFixed(2),
      '{{REMAINING_AMOUNT}}': remainingAmount.toStringAsFixed(2),
      '{{BOOKING_NOTES}}': booking.notes ?? '',
    };

    String result = htmlTemplate;
    replacements.forEach((placeholder, value) {
      result = result.replaceAll(placeholder, value);
    });

    result = _handleConditionalBlocks(result, {
      'ADDITIONAL_COST': additionalCost > 0,
      'PAID_ON_DELIVERY': paidOnDelivery > 0,
      'REMAINING_AMOUNT': remainingAmount > 0,
      'DELIVERY_NOTES': deliveryNotes.isNotEmpty,
      'BOOKING_NOTES': (booking.notes ?? '').isNotEmpty,
      'EVENT_DESCRIPTION': (booking.eventDescription ?? '').isNotEmpty,
      'HALL_NAME': (booking.hallName ?? '').isNotEmpty,
      'SELECTED_OFFERS': (booking.selectedOffers ?? []).isNotEmpty,
    });

    return result;
  }

  static String _handleConditionalBlocks(String html, Map<String, bool> conditions) {
    conditions.forEach((condition, show) {
      final pattern = RegExp(
        r'\{\{#if\s+' + condition + r'\}\}(.*?)\{\{/if\}\}',
        multiLine: true,
        dotAll: true,
      );
      if (show) {
        html = html.replaceAllMapped(pattern, (match) => match.group(1) ?? '');
      } else {
        html = html.replaceAll(pattern, '');
      }
    });
    return html;
  }

  /// Format delivery items as table rows with additional costs
  static String _formatDeliveryItemsAsTable(List<String> items, double additionalCost) {
    if (items.isEmpty && additionalCost == 0) return '';
    
    double total = 0;
    final tableRows = <String>[];
    
    // Add original items
    for (int i = 0; i < items.length; i++) {
      final index = i + 1;
      final item = items[i];
      
      String serviceName = item;
      String description = '';
      String price = '-';
      
      // Extract price using enhanced pattern - New format: "service name - price د.ع"
      final newFormatPattern = RegExp(r'^(.+?)\s*-\s*(\d+(?:\.\d+)?)\s*(د\.ع)\s*$');
      final newFormatMatch = newFormatPattern.firstMatch(item);
      
      if (newFormatMatch != null) {
        serviceName = newFormatMatch.group(1)?.trim() ?? item;
        final priceValue = newFormatMatch.group(2)?.trim() ?? '0';
        final currency = newFormatMatch.group(3)?.trim() ?? 'د.ع';
        price = '$priceValue $currency';
        
        final priceNum = double.tryParse(priceValue);
        if (priceNum != null) {
          total += priceNum;
        }
      } else {
        // Try old format
        final parts = item.split(' - ');
        
        if (parts.length >= 2) {
          serviceName = parts[0].trim();
          
          final lastPart = parts.last.trim();
          final pricePattern = RegExp(r'^(\d+(?:\.\d+)?)\s*(د\.ع|ريال|ر\.س|دينار)?\s*$');
          final priceMatch = pricePattern.firstMatch(lastPart);
          
          if (priceMatch != null) {
            final priceValue = priceMatch.group(1)?.trim() ?? '0';
            final currency = priceMatch.group(2)?.trim() ?? 'د.ع';
            price = '$priceValue $currency';
            
            if (parts.length > 2) {
              description = parts.sublist(1, parts.length - 1).join(' - ').trim();
            }
            
            final priceNum = double.tryParse(priceValue);
            if (priceNum != null) {
              total += priceNum;
            }
          } else {
            description = parts.sublist(1).join(' - ').trim();
          }
        }
      }
      
      if (price != '-' && !price.contains('د.ع') && !price.contains('ريال') && !price.contains('ر.س')) {
        price = '$price د.ع';
      }
      
      tableRows.add('''
                        <tr>
                            <td style="text-align:right">$index</td>
                            <td style="text-align:right">$serviceName</td>
                            <td style="text-align:left; font-weight:bold; color:#007bff;">$price</td>
                        </tr>''');
    }
    
    // Add additional cost if present
    if (additionalCost > 0) {
      final index = items.length + 1;
      total += additionalCost;
      tableRows.add('''
                        <tr style="background-color: #fff3cd;">
                            <td style="text-align:right">$index</td>
                            <td style="text-align:right">رسوم إضافية (توصيل/أخرى)</td>
                            <td style="text-align:left; font-weight:bold; color:#856404;">${additionalCost.toStringAsFixed(0)} د.ع</td>
                        </tr>''');
    }
    
    final totalRow = '''
                        <tr class="total-row">
                            <td colspan="2" style="text-align:center; font-weight:bold;">إجمالي مبلغ التسليم</td>
                            <td style="text-align:left; font-weight:bold; color:#007bff; font-size:14px;">${total.toStringAsFixed(0)} د.ع</td>
                        </tr>''';
    
    return '${tableRows.join('\n')}\n$totalRow';
  }

  /// Generate logo HTML based on image path or fallback text/emoji
  static Future<String> _generateLogoHtml(String? imagePath, String fallbackLogo) async {
    if (imagePath != null && imagePath.isNotEmpty) {
      if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
        return '<img src="$imagePath" alt="شعار الشركة" onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'block\';" /><span class="company-logo-text" style="display:none;">$fallbackLogo</span>';
      } else {
        final base64Image = await _imageToBase64(imagePath);
        if (base64Image != null) {
          return '<img src="$base64Image" alt="شعار الشركة" />';
        } else {
          return '<img src="file:///$imagePath" alt="شعار الشركة" onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'block\';" /><span class="company-logo-text" style="display:none;">$fallbackLogo</span>';
        }
      }
    } else {
      return '<span class="company-logo-text">$fallbackLogo</span>';
    }
  }

  /// Convert image file to base64 for embedding in HTML
  static Future<String?> _imageToBase64(String imagePath) async {
    try {
      final file = File(imagePath);
      if (await file.exists()) {
        final bytes = await file.readAsBytes();
        final base64String = base64Encode(bytes);
        final extension = imagePath.split('.').last.toLowerCase();
        String mimeType = 'image/png';
        
        switch (extension) {
          case 'jpg':
          case 'jpeg':
            mimeType = 'image/jpeg';
            break;
          case 'png':
            mimeType = 'image/png';
            break;
          case 'gif':
            mimeType = 'image/gif';
            break;
          case 'svg':
            mimeType = 'image/svg+xml';
            break;
        }
        
        return 'data:$mimeType;base64,$base64String';
      }
    } catch (e) {
      print('Error converting image to base64: $e');
    }
    return null;
  }

  static Future<String> _saveHtmlFile(
    String htmlContent,
    Booking booking,
  ) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final receiptsPath = '${directory.path}/$_receiptsDir';
      final receiptsDir = Directory(receiptsPath);
      if (!await receiptsDir.exists()) {
        await receiptsDir.create(recursive: true);
      }
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final customerName = booking.customerName.replaceAll(' ', '_');
      final filename = 'delivery_receipt_${customerName}_$timestamp.html';
      final filePath = '$receiptsPath/$filename';
      final file = File(filePath);
      await file.writeAsString(htmlContent, encoding: utf8);
      return filePath;
    } catch (e) {
      print('Error saving delivery receipt HTML file: $e');
      rethrow;
    }
  }

  static Future<String> _getBuiltInDeliveryTemplate() async {
    return '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{RECEIPT_TITLE}}</title>
    <style>
        @page {
            size: A4;
            margin: 15mm;
        }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background: white; direction: rtl; font-size: 12px; line-height: 1.4; color: #333; }
        .receipt { width: 100%; max-width: 210mm; min-height: 297mm; margin: 0 auto; background: white; padding: 10mm; box-sizing: border-box; position: relative; }
        .header { text-align: center; margin-bottom: 20px; border-bottom: 3px solid #28a745; padding-bottom: 15px; position: relative; min-height: 80px; background: linear-gradient(135deg, #f8f9fa, #e9ecef); border-radius: 8px; }
        .company-logo { position: absolute; left: 10px; top: 10px; margin-bottom: 10px; }
        .company-logo img { max-width: 80px; max-height: 80px; width: auto; height: auto; border-radius: 8px; box-shadow: 0 2px 6px rgba(0,0,0,0.15); }
        .company-logo-text { font-size: 48px; }
        .company-name { position: absolute; right: 10px; top: 10px; font-size: 16px; color: #28a745; font-weight: bold; max-width: 200px; text-align: right; }
        .company-address { font-size: 12px; color: #666; margin-bottom: 8px; line-height: 1.4; }
        .receipt-title { font-size: 20px; color: #28a745; margin-bottom: 8px; font-weight: 700; margin-top: 15px; text-shadow: 1px 1px 2px rgba(0,0,0,0.1); }
        .receipt-number { font-size: 14px; color: #666; background: #28a745; color: white; padding: 5px 15px; border-radius: 15px; display: inline-block; margin-top: 5px; }
        .delivery-info { background: linear-gradient(135deg, #d4edda, #c3e6cb); border: 2px solid #28a745; border-radius: 8px; padding: 15px; margin: 15px 0; text-align: center; }
        .delivery-info h3 { color: #155724; margin: 0 0 10px 0; font-size: 16px; }
        .delivery-date { font-size: 18px; font-weight: bold; color: #155724; }
        .delivery-method { font-size: 14px; color: #155724; margin-top: 5px; }
        .main-content { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px; }
        .section { background: #f9f9f9; padding: 12px; border-radius: 6px; border-left: 4px solid #28a745; }
        .section-title { font-size: 14px; font-weight: bold; color: #28a745; margin-bottom: 8px; border-bottom: 1px solid #ddd; padding-bottom: 4px; }
        .info-row { display: flex; justify-content: space-between; padding: 6px 0; border-bottom: 1px solid #eee; font-size: 11px; }
        .info-row:last-child { border-bottom: none; }
        .label { font-weight: 600; color: #444; min-width: 80px; }
        .value { color: #666; text-align: left; flex: 1; }
        .services-section { grid-column: 1 / -1; margin: 15px 0; }
        .services-table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 11px; }
        .services-table th, .services-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        .services-table th { background: #28a745; color: white; font-weight: bold; font-size: 12px; }
        .services-table th:nth-child(4), .services-table td:nth-child(4) { text-align: left; font-weight: bold; color: #28a745; min-width: 100px; background-color: #f8f9fa; }
        .services-table tr:nth-child(even) { background: #f9f9f9; }
        .services-table tr:hover { background: #f5f5f5; }
        .services-table .total-row { font-weight: bold; background: #e9ecef !important; }
        .services-table .total-row td { font-size: 13px; color: #28a745; }
        .services-table .total-row td:nth-child(4) { background-color: #28a745; color: white !important; }
        .payment-section { background: linear-gradient(135deg, #28a745, #20923a); color: white; padding: 15px; margin: 20px 0; text-align: center; border-radius: 8px; box-shadow: 0 3px 6px rgba(40,167,69,0.3); }
        .payment-title { font-size: 16px; margin-bottom: 15px; opacity: 0.9; font-weight: bold; }
        .payment-details { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; font-size: 12px; margin-top: 10px; }
        .payment-item { text-align: center; padding: 8px; background: rgba(255,255,255,0.15); border-radius: 6px; }
        .payment-item .label { font-size: 10px; opacity: 0.8; margin-bottom: 4px; }
        .payment-item .amount { font-size: 14px; font-weight: bold; }
        .final-amount { font-size: 20px; font-weight: bold; margin: 10px 0; background: rgba(255,255,255,0.2); padding: 10px; border-radius: 6px; }
        .status-delivered { background: #d4edda; color: #155724; padding: 8px 16px; border-radius: 20px; font-size: 12px; font-weight: bold; display: inline-block; margin: 10px 0; }
        .notes-section { background: #fff3cd; border: 2px solid #ffeaa7; border-radius: 6px; padding: 12px; margin: 15px 0; }
        .notes-title { font-weight: bold; color: #856404; margin-bottom: 6px; font-size: 12px; }
        .notes-content { color: #856404; font-size: 11px; line-height: 1.5; }
        .footer { text-align: center; margin-top: auto; padding-top: 20px; border-top: 2px solid #28a745; color: #666; font-size: 10px; }
        .footer-line { margin: 3px 0; }
        .thank-you { font-weight: bold; color: #28a745; margin: 8px 0; font-size: 14px; }
        .signature-section { display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin: 30px 0; }
        .signature-box { text-align: center; padding: 15px; border: 2px dashed #28a745; border-radius: 8px; background: #f8f9fa; }
        .signature-title { font-weight: bold; color: #28a745; margin-bottom: 30px; }
        .signature-line { border-bottom: 2px solid #28a745; margin: 10px 0; height: 40px; }
        @media print { body { background: white !important; -webkit-print-color-adjust: exact; print-color-adjust: exact; } .receipt { box-shadow: none !important; min-height: auto; } @page { margin: 10mm; } }
        @media screen and (max-width: 768px) { .main-content { grid-template-columns: 1fr; } .payment-details { grid-template-columns: 1fr; } .signature-section { grid-template-columns: 1fr; } }
    </style>
</head>
<body>
    <div class="receipt">
        <div class="header">
            <div class="company-logo">{{COMPANY_LOGO}}</div>
            <div class="company-name">{{COMPANY_NAME}}</div>
            <div class="company-address">{{COMPANY_ADDRESS}}</div>
            <h2 class="receipt-title">{{RECEIPT_TITLE}}</h2>
            <span class="receipt-number">رقم الوصل: {{RECEIPT_NUMBER}}</span>
        </div>
        
        <div class="delivery-info">
            <h3>✅ تم التسليم بنجاح</h3>
            <div class="delivery-date">{{DELIVERY_DATE}} في {{DELIVERY_TIME}}</div>
            <div class="delivery-method">طريقة التسليم: {{DELIVERY_METHOD}}</div>
        </div>

        <div class="main-content">
            <div class="section">
                <div class="section-title">معلومات العميل</div>
                <div class="info-row"><span class="label">الاسم:</span><span class="value">{{CUSTOMER_NAME}}</span></div>
                <div class="info-row"><span class="label">الهاتف:</span><span class="value">{{CUSTOMER_PHONE}}</span></div>
                <div class="info-row"><span class="label">سُلم بواسطة:</span><span class="value">{{DELIVERED_BY}}</span></div>
            </div>
            <div class="section">
                <div class="section-title">تفاصيل الطلب الأصلي</div>
                <div class="info-row"><span class="label">نوع الخدمة:</span><span class="value">{{SERVICE_TYPE}}</span></div>
                {{#if HALL_NAME}}
                <div class="info-row"><span class="label">اسم القاعة:</span><span class="value">{{HALL_NAME}}</span></div>
                {{/if}}
                <div class="info-row"><span class="label">تاريخ الحجز:</span><span class="value">{{BOOKING_DATE}}</span></div>
                <div class="info-row"><span class="label">موعد التسليم المقرر:</span><span class="value">{{ORIGINAL_DELIVERY_DATE}}</span></div>
            </div>
            
            {{#if EVENT_DESCRIPTION}}
            <div class="section" style="grid-column: 1 / -1;">
                <div class="section-title">تفاصيل المناسبة</div>
                <div style="padding: 8px 0; font-size: 11px; line-height: 1.5;">{{EVENT_DESCRIPTION}}</div>
            </div>
            {{/if}}
            
            {{#if SELECTED_OFFERS}}
            <div class="section services-section">
                <div class="section-title">تفاصيل الخدمات المُسلَّمة</div>
                <table class="services-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>اسم الخدمة</th>
                            <th>الوصف</th>
                            <th>السعر</th>
                        </tr>
                    </thead>
                    <tbody>
                        {{SELECTED_OFFERS}}
                    </tbody>
                </table>
            </div>
            {{/if}}
        </div>

        <div class="payment-section">
            <div class="payment-title">💰 تفاصيل الدفع النهائية</div>
            <div class="payment-details">
                <div class="payment-item">
                    <div class="label">المبلغ الأصلي</div>
                    <div class="amount">{{ORIGINAL_AMOUNT}} د.ع</div>
                </div>
                {{#if ADDITIONAL_COST}}
                <div class="payment-item">
                    <div class="label">رسوم إضافية</div>
                    <div class="amount">{{ADDITIONAL_COST}} د.ع</div>
                </div>
                {{/if}}
                <div class="payment-item">
                    <div class="label">العربون المدفوع</div>
                    <div class="amount">{{DEPOSIT_PAID}} د.ع</div>
                </div>
            </div>
            {{#if PAID_ON_DELIVERY}}
            <div class="payment-details" style="margin-top: 10px;">
                <div class="payment-item">
                    <div class="label">مدفوع عند التسليم</div>
                    <div class="amount">{{PAID_ON_DELIVERY}} د.ع</div>
                </div>
                <div class="payment-item">
                    <div class="label">إجمالي المدفوع</div>
                    <div class="amount">{{TOTAL_PAID}} د.ع</div>
                </div>
                {{#if REMAINING_AMOUNT}}
                <div class="payment-item" style="background: rgba(255,193,7,0.3);">
                    <div class="label">المتبقي</div>
                    <div class="amount">{{REMAINING_AMOUNT}} د.ع</div>
                </div>
                {{/if}}
            </div>
            {{/if}}
            <div class="final-amount">المبلغ النهائي: {{FINAL_AMOUNT}} د.ع</div>
        </div>

        {{#if DELIVERY_NOTES}}
        <div class="notes-section">
            <div class="notes-title">ملاحظات التسليم</div>
            <div class="notes-content">{{DELIVERY_NOTES}}</div>
        </div>
        {{/if}}

        {{#if BOOKING_NOTES}}
        <div class="notes-section">
            <div class="notes-title">ملاحظات الحجز الأصلي</div>
            <div class="notes-content">{{BOOKING_NOTES}}</div>
        </div>
        {{/if}}

        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-title">توقيع العميل</div>
                <div class="signature-line"></div>
                <div style="margin-top: 10px; font-size: 10px; color: #666;">تأكيد استلام الطلب كاملاً</div>
            </div>
            <div class="signature-box">
                <div class="signature-title">توقيع المُسلِّم</div>
                <div class="signature-line"></div>
                <div style="margin-top: 10px; font-size: 10px; color: #666;">{{DELIVERED_BY}}</div>
            </div>
        </div>

        <div class="footer"> 
            <div class="footer-line">{{COMPANY_PHONE}} | {{COMPANY_EMAIL}}</div>
            <div class="thank-you">شكراً لثقتكم بخدماتنا - تم التسليم بنجاح ✅</div>
            <div class="footer-line">تاريخ التسليم: {{DELIVERY_DATE}} في {{DELIVERY_TIME}} | سُلم بواسطة: {{DELIVERED_BY}}</div>
        </div>
    </div>
</body>
</html>''';
  }

  /// Open HTML file in default browser
  static Future<void> openHtmlFile(String filePath) async {
    final file = File(filePath);
    if (await file.exists()) {
      if (Platform.isWindows) {
        await Process.run('cmd', ['/c', 'start', 'file:///$filePath']);
      } else if (Platform.isMacOS) {
        await Process.run('open', [filePath]);
      } else if (Platform.isLinux) {
        await Process.run('xdg-open', [filePath]);
      }
    }
  }

  /// Get supported image formats for logo selection
  static List<String> getSupportedImageFormats() {
    return ['png', 'jpg', 'jpeg', 'gif', 'svg'];
  }

  /// Validate if a file is a supported image format
  static bool isValidImageFile(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();
    return getSupportedImageFormats().contains(extension);
  }

  /// Clean up old delivery receipts
  static Future<void> cleanupOldDeliveryReceipts({int daysToKeep = 30}) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final receiptsPath = '${directory.path}/$_receiptsDir';
      final receiptsDir = Directory(receiptsPath);
      if (!await receiptsDir.exists()) return;
      final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));
      final files = await receiptsDir.list().toList();
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          if (stat.modified.isBefore(cutoffDate)) {
            await file.delete();
            print('Deleted old delivery receipt: ${file.path}');
          }
        }
      }
    } catch (e) {
      print('Error cleaning up old delivery receipts: $e');
    }
  }
}
