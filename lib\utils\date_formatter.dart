import 'package:intl/intl.dart';

class DateFormatter {
  /// تنسيق الوقت بنظام 12 ساعة مع ص/م
  static String formatTime12Hour(DateTime dateTime) {
    final hour = dateTime.hour;
    final minute = dateTime.minute;
    final second = dateTime.second;
    
    // تحويل إلى نظام 12 ساعة
    String displayHour;
    String period;
    
    if (hour == 0) {
      displayHour = '12';
      period = 'ص';
    } else if (hour < 12) {
      displayHour = hour.toString();
      period = 'ص';
    } else if (hour == 12) {
      displayHour = '12';
      period = 'م';
    } else {
      displayHour = (hour - 12).toString();
      period = 'م';
    }
    
    return '${displayHour.padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}:${second.toString().padLeft(2, '0')} $period';
  }

  /// تنسيق الوقت المختصر بنظام 12 ساعة مع ص/م (بدون ثواني)
  static String formatTimeShort12Hour(DateTime dateTime) {
    final hour = dateTime.hour;
    final minute = dateTime.minute;
    
    // تحويل إلى نظام 12 ساعة
    String displayHour;
    String period;
    
    if (hour == 0) {
      displayHour = '12';
      period = 'ص';
    } else if (hour < 12) {
      displayHour = hour.toString();
      period = 'ص';
    } else if (hour == 12) {
      displayHour = '12';
      period = 'م';
    } else {
      displayHour = (hour - 12).toString();
      period = 'م';
    }
    
    return '${displayHour.padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  /// تنسيق التاريخ والوقت بنظام 12 ساعة
  static String formatDateTimeShort(DateTime dateTime) {
    final dateStr = DateFormat('dd/MM/yyyy').format(dateTime);
    final timeStr = formatTimeShort12Hour(dateTime);
    return '$dateStr - $timeStr';
  }

  /// تنسيق التاريخ والوقت الكامل بنظام 12 ساعة
  static String formatDateTimeFull(DateTime dateTime) {
    final dateStr = DateFormat('dd/MM/yyyy').format(dateTime);
    final timeStr = formatTime12Hour(dateTime);
    return '$dateStr - $timeStr';
  }
}
