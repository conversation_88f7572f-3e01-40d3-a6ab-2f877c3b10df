# دليل المتغيرات المتاحة في نظام الصور

## مقدمة
يوفر نظام حفظ الوصولات كصور مجموعة شاملة من المتغيرات التي يمكن إضافتها إلى قوالب الوصولات. هذا الدليل يوضح جميع المتغيرات المتاحة وكيفية استخدامها.

## كيفية عمل النظام
- يتم حفظ جميع الوصولات كصور PNG عالية الجودة (2480 × 3508 بكسل، A4 بدقة 300 DPI)
- يحفظ النظام الصور في مجلد `Documents/receipts/`
- يتم فتح الصور تلقائياً بعد الحفظ
- أسماء الملفات تحتوي على تفاصيل واضحة (نوع الوصل، اسم العميل، التاريخ والوقت)

## المتغيرات المتاحة لوصولات الحجز

### معلومات أساسية
- `title`: عنوان الوصل (مثال: "وصل حجز")
- `studio_name`: اسم الاستوديو (افتراضي: "استوديو الذكريات الجميلة")
- `receipt_number`: رقم الوصل المُولد تلقائياً

### معلومات العميل
- `customer_name`: اسم العميل
- `customer_phone`: رقم هاتف العميل

### تفاصيل الحجز
- `booking_id` / `booking_number`: رقم الحجز الفريد
- `booking_date`: تاريخ إنشاء الحجز
- `delivery_date`: تاريخ التسليم المطلوب
- `event_date`: تاريخ المناسبة (إذا كان محدداً)
- `event_description`: وصف المناسبة
- `service_type`: نوع الخدمة المطلوبة

### الخدمات والإضافات
- `selected_offers`: العروض المختارة (مفصولة بفواصل)
- `additions`: الإضافات المطلوبة (مفصولة بفواصل)

### المبالغ المالية
- `total_amount`: المبلغ الكلي (بالريال السعودي)
- `deposit`: العربون المدفوع (بالريال السعودي)
- `discount`: مبلغ الخصم (بالريال السعودي)
- `remaining_amount`: المبلغ المتبقي (بالريال السعودي)

### معلومات إدارية
- `booked_by`: الشخص الذي أنشأ الحجز
- `status`: حالة الحجز (قيد العمل، تم التسليم، ملغي)
- `notes`: الملاحظات الإضافية

### معلومات التوقيت
- `created_at`: تاريخ إنشاء الحجز
- `updated_at`: تاريخ آخر تحديث للحجز
- `print_date`: تاريخ ووقت طباعة الوصل
- `print_time`: وقت الطباعة فقط

## المتغيرات المتاحة لوصولات الدفع

### معلومات أساسية
- `title`: عنوان الوصل (مثال: "وصل دفع")
- `studio_name`: اسم الاستوديو
- `receipt_number`: رقم الوصل المُولد تلقائياً

### معلومات العميل والحجز
- `customer_name`: اسم العميل
- `customer_phone`: رقم هاتف العميل
- `booking_id` / `booking_number`: رقم الحجز المرتبط بالدفع

### تفاصيل الحجز
- `booking_date`: تاريخ إنشاء الحجز
- `delivery_date`: تاريخ التسليم
- `event_date`: تاريخ المناسبة
- `event_description`: وصف المناسبة
- `service_type`: نوع الخدمة
- `selected_offers`: العروض المختارة
- `additions`: الإضافات المطلوبة

### معلومات الدفع
- `payment_amount`: مبلغ الدفعة الحالية (بالريال السعودي)
- `payment_date`: تاريخ الدفع
- `payment_time`: وقت الدفع

### المبالغ المالية
- `total_amount`: المبلغ الكلي للحجز
- `deposit`: العربون الأولي
- `discount`: مبلغ الخصم
- `remaining_amount`: المبلغ المتبقي بعد هذه الدفعة

### معلومات إضافية
- `booked_by`: منشئ الحجز
- `status`: حالة الحجز
- `notes`: الملاحظات
- `print_date`: تاريخ ووقت الطباعة
- `print_time`: وقت الطباعة فقط

## أمثلة على القيم المعروضة

### معلومات العميل
```
customer_name: "أحمد محمد علي"
customer_phone: "05012345678"
```

### المبالغ
```
total_amount: "2500.00 ر.س"
deposit: "500.00 ر.س"
discount: "100.00 ر.س"
remaining_amount: "2000.00 ر.س"
payment_amount: "1000.00 ر.س"
```

### التواريخ
```
booking_date: "2025/01/15"
delivery_date: "2025/01/20"
event_date: "2025/01/18"
print_date: "2025/01/26 14:30"
print_time: "14:30:25"
```

### النصوص العربية
```
service_type: "تصوير زفاف"
event_description: "حفل زفاف"
status: "قيد العمل"
notes: "التصوير يشمل الحناء والزفة"
```

### القوائم
```
selected_offers: "عرض التصوير الكامل, عرض الفيديو"
additions: "صور فورية, ألبوم إضافي"
```

## ملاحظات مهمة

1. **معالجة القيم الفارغة**: إذا لم تكن القيمة متوفرة، سيتم عرض نص بديل مثل "غير محدد" أو "لا توجد"

2. **التنسيق التلقائي**: المبالغ تُنسق تلقائياً بفاصلتين عشريتين ووحدة "ر.س"

3. **النصوص العربية**: جميع النصوص تعرض باللغة العربية مع دعم كامل لاتجاه الكتابة من اليمين لليسار

4. **أسماء الملفات**: تُنظف أسماء العملاء من الرموز الخاصة لضمان توافق أسماء الملفات

5. **تنظيم الملفات**: الصور تُحفظ في مجلد منظم مع أسماء واضحة تتضمن نوع الوصل واسم العميل والتاريخ

## استخدام المتغيرات في القوالب

عند إنشاء قالب جديد أو تحرير قالب موجود، يمكنك استخدام أي من هذه المتغيرات كمحتوى للمناطق النصية في القالب. النظام سيستبدل اسم المتغير بالقيمة الحقيقية عند إنشاء الصورة.

مثال:
- منطقة باسم `customer_name` ستعرض "أحمد محمد علي"
- منطقة باسم `total_amount` ستعرض "2500.00 ر.س"
- منطقة باسم `print_date` ستعرض التاريخ والوقت الحاليين

هذا النظام يوفر مرونة كاملة في تصميم الوصولات وضمان احتواء الصور على جميع المعلومات المطلوبة بتنسيق احترافي.
