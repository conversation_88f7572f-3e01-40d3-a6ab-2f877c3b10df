import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:intl/intl.dart';
import '../utils/app_colors.dart';
import '../models/booking.dart';
import '../models/customer.dart';
import '../models/service_offer.dart';
import '../services/database_helper.dart';
import '../services/html_receipt_enhanced_service.dart';
import '../services/html_to_pdf_standalone.dart';

class BookingReceiptScreen extends StatefulWidget {
  const BookingReceiptScreen({super.key});

  @override
  State<BookingReceiptScreen> createState() => _BookingReceiptScreenState();
}

class _BookingReceiptScreenState extends State<BookingReceiptScreen>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _customerNameController = TextEditingController();
  final _customerPhoneController = TextEditingController();
  final _customerSearchController = TextEditingController();
  final _serviceTypeController = TextEditingController();
  final _eventDescriptionController = TextEditingController();
  final _hallNameController = TextEditingController();
  final _totalAmountController = TextEditingController();
  final _depositController = TextEditingController();
  final _discountController = TextEditingController();
  final _bookedByController = TextEditingController();
  final _notesController = TextEditingController();
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  
  late AnimationController _animationController;
  DateTime _selectedDeliveryDate = DateTime.now().add(const Duration(days: 25));
  DateTime _selectedEventDate = DateTime.now();
  bool _isLoading = false;
  bool _isSearching = false;
  
  Customer? _selectedCustomer;
  List<Customer> _searchResults = [];
  List<ServiceOffer> _selectedOffers = [];
  List<Addition> _selectedAdditions = [];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _animationController.forward();
    
    // Set initial delivery date to 25 days after event date
    _selectedDeliveryDate = _selectedEventDate.add(const Duration(days: 25));
    
    // Add listeners for automatic calculation
    _totalAmountController.addListener(_updateRemainingAmount);
    _depositController.addListener(_updateRemainingAmount);
    _discountController.addListener(_updateRemainingAmount);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _totalAmountController.removeListener(_updateRemainingAmount);
    _depositController.removeListener(_updateRemainingAmount);
    _customerNameController.dispose();
    _customerPhoneController.dispose();
    _customerSearchController.dispose();
    _serviceTypeController.dispose();
    _eventDescriptionController.dispose();
    _hallNameController.dispose();
    _totalAmountController.dispose();
    _depositController.dispose();
    _discountController.dispose();
    _bookedByController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _searchCustomers(String query) async {
    if (query.isEmpty) {
      setState(() {
        _searchResults = [];
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    try {
      final results = await _databaseHelper.searchCustomers(query);
      setState(() {
        _searchResults = results;
        _isSearching = false;
      });
    } catch (e) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
    }
  }

  void _selectCustomer(Customer customer) {
    setState(() {
      _selectedCustomer = customer;
      _customerNameController.text = customer.name;
      _customerPhoneController.text = customer.phone;
      _searchResults = [];
    });
  }

  void _showNewCustomerForm() {
    showDialog(
      context: context,
      builder: (context) => QuickAddCustomerDialog(
        initialName: _customerSearchController.text,
        onCustomerAdded: (customer) {
          setState(() {
            _selectedCustomer = customer;
            _customerNameController.text = customer.name;
            _customerPhoneController.text = customer.phone;
            _searchResults = [];
          });
          _showCustomerAddedSnackBar(customer.name);
        },
      ),
    );
  }

  void _showCustomerAddedSnackBar(String customerName) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Text('تم إنشاء العميل "$customerName" بنجاح'),
            ),
          ],
        ),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _clearCustomerSearch() {
    setState(() {
      _customerSearchController.clear();
      _searchResults = [];
      _selectedCustomer = null;
      _customerNameController.clear();
      _customerPhoneController.clear();
    });
  }

  Future<void> _selectDate() async {
    // Show confirmation dialog first
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغيير تاريخ التسليم'),
        content: const Text(
          'تاريخ التسليم يتم حسابه تلقائياً (25 يوم بعد تاريخ المناسبة).\n'
          'هل تريد تخصيص تاريخ التسليم يدوياً؟'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: Colors.white,
            ),
            child: const Text('نعم، تخصيص التاريخ'),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDeliveryDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF3B82F6),
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _selectedDeliveryDate) {
      setState(() {
        _selectedDeliveryDate = picked;
      });
    }
  }

  Future<void> _saveBooking() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final booking = Booking(
        customerName: _customerNameController.text.trim(),
        customerPhone: _customerPhoneController.text.trim(),
        serviceType: _serviceTypeController.text.trim(),
        eventDescription: _eventDescriptionController.text.trim().isEmpty ? null : _eventDescriptionController.text.trim(),
        hallName: _hallNameController.text.trim().isEmpty ? null : _hallNameController.text.trim(),
        eventDate: _selectedEventDate,
        selectedOffers: _selectedOffers.map((offer) => '${offer.name} - ${offer.price.toStringAsFixed(0)} د.ع').toList(),
        additions: _selectedAdditions.map((addition) => '${addition.name} - ${addition.price.toStringAsFixed(0)} د.ع').toList(),
        totalAmount: double.parse(_totalAmountController.text),
        deposit: double.parse(_depositController.text),
        discount: _discountController.text.isEmpty ? 0.0 : double.parse(_discountController.text),
        bookedBy: _bookedByController.text.trim().isEmpty ? null : _bookedByController.text.trim(),
        bookingDate: DateTime.now(),
        deliveryDate: _selectedDeliveryDate,
        status: BookingStatus.inProgress,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      );

      // Save booking to database
      await _databaseHelper.insertBooking(booking);
      
      // Save customer if it's a new customer
      if (_selectedCustomer == null) {
        try {
          final customer = Customer(
            name: _customerNameController.text.trim(),
            phone: _customerPhoneController.text.trim(),
          );
          await _databaseHelper.insertCustomer(customer);
        } catch (e) {
          // Continue even if customer save fails
          debugPrint('Failed to save customer: $e');
        }
      }

      if (mounted) {
        _showSuccessDialog(booking);
      }
    } catch (e) {
      _showErrorDialog('حدث خطأ أثناء حفظ الحجز: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showSuccessDialog(Booking booking) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        icon: const Icon(
          Icons.check_circle,
          color: AppColors.success,
          size: 64,
        ),
        title: const Text('تم الحفظ بنجاح'),
        content: const Text('تم إنشاء وصل الحجز بنجاح\nهل تريد طباعة الوصل؟'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _resetForm();
            },
            child: const Text('حجز جديد'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                // Save receipt as HTML file
                final htmlPath = await HtmlReceiptService.generateHtmlReceipt(
                  booking: booking,
                  templateName: 'modern',
                  receiptType: 'booking',
                  customData: {
                    'companyName': 'استوديو الذكريات الجميلة',
                    'companyLogo': '📸',
                    'companyPhone': '0555123456',
                    'companyEmail': '<EMAIL>',
                  },
                );

                // Also save PDF to C:/Users/<USER>/Documents/pdfs using standalone function
                await convertHtmlToPdfAndSave();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Row(
                        children: [
                          const Icon(Icons.check_circle, color: Colors.white),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text('تم حفظ الوصل كملف HTML و PDF بنجاح'),
                          ),
                          TextButton(
                            onPressed: () => HtmlReceiptService.openHtmlFile(htmlPath),
                            child: const Text('عرض HTML', style: TextStyle(color: Colors.white)),
                          ),
                        ],
                      ),
                      backgroundColor: AppColors.success,
                      duration: const Duration(seconds: 5),
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في حفظ الوصل: $e'),
                      backgroundColor: AppColors.error,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: Colors.white,
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.save, size: 18),
                SizedBox(width: 4),
                Text('حفظ الوصل'),
              ],
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: const Text('العودة للقائمة الرئيسية'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: const Icon(
          Icons.error,
          color: AppColors.error,
          size: 64,
        ),
        title: const Text('خطأ'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _resetForm() {
    _formKey.currentState?.reset();
    _customerNameController.clear();
    _customerPhoneController.clear();
    _customerSearchController.clear();
    _serviceTypeController.clear();
    _eventDescriptionController.clear();
    _hallNameController.clear();
    _totalAmountController.clear();
    _depositController.clear();
    _discountController.clear();
    _bookedByController.clear();
    _notesController.clear();
    setState(() {
      _selectedEventDate = DateTime.now();
      _selectedDeliveryDate = DateTime.now().add(const Duration(days: 25));
      _selectedCustomer = null;
      _searchResults = [];
      _selectedOffers = [];
      _selectedAdditions = [];
      _isSearching = false;
    });
  }

  void _showOffersDialog() async {
    try {
      // Load offers from database
      final servicesData = await _databaseHelper.getAllServices();
      final offers = <ServiceOffer>[];
      
      // Filter only offers (marked as popular or category contains 'عرض')
      for (final serviceMap in servicesData) {
        final category = serviceMap['category'] ?? 'عام';
        final isPopular = (serviceMap['is_popular'] ?? 0) == 1;
        
        // Skip additions (categories starting with 'إضافة')
        if (category.startsWith('إضافة')) continue;
        
        final serviceOffer = ServiceOffer(
          id: serviceMap['id'] ?? '',
          name: serviceMap['name'] ?? '',
          description: serviceMap['description'] ?? '',
          price: (serviceMap['price'] ?? 0).toDouble(),
          duration: serviceMap['duration'] ?? '30 دقيقة',
          category: category,
          isPopular: isPopular,
          features: serviceMap['features']?.split(',') ?? [],
        );
        
        // Add to offers if it's marked as popular or category contains 'عرض' or ID starts with 'offer_'
        // Also include regular services for selection
        if (isPopular || 
            category.contains('عرض') || 
            serviceOffer.id.startsWith('offer_') ||
            !category.startsWith('إضافة')) {
          offers.add(serviceOffer);
        }
      }
      
      if (!mounted) return;
      
      if (offers.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا توجد عروض محفوظة. يرجى إضافة عروض من شاشة إدارة العروض أولاً'),
            backgroundColor: AppColors.warning,
          ),
        );
        return;
      }

      final selectedOffers = List<ServiceOffer>.from(_selectedOffers);
      
      showDialog(
        context: context,
        builder: (context) => StatefulBuilder(
          builder: (context, setStateDialog) => AlertDialog(
            title: const Text('اختيار العروض'),
            content: SizedBox(
              width: double.maxFinite,
              height: 400,
              child: Column(
                children: [
                  Text(
                    'اختر العروض المناسبة للعميل:',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: ListView.builder(
                      itemCount: offers.length,
                      itemBuilder: (context, index) {
                        final offer = offers[index];
                        final isSelected = selectedOffers.any((selected) => selected.id == offer.id);
                        
                        return Card(
                          margin: const EdgeInsets.only(bottom: 8),
                          child: CheckboxListTile(
                            value: isSelected,
                            onChanged: (value) {
                              setStateDialog(() {
                                if (value == true) {
                                  selectedOffers.add(offer);
                                } else {
                                  selectedOffers.removeWhere((selected) => selected.id == offer.id);
                                }
                              });
                            },
                            title: Text(
                              offer.name,
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (offer.description.isNotEmpty)
                                  Text(offer.description),
                                const SizedBox(height: 4),
                                Text(
                                  '${offer.price.toStringAsFixed(0)} د.ع',
                                  style: const TextStyle(
                                    color: AppColors.success,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            controlAffinity: ListTileControlAffinity.leading,
                          ),
                        );
                      },
                    ),
                  ),
                  const Divider(),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColors.veryLightBlue,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'إجمالي العروض المختارة:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          '${selectedOffers.fold(0.0, (sum, offer) => sum + offer.price).toStringAsFixed(0)} د.ع',
                          style: const TextStyle(
                            color: AppColors.success,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _selectedOffers = selectedOffers;
                  });
                  _calculateTotal();
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  foregroundColor: Colors.white,
                ),
                child: Text('تأكيد (${selectedOffers.length})'),
              ),
            ],
          ),
        ),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل العروض: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _showAdditionsDialog() async {
    try {
      // Load additions from database
      final servicesData = await _databaseHelper.getAllServices();
      final additions = <Addition>[];
      
      // Filter only additions (categories starting with 'إضافة')
      for (final serviceMap in servicesData) {
        final category = serviceMap['category'] ?? 'عام';
        
        // Only include additions
        if (category.startsWith('إضافة')) {
          final addition = Addition(
            id: serviceMap['id'] ?? '',
            name: serviceMap['name'] ?? '',
            description: serviceMap['description'] ?? '',
            price: (serviceMap['price'] ?? 0).toDouble(),
            category: category.replaceFirst('إضافة - ', ''),
          );
          additions.add(addition);
        }
      }
      
      if (!mounted) return;
      
      if (additions.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا توجد إضافات محفوظة'),
            backgroundColor: AppColors.warning,
          ),
        );
        return;
      }

      final selectedAdditions = List<Addition>.from(_selectedAdditions);
      
      showDialog(
        context: context,
        builder: (context) => StatefulBuilder(
          builder: (context, setStateDialog) => AlertDialog(
            title: const Text('اختيار الإضافات'),
            content: SizedBox(
              width: double.maxFinite,
              height: 400,
              child: Column(
                children: [
                  Text(
                    'اختر الإضافات المناسبة للعميل:',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: ListView.builder(
                      itemCount: additions.length,
                      itemBuilder: (context, index) {
                        final addition = additions[index];
                        final isSelected = selectedAdditions.any((selected) => selected.id == addition.id);
                        
                        return Card(
                          margin: const EdgeInsets.only(bottom: 8),
                          child: CheckboxListTile(
                            value: isSelected,
                            onChanged: (value) {
                              setStateDialog(() {
                                if (value == true) {
                                  selectedAdditions.add(addition);
                                } else {
                                  selectedAdditions.removeWhere((selected) => selected.id == addition.id);
                                }
                              });
                            },
                            title: Text(
                              addition.name,
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (addition.description.isNotEmpty)
                                  Text(addition.description),
                                const SizedBox(height: 4),
                                Text(
                                  '${addition.price.toStringAsFixed(0)} د.ع',
                                  style: const TextStyle(
                                    color: AppColors.primaryBlue,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            controlAffinity: ListTileControlAffinity.leading,
                          ),
                        );
                      },
                    ),
                  ),
                  const Divider(),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColors.veryLightBlue,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'إجمالي الإضافات المختارة:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          '${selectedAdditions.fold(0.0, (sum, addition) => sum + addition.price).toStringAsFixed(0)} د.ع',
                          style: const TextStyle(
                            color: AppColors.primaryBlue,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _selectedAdditions = selectedAdditions;
                  });
                  _calculateTotal();
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  foregroundColor: Colors.white,
                ),
                child: Text('تأكيد (${selectedAdditions.length})'),
              ),
            ],
          ),
        ),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الإضافات: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _calculateTotal() {
    double total = 0.0;
    
    // Add offers prices
    for (final offer in _selectedOffers) {
      total += offer.price;
    }
    
    // Add additions prices
    for (final addition in _selectedAdditions) {
      total += addition.price;
    }
    
    setState(() {
      _totalAmountController.text = total.toStringAsFixed(0);
    });
  }

  void _updateRemainingAmount() {
    setState(() {
      // This will trigger a rebuild and update the remaining amount display
    });
  }

  double _calculateRemainingAmount() {
    final total = double.tryParse(_totalAmountController.text) ?? 0.0;
    final discount = double.tryParse(_discountController.text) ?? 0.0;
    final deposit = double.tryParse(_depositController.text) ?? 0.0;
    
    final totalAfterDiscount = total - discount;
    final remaining = totalAfterDiscount - deposit;
    
    return remaining > 0 ? remaining : 0.0;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFB),
      appBar: AppBar(
        title: const Text(
          'وصل حجز جديد',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF1E293B),
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back_ios),
        ),
        actions: [
          IconButton(
            onPressed: _resetForm,
            icon: const Icon(Icons.refresh),
            tooltip: 'إعادة تعيين',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: AnimationLimiter(
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: AnimationConfiguration.toStaggeredList(
                duration: const Duration(milliseconds: 300),
                childAnimationBuilder: (widget) => SlideAnimation(
                  verticalOffset: 30.0,
                  child: FadeInAnimation(child: widget),
                ),
                children: [
                  // Header Section
                  _buildHeaderSection(),
                  
                  const SizedBox(height: 24),
                  
                  // Customer Section
                  _buildCustomerSection(),
                  
                  const SizedBox(height: 24),
                  
                  // Service Details Section
                  _buildServiceSection(),
                  
                  const SizedBox(height: 24),
                  
                  // Financial Section
                  _buildFinancialSection(),
                  
                  const SizedBox(height: 24),
                  
                  // Additional Info Section
                  _buildAdditionalInfoSection(),
                  
                  const SizedBox(height: 32),
                  
                  // Action Buttons
                  _buildActionButtons(),
                  
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Helper methods for building UI sections
  Widget _buildHeaderSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.receipt_long,
              color: Colors.white,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'إنشاء وصل حجز جديد',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: const Color(0xFF3B82F6).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.person_outline,
                  color: Color(0xFF3B82F6),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'بيانات العميل',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1E293B),
                ),
              ),
              const Spacer(),
              if (_selectedCustomer != null)
                TextButton.icon(
                  onPressed: _clearCustomerSearch,
                  icon: const Icon(Icons.refresh, size: 16),
                  label: const Text('تغيير'),
                  style: TextButton.styleFrom(
                    foregroundColor: const Color(0xFF6B7280),
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 20),
          
          if (_selectedCustomer == null) ...[
            // Customer search
            TextFormField(
              controller: _customerSearchController,
              decoration: InputDecoration(
                labelText: 'البحث عن عميل موجود',
                hintText: 'ابحث بالاسم أو رقم الهاتف...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _isSearching
                    ? const Padding(
                        padding: EdgeInsets.all(12),
                        child: SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF3B82F6), width: 2),
                ),
              ),
              onChanged: _searchCustomers,
            ),
            
            // Search results
            if (_searchResults.isNotEmpty) ...[
              const SizedBox(height: 12),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _searchResults.length,
                  separatorBuilder: (context, index) => Divider(height: 1, color: Colors.grey[200]),
                  itemBuilder: (context, index) {
                    final customer = _searchResults[index];
                    return ListTile(
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      leading: CircleAvatar(
                        backgroundColor: const Color(0xFF3B82F6).withOpacity(0.1),
                        child: Text(
                          customer.name.substring(0, 1),
                          style: const TextStyle(
                            color: Color(0xFF3B82F6),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      title: Text(
                        customer.name,
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                      subtitle: Text(customer.phone),
                      onTap: () => _selectCustomer(customer),
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    );
                  },
                ),
              ),
            ],
            
            // Create new customer button
            if (_customerSearchController.text.isNotEmpty && 
                _searchResults.isEmpty && 
                !_isSearching) ...[
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: _showNewCustomerForm,
                  icon: const Icon(Icons.person_add),
                  label: Text('إنشاء عميل جديد: "${_customerSearchController.text}"'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: const Color(0xFF3B82F6),
                    side: const BorderSide(color: Color(0xFF3B82F6)),
                    padding: const EdgeInsets.all(16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ] else ...[
            // Selected customer display
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFF10B981).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFF10B981).withOpacity(0.2)),
              ),
              child: Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: const BoxDecoration(
                      color: Color(0xFF10B981),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(Icons.check, color: Colors.white, size: 24),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'العميل المحدد',
                          style: TextStyle(
                            color: Color(0xFF065F46),
                            fontWeight: FontWeight.w500,
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          _selectedCustomer!.name,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: Color(0xFF065F46),
                          ),
                        ),
                        Text(
                          _selectedCustomer!.phone,
                          style: const TextStyle(
                            color: Color(0xFF047857),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            // Hidden validation fields
            Visibility(
              visible: false,
              child: Column(
                children: [
                  TextFormField(
                    controller: _customerNameController,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال اسم العميل';
                      }
                      return null;
                    },
                  ),
                  TextFormField(
                    controller: _customerPhoneController,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال رقم الهاتف';
                      }
                      return null;
                    },
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildServiceSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: const Color(0xFF8B5CF6).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.camera_alt_outlined,
                  color: Color(0xFF8B5CF6),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'تفاصيل الخدمة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1E293B),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // Service type
          TextFormField(
            controller: _serviceTypeController,
            decoration: InputDecoration(
              labelText: 'نوع الخدمة *',
              hintText: 'مثال: تصوير أعراس، تصوير أطفال، جلسة تصوير...',
              prefixIcon: const Icon(Icons.camera_alt),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFF8B5CF6), width: 2),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال نوع الخدمة';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 16),
          
          // Hall name
          TextFormField(
            controller: _hallNameController,
            decoration: InputDecoration(
              labelText: 'اسم القاعة',
              hintText: 'مثال: قاعة الأفراح الكبرى، قاعة الملكة...',
              prefixIcon: const Icon(Icons.location_city),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFF8B5CF6), width: 2),
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          Row(
            children: [
              // Event date
              Expanded(
                child: InkWell(
                  onTap: () async {
                    final DateTime? picked = await showDatePicker(
                      context: context,
                      initialDate: _selectedEventDate,
                      firstDate: DateTime.now().subtract(const Duration(days: 365)),
                      lastDate: DateTime.now().add(const Duration(days: 365)),
                      builder: (context, child) {
                        return Theme(
                          data: Theme.of(context).copyWith(
                            colorScheme: const ColorScheme.light(
                              primary: Color(0xFF8B5CF6),
                            ),
                          ),
                          child: child!,
                        );
                      },
                    );
                    if (picked != null && picked != _selectedEventDate) {
                      setState(() {
                        _selectedEventDate = picked;
                        // Automatically set delivery date to 25 days after event date
                        _selectedDeliveryDate = picked.add(const Duration(days: 25));
                      });
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.event, color: Colors.grey[600], size: 20),
                            const SizedBox(width: 8),
                            Text(
                              'تاريخ المناسبة',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          DateFormat('dd/MM/yyyy').format(_selectedEventDate),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF1E293B),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Delivery date
              Expanded(
                child: InkWell(
                  onTap: _selectDate,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.calendar_today, color: Colors.grey[600], size: 20),
                            const SizedBox(width: 8),
                            Text(
                              'تاريخ التسليم (تلقائي: +25 يوم)',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          DateFormat('dd/MM/yyyy').format(_selectedDeliveryDate),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF1E293B),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          const SizedBox(height: 16),
          
          // Service options buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _showOffersDialog,
                  icon: const Icon(Icons.local_offer),
                  label: Text(_selectedOffers.isEmpty 
                    ? 'اختيار العروض' 
                    : 'العروض (${_selectedOffers.length})'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: const Color(0xFF8B5CF6),
                    side: const BorderSide(color: Color(0xFF8B5CF6)),
                    padding: const EdgeInsets.all(16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _showAdditionsDialog,
                  icon: const Icon(Icons.add_circle_outline),
                  label: Text(_selectedAdditions.isEmpty 
                    ? 'اختيار الإضافات' 
                    : 'الإضافات (${_selectedAdditions.length})'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: const Color(0xFF8B5CF6),
                    side: const BorderSide(color: Color(0xFF8B5CF6)),
                    padding: const EdgeInsets.all(16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialSection() {
    final remainingAmount = _calculateRemainingAmount();
    
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: const Color(0xFF10B981).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.payments_outlined,
                  color: Color(0xFF10B981),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'التفاصيل المالية',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1E293B),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // Total amount
          TextFormField(
            controller: _totalAmountController,
            decoration: InputDecoration(
              labelText: 'المبلغ الكلي *',
              hintText: '100000',
              prefixIcon: const Icon(Icons.attach_money),
              suffixText: 'د.ع',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFF10B981), width: 2),
              ),
            ),
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال المبلغ الكلي';
              }
              if (double.tryParse(value) == null) {
                return 'يرجى إدخال رقم صحيح';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 16),
          
          Row(
            children: [
              // Deposit
              Expanded(
                child: TextFormField(
                  controller: _depositController,
                  decoration: InputDecoration(
                    labelText: 'العربون *',
                    hintText: '25000',
                    prefixIcon: const Icon(Icons.account_balance_wallet),
                    suffixText: 'د.ع',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFF10B981), width: 2),
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال العربون';
                    }
                    final deposit = double.tryParse(value);
                    final total = double.tryParse(_totalAmountController.text);
                    if (deposit == null) {
                      return 'يرجى إدخال رقم صحيح';
                    }
                    if (total != null && deposit > total) {
                      return 'العربون أكبر من الإجمالي';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              // Discount
              Expanded(
                child: TextFormField(
                  controller: _discountController,
                  decoration: InputDecoration(
                    labelText: 'الخصم',
                    hintText: '5000',
                    prefixIcon: const Icon(Icons.discount),
                    suffixText: 'د.ع',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFF10B981), width: 2),
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value != null && value.isNotEmpty) {
                      final discount = double.tryParse(value);
                      final total = double.tryParse(_totalAmountController.text);
                      if (discount == null) {
                        return 'يرجى إدخال رقم صحيح';
                      }
                      if (total != null && discount > total) {
                        return 'الخصم أكبر من الإجمالي';
                      }
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Remaining amount display
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF3B82F6).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFF3B82F6).withOpacity(0.2)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'المبلغ المتبقي:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF1E293B),
                  ),
                ),
                Text(
                  '${remainingAmount.toStringAsFixed(0)} د.ع',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF3B82F6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfoSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: const Color(0xFFF59E0B).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.info_outline,
                  color: Color(0xFFF59E0B),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'معلومات إضافية',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1E293B),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // Booked by
          TextFormField(
            controller: _bookedByController,
            decoration: InputDecoration(
              labelText: 'محجوز بواسطة',
              hintText: 'اسم الموظف المسؤول',
              prefixIcon: const Icon(Icons.person_pin),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Color(0xFFF59E0B), width: 2),
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
        
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _resetForm,
            style: OutlinedButton.styleFrom(
              foregroundColor: const Color(0xFF6B7280),
              side: const BorderSide(color: Color(0xFF6B7280)),
              padding: const EdgeInsets.all(16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'إعادة تعيين',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveBooking,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF3B82F6),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.all(16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.save, size: 20),
                      SizedBox(width: 8),
                      Text(
                        'حفظ الوصل',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ],
    );
  }
}

// Quick Add Customer Dialog
class QuickAddCustomerDialog extends StatefulWidget {
  final String initialName;
  final Function(Customer) onCustomerAdded;

  const QuickAddCustomerDialog({
    super.key,
    required this.initialName,
    required this.onCustomerAdded,
  });

  @override
  State<QuickAddCustomerDialog> createState() => _QuickAddCustomerDialogState();
}

class _QuickAddCustomerDialogState extends State<QuickAddCustomerDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _nameController.text = widget.initialName;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  Future<void> _saveCustomer() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final customer = Customer(
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim(),
        address: _addressController.text.trim().isEmpty ? null : _addressController.text.trim(),
      );

      await _databaseHelper.insertCustomer(customer);
      
      if (mounted) {
        widget.onCustomerAdded(customer);
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ العميل: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إضافة عميل جديد'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'اسم العميل *',
                prefixIcon: Icon(Icons.person),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال اسم العميل';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'رقم الهاتف *',
                prefixIcon: Icon(Icons.phone),
              ),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال رقم الهاتف';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveCustomer,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryBlue,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('حفظ'),
        ),
      ],
    );
  }
}
