class CashboxSession {
  final String? id;
  final String cashboxId;
  final DateTime startDate;
  final DateTime? endDate;
  final double startingBalance;
  final double? endingBalance;
  final double? actualBalance;
  final double? difference;
  final String? notes;
  final String openedBy;
  final String? closedBy;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  CashboxSession({
    this.id,
    required this.cashboxId,
    required this.startDate,
    this.endDate,
    required this.startingBalance,
    this.endingBalance,
    this.actualBalance,
    this.difference,
    this.notes,
    required this.openedBy,
    this.closedBy,
    this.isActive = true,
    DateTime? createdAt,
    this.updatedAt,
  }) : createdAt = createdAt ?? DateTime.now();

  CashboxSession copyWith({
    String? id,
    String? cashboxId,
    DateTime? startDate,
    DateTime? endDate,
    double? startingBalance,
    double? endingBalance,
    double? actualBalance,
    double? difference,
    String? notes,
    String? openedBy,
    String? closedBy,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CashboxSession(
      id: id ?? this.id,
      cashboxId: cashboxId ?? this.cashboxId,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      startingBalance: startingBalance ?? this.startingBalance,
      endingBalance: endingBalance ?? this.endingBalance,
      actualBalance: actualBalance ?? this.actualBalance,
      difference: difference ?? this.difference,
      notes: notes ?? this.notes,
      openedBy: openedBy ?? this.openedBy,
      closedBy: closedBy ?? this.closedBy,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'cashbox_id': cashboxId,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'starting_balance': startingBalance,
      'ending_balance': endingBalance,
      'actual_balance': actualBalance,
      'difference': difference,
      'notes': notes,
      'opened_by': openedBy,
      'closed_by': closedBy,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  factory CashboxSession.fromMap(Map<String, dynamic> map) {
    return CashboxSession(
      id: map['id'],
      cashboxId: map['cashbox_id'] ?? '',
      startDate: DateTime.parse(map['start_date']),
      endDate: map['end_date'] != null ? DateTime.parse(map['end_date']) : null,
      startingBalance: (map['starting_balance'] ?? 0.0).toDouble(),
      endingBalance: map['ending_balance'] != null ? (map['ending_balance']).toDouble() : null,
      actualBalance: map['actual_balance'] != null ? (map['actual_balance']).toDouble() : null,
      difference: map['difference'] != null ? (map['difference']).toDouble() : null,
      notes: map['notes'],
      openedBy: map['opened_by'] ?? '',
      closedBy: map['closed_by'],
      isActive: (map['is_active'] ?? 1) == 1,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }

  // الرصيد المحسوب خلال الجلسة
  double get calculatedBalance => startingBalance + (endingBalance ?? 0.0);

  // الفرق بين الرصيد المحسوب والرصيد الفعلي
  double get balanceDifference {
    if (actualBalance == null) return 0.0;
    return actualBalance! - calculatedBalance;
  }

  // مدة الجلسة
  Duration? get sessionDuration {
    if (endDate == null) return null;
    return endDate!.difference(startDate);
  }

  // حالة الجلسة
  String get statusText {
    if (isActive) return 'نشطة';
    if (difference == null || difference == 0) return 'مغلقة - متوازنة';
    if (difference! > 0) return 'مغلقة - زيادة في الصندوق';
    return 'مغلقة - نقص في الصندوق';
  }

  // لون حالة الجلسة
  String get statusColor {
    if (isActive) return 'blue';
    if (difference == null || difference == 0) return 'green';
    if (difference! > 0) return 'orange';
    return 'red';
  }
}
