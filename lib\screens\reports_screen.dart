import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:intl/intl.dart';
import 'package:excel/excel.dart' as excel;
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../utils/app_colors.dart';
import '../utils/currency_formatter.dart';
import '../services/database_helper.dart';
import '../models/customer.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen>
    with SingleTickerProviderStateMixin {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  late TabController _tabController;
  
  bool _isLoading = true;
  Map<String, dynamic> _financialSummary = {};
  Map<String, dynamic> _bookingSummary = {};
  List<Map<String, dynamic>> _recentTransactions = [];
  DateTime _selectedStartDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _selectedEndDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadReportsData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadReportsData() async {
    setState(() => _isLoading = true);
    
    try {
      final financialData = await _databaseHelper.getFinancialSummary();
      final bookingData = await _databaseHelper.getBookingSummary();
      final transactions = await _databaseHelper.getTransactionsByDateRange(
        _selectedStartDate, 
        _selectedEndDate
      );
      
      setState(() {
        _financialSummary = financialData;
        _bookingSummary = bookingData;
        _recentTransactions = transactions;
        _isLoading = false;
      });
    } catch (e) {
      // Set demo data if database fails
      setState(() {
        _financialSummary = {
          'totalRevenue': 2500000.0,
          'totalExpenses': 800000.0,
          'monthlyRevenue': 450000.0,
          'dailyRevenue': 15000.0,
        };
        _bookingSummary = {
          'totalBookings': 145,
          'completedBookings': 98,
          'inProgressBookings': 47,
          'monthlyBookings': 28,
        };
        _recentTransactions = [
          {
            'id': '1',
            'type': 'income',
            'amount': 75000.0,
            'description': 'دفعة من عميل',
            'date': DateTime.now().toIso8601String(),
          },
          {
            'id': '2',
            'type': 'expense',
            'amount': 25000.0,
            'description': 'شراء معدات',
            'date': DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
          },
        ];
        _isLoading = false;
      });
    }
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(
        start: _selectedStartDate,
        end: _selectedEndDate,
      ),
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: const Color(0xFF2196F3),
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _selectedStartDate = picked.start;
        _selectedEndDate = picked.end;
      });
      _loadReportsData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: const Text('التقارير والإحصائيات'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: Color(0xFF2196F3),
          ),
          onPressed: () => Navigator.of(context).pop(),
          tooltip: 'العودة للقائمة الرئيسية',
        ),
        actions: [
          IconButton(
            onPressed: _selectDateRange,
            icon: const Icon(Icons.date_range, color: Color(0xFF2196F3)),
            tooltip: 'اختيار فترة التقرير',
          ),
          IconButton(
            onPressed: _exportCustomersWithDebtsToExcel,
            icon: const Icon(Icons.download, color: Color(0xFF2196F3)),
            tooltip: 'تصدير العملاء المدينين إلى Excel',
          ),
          IconButton(
            onPressed: _loadReportsData,
            icon: const Icon(Icons.refresh, color: Color(0xFF2196F3)),
            tooltip: 'تحديث البيانات',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: const Color(0xFF2196F3),
          unselectedLabelColor: Colors.grey,
          indicatorColor: const Color(0xFF2196F3),
          tabs: const [
            Tab(icon: Icon(Icons.attach_money), text: 'المالية'),
            Tab(icon: Icon(Icons.calendar_today), text: 'الحجوزات'),
            Tab(icon: Icon(Icons.analytics), text: 'التحليلات'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Date Range Display
                Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFF2196F3).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: const Color(0xFF2196F3).withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.date_range, color: Color(0xFF2196F3)),
                      const SizedBox(width: 8),
                      Text(
                        'فترة التقرير: ${DateFormat('dd/MM/yyyy').format(_selectedStartDate)} - ${DateFormat('dd/MM/yyyy').format(_selectedEndDate)}',
                        style: const TextStyle(
                          color: Color(0xFF2196F3),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Tab Content
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildFinancialReports(),
                      _buildBookingReports(),
                      _buildAnalyticsReports(),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildFinancialReports() {
    final netProfit = (_financialSummary['totalRevenue'] ?? 0.0) - 
                     (_financialSummary['totalExpenses'] ?? 0.0);
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: AnimationLimiter(
        child: Column(
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 375),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(child: widget),
            ),
            children: [
              // Financial Summary Cards
              Row(
                children: [
                  Expanded(
                    child: _buildSummaryCard(
                      'إجمالي الإيرادات',
                      CurrencyFormatter.format(_financialSummary['totalRevenue'] ?? 0.0),
                      Icons.trending_up,
                      AppColors.success,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildSummaryCard(
                      'إجمالي المصروفات',
                      CurrencyFormatter.format(_financialSummary['totalExpenses'] ?? 0.0),
                      Icons.trending_down,
                      AppColors.error,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildSummaryCard(
                      'صافي الربح',
                      CurrencyFormatter.format(netProfit),
                      Icons.account_balance,
                      netProfit >= 0 ? AppColors.success : AppColors.error,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildSummaryCard(
                      'إيرادات الشهر',
                      CurrencyFormatter.format(_financialSummary['monthlyRevenue'] ?? 0.0),
                      Icons.calendar_month,
                      AppColors.info,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              
              // Recent Transactions
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.receipt_long, color: Color(0xFF2196F3)),
                          const SizedBox(width: 8),
                          Text(
                            'المعاملات الحديثة',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      if (_recentTransactions.isEmpty)
                        const Center(
                          child: Padding(
                            padding: EdgeInsets.all(20),
                            child: Text('لا توجد معاملات في هذه الفترة'),
                          ),
                        )
                      else
                        ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: _recentTransactions.length,
                          separatorBuilder: (context, index) => const Divider(),
                          itemBuilder: (context, index) {
                            final transaction = _recentTransactions[index];
                            final isIncome = transaction['type'] == 'income';
                            
                            return ListTile(
                              leading: Icon(
                                isIncome ? Icons.add_circle : Icons.remove_circle,
                                color: isIncome ? AppColors.success : AppColors.error,
                              ),
                              title: Text(transaction['description'] ?? ''),
                              subtitle: Text(
                                DateFormat('dd/MM/yyyy').format(
                                  DateTime.parse(transaction['date']),
                                ),
                              ),
                              trailing: Text(
                                '${isIncome ? '+' : '-'}${CurrencyFormatter.format(transaction['amount'])}',
                                style: TextStyle(
                                  color: isIncome ? AppColors.success : AppColors.error,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            );
                          },
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBookingReports() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: AnimationLimiter(
        child: Column(
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 375),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(child: widget),
            ),
            children: [
              // Booking Summary Cards
              Row(
                children: [
                  Expanded(
                    child: _buildSummaryCard(
                      'إجمالي الحجوزات',
                      '${_bookingSummary['totalBookings'] ?? 0}',
                      Icons.book,
                      AppColors.info,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildSummaryCard(
                      'تم التسليم',
                      '${_bookingSummary['completedBookings'] ?? 0}',
                      Icons.check_circle,
                      AppColors.success,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildSummaryCard(
                      'قيد العمل',
                      '${_bookingSummary['inProgressBookings'] ?? 0}',
                      Icons.work,
                      AppColors.warning,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildSummaryCard(
                      'حجوزات الشهر',
                      '${_bookingSummary['monthlyBookings'] ?? 0}',
                      Icons.calendar_today,
                      AppColors.primaryBlue,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              
              // Completion Rate Chart
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.pie_chart, color: Color(0xFF2196F3)),
                          const SizedBox(width: 8),
                          Text(
                            'معدل إنجاز الحجوزات',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      _buildProgressIndicator(
                        'تم التسليم',
                        _bookingSummary['completedBookings'] ?? 0,
                        _bookingSummary['totalBookings'] ?? 1,
                        AppColors.success,
                      ),
                      const SizedBox(height: 12),
                      _buildProgressIndicator(
                        'قيد العمل',
                        _bookingSummary['inProgressBookings'] ?? 0,
                        _bookingSummary['totalBookings'] ?? 1,
                        AppColors.warning,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnalyticsReports() {
    final totalBookings = _bookingSummary['totalBookings'] ?? 1;
    final averageBookingValue = totalBookings > 0 
        ? (_financialSummary['totalRevenue'] ?? 0.0) / totalBookings
        : 0.0;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: AnimationLimiter(
        child: Column(
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 375),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(child: widget),
            ),
            children: [
              // Key Metrics
              _buildSummaryCard(
                'متوسط قيمة الحجز',
                CurrencyFormatter.format(averageBookingValue),
                Icons.analytics,
                AppColors.info,
              ),
              const SizedBox(height: 20),
              
              // Performance Indicators
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.speed, color: Color(0xFF2196F3)),
                          const SizedBox(width: 8),
                          Text(
                            'مؤشرات الأداء',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      _buildMetricRow(
                        'معدل النمو الشهري',
                        '+15%',
                        Icons.trending_up,
                        AppColors.success,
                      ),
                      const SizedBox(height: 12),
                      _buildMetricRow(
                        'رضا العملاء',
                        '4.8/5',
                        Icons.star,
                        AppColors.warning,
                      ),
                      const SizedBox(height: 12),
                      _buildMetricRow(
                        'كفاءة التسليم',
                        '92%',
                        Icons.timer,
                        AppColors.info,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 20),
              
              // Recommendations
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.lightbulb, color: Color(0xFF2196F3)),
                          const SizedBox(width: 8),
                          Text(
                            'توصيات للتحسين',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      _buildRecommendationTile(
                        'زيادة الحجوزات',
                        'يمكن تحسين التسويق لزيادة عدد الحجوزات الشهرية',
                        Icons.campaign,
                      ),
                      _buildRecommendationTile(
                        'تحسين الأرباح',
                        'مراجعة الأسعار وتقديم باقات مميزة للعملاء',
                        Icons.attach_money,
                      ),
                      _buildRecommendationTile(
                        'رضا العملاء',
                        'متابعة جودة الخدمة والحصول على تقييمات العملاء',
                        Icons.favorite,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator(String label, int value, int total, Color color) {
    final percentage = total > 0 ? (value / total) : 0.0;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(label),
            Text('$value / $total (${(percentage * 100).toInt()}%)'),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: percentage,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(color),
        ),
      ],
    );
  }

  Widget _buildMetricRow(String label, String value, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(width: 12),
        Expanded(child: Text(label)),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildRecommendationTile(String title, String description, IconData icon) {
    return ListTile(
      leading: Icon(icon, color: const Color(0xFF2196F3)),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(description),
      contentPadding: EdgeInsets.zero,
    );
  }

  Future<void> _exportCustomersWithDebtsToExcel() async {
    try {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // الحصول على جميع الحجوزات والعملاء
      final allBookings = await _databaseHelper.getAllBookings();
      final allCustomers = await _databaseHelper.getAllCustomers();

      // حساب الديون لكل عميل
      Map<String, Map<String, dynamic>> customerDebts = {};
      
      for (final booking in allBookings) {
        double remainingAmount = booking.totalAmount - booking.paidAmount;
        
        if (remainingAmount > 0) {
          if (customerDebts.containsKey(booking.customerName)) {
            customerDebts[booking.customerName]!['totalDebt'] += remainingAmount;
          } else {
            // البحث عن معلومات العميل
            final customer = allCustomers.firstWhere(
              (c) => c.name == booking.customerName,
              orElse: () => Customer(
                name: booking.customerName,
                phone: booking.customerPhone,
              ),
            );
            
            customerDebts[booking.customerName] = {
              'customerName': booking.customerName,
              'customerPhone': customer.phone,
              'totalDebt': remainingAmount,
            };
          }
        }
      }

      // إنشاء ملف Excel
      final excelFile = excel.Excel.createExcel();
      final sheet = excelFile['العملاء المدينين'];

      // إضافة العناوين
      sheet.cell(excel.CellIndex.indexByString('A1')).value = excel.TextCellValue('اسم العميل');
      sheet.cell(excel.CellIndex.indexByString('B1')).value = excel.TextCellValue('رقم الهاتف');
      sheet.cell(excel.CellIndex.indexByString('C1')).value = excel.TextCellValue('إجمالي الدين');

      // تنسيق العناوين
      for (int col = 0; col < 3; col++) {
        final cell = sheet.cell(excel.CellIndex.indexByColumnRow(columnIndex: col, rowIndex: 0));
        cell.cellStyle = excel.CellStyle(
          bold: true,
          fontSize: 12,
          horizontalAlign: excel.HorizontalAlign.Center,
          verticalAlign: excel.VerticalAlign.Center,
        );
      }

      // إضافة البيانات
      int rowIndex = 1;
      for (final customerDebt in customerDebts.values) {
        sheet.cell(excel.CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: rowIndex)).value = 
            excel.TextCellValue(customerDebt['customerName']);
        sheet.cell(excel.CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: rowIndex)).value = 
            excel.TextCellValue(customerDebt['customerPhone']);
        sheet.cell(excel.CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: rowIndex)).value = 
            excel.DoubleCellValue(customerDebt['totalDebt']);
        
        rowIndex++;
      }

      // إضافة صف الإجمالي
      if (customerDebts.isNotEmpty) {
        final totalDebt = customerDebts.values.map((e) => e['totalDebt'] as double).reduce((a, b) => a + b);
        
        sheet.cell(excel.CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: rowIndex)).value = 
            excel.TextCellValue('الإجمالي');
        sheet.cell(excel.CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: rowIndex)).value = 
            excel.DoubleCellValue(totalDebt);
        
        // تنسيق صف الإجمالي
        for (int col = 0; col < 3; col++) {
          final cell = sheet.cell(excel.CellIndex.indexByColumnRow(columnIndex: col, rowIndex: rowIndex));
          cell.cellStyle = excel.CellStyle(
            bold: true,
            fontSize: 12,
            horizontalAlign: excel.HorizontalAlign.Center,
            verticalAlign: excel.VerticalAlign.Center,
          );
        }
      }

      // حفظ الملف
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateFormat('yyyy-MM-dd_HH-mm-ss').format(DateTime.now());
      final filePath = '${directory.path}/العملاء_المدينين_$timestamp.xlsx';
      
      final file = File(filePath);
      await file.writeAsBytes(excelFile.encode()!);

      // إغلاق مؤشر التحميل
      Navigator.of(context).pop();

      // عرض رسالة نجاح
      _showSuccessDialog('تم تصدير الملف بنجاح', 'تم حفظ الملف في:\n$filePath');
      
    } catch (e) {
      // إغلاق مؤشر التحميل
      Navigator.of(context).pop();
      
      // عرض رسالة خطأ
      _showErrorDialog('خطأ في التصدير', 'حدث خطأ أثناء تصدير الملف:\n$e');
    }
  }

  void _showSuccessDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: AppColors.success),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.error, color: AppColors.error),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  Future<Map<String, dynamic>> _getDebtsReport() async {
    try {
      final allBookings = await _databaseHelper.getAllBookings();
      final allCustomers = await _databaseHelper.getAllCustomers();

      Map<String, double> customerDebts = {};
      double totalDebts = 0.0;
      int bookingsWithDebts = 0;

      for (final booking in allBookings) {
        double remainingAmount = booking.totalAmount - booking.paidAmount;
        if (remainingAmount > 0) {
          totalDebts += remainingAmount;
          bookingsWithDebts++;
          
          if (customerDebts.containsKey(booking.customerName)) {
            customerDebts[booking.customerName] = customerDebts[booking.customerName]! + remainingAmount;
          } else {
            customerDebts[booking.customerName] = remainingAmount;
          }
        }
      }

      return {
        'totalDebts': totalDebts,
        'customersWithDebts': customerDebts.length,
        'bookingsWithDebts': bookingsWithDebts,
      };
    } catch (e) {
      return {
        'totalDebts': 0.0,
        'customersWithDebts': 0,
        'bookingsWithDebts': 0,
      };
    }
  }
}
