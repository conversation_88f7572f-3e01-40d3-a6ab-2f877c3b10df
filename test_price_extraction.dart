void main() {
  // اختبار منطق استخراج السعر
  testPriceExtraction();
}

void testPriceExtraction() {
  final testItems = [
    'تصوير زفاف - 250000 د.ع',
    'جلسة تصوير شخصي - 150000 د.ع',
    'طباعة 20 صورة - 50000 د.ع',
    'ألبوم فاخر - وصف خاص - 75000 د.ع',
    'خدمة بدون سعر',
  ];

  for (final item in testItems) {
    print('\n--- معالجة العنصر: "$item" ---');
    
    String serviceName = item;
    String description = '';
    String price = '-';
    
    // نمط محسن لاستخراج السعر - التنسيق الجديد: "اسم الخدمة - رقم د.ع"
    final newFormatPattern = RegExp(r'^(.+?)\s*-\s*(\d+(?:\.\d+)?)\s*(د\.ع)\s*$');
    final newFormatMatch = newFormatPattern.firstMatch(item);
    
    if (newFormatMatch != null) {
      // التنسيق الجديد: "اسم الخدمة - 250000 د.ع"
      serviceName = newFormatMatch.group(1)?.trim() ?? item;
      final priceValue = newFormatMatch.group(2)?.trim() ?? '0';
      final currency = newFormatMatch.group(3)?.trim() ?? 'د.ع';
      price = '$priceValue $currency';
      
      print('✅ تم العثور على السعر بالتنسيق الجديد');
      print('اسم الخدمة: "$serviceName"');
      print('السعر: "$price"');
    } else {
      // محاولة التنسيق القديم أو أنماط أخرى
      final parts = item.split(' - ');
      
      if (parts.length >= 2) {
        serviceName = parts[0].trim();
        
        // فحص الجزء الأخير للسعر
        final lastPart = parts.last.trim();
        final pricePattern = RegExp(r'^(\d+(?:\.\d+)?)\s*(د\.ع|ريال|ر\.س|دينار)?\s*$');
        final priceMatch = pricePattern.firstMatch(lastPart);
        
        if (priceMatch != null) {
          // وجد سعر في الجزء الأخير
          final priceValue = priceMatch.group(1)?.trim() ?? '0';
          final currency = priceMatch.group(2)?.trim() ?? 'د.ع';
          price = '$priceValue $currency';
          
          // الوصف هو كل شيء بين الاسم والسعر
          if (parts.length > 2) {
            description = parts.sublist(1, parts.length - 1).join(' - ').trim();
          }
          
          print('✅ تم العثور على السعر في الجزء الأخير');
          print('اسم الخدمة: "$serviceName"');
          print('الوصف: "$description"');
          print('السعر: "$price"');
        } else {
          // لا يوجد سعر في الجزء الأخير
          description = parts.sublist(1).join(' - ').trim();
          print('❌ لم يتم العثور على سعر');
          print('اسم الخدمة: "$serviceName"');
          print('الوصف: "$description"');
        }
      } else {
        // جزء واحد فقط - اعتبره اسم الخدمة فقط
        serviceName = item.trim();
        print('ℹ️ جزء واحد فقط - لا يوجد سعر');
        print('اسم الخدمة: "$serviceName"');
      }
    }
  }
}
