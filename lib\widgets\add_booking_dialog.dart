import 'package:flutter/material.dart';
import '../utils/app_colors.dart';
import '../models/customer.dart';
import '../models/booking.dart';
import '../services/database_helper.dart';
import 'package:intl/intl.dart';

class AddBookingDialog extends StatefulWidget {
  final VoidCallback onBookingAdded;
  final Booking? bookingToEdit;

  const AddBookingDialog({
    super.key,
    required this.onBookingAdded,
    this.bookingToEdit,
  });

  @override
  State<AddBookingDialog> createState() => _AddBookingDialogState();
}

class _AddBookingDialogState extends State<AddBookingDialog> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final _serviceController = TextEditingController();
  final _totalAmountController = TextEditingController();
  final _paidAmountController = TextEditingController();
  final _discountController = TextEditingController();
  final _notesController = TextEditingController();
  final _customerSearchController = TextEditingController();

  Customer? _selectedCustomer;
  List<Customer> _searchResults = [];
  DateTime _selectedBookingDate = DateTime.now();
  DateTime _selectedDeliveryDate = DateTime.now().add(const Duration(days: 7));
  bool _isSearching = false;
  bool _isCreatingBooking = false;

  @override
  void initState() {
    super.initState();
    if (widget.bookingToEdit != null) {
      _loadBookingData();
    }
  }

  Future<void> _loadBookingData() async {
    final booking = widget.bookingToEdit!;
    
    // البحث عن العميل
    try {
      final customers = await _databaseHelper.getAllCustomers();
      final customer = customers.firstWhere(
        (c) => c.name == booking.customerName && c.phone == booking.customerPhone,
        orElse: () => Customer(
          id: 'temp',
          name: booking.customerName,
          phone: booking.customerPhone,
          email: '',
          address: '',
          notes: '',
          createdAt: DateTime.now(),
        ),
      );
      
      setState(() {
        _selectedCustomer = customer;
        _customerSearchController.text = customer.name;
        _serviceController.text = booking.serviceType;
        _totalAmountController.text = booking.totalAmount.toString();
        _paidAmountController.text = booking.paidAmount.toString();
        _discountController.text = booking.discount.toString();
        _notesController.text = booking.notes ?? '';
        _selectedBookingDate = booking.bookingDate;
        _selectedDeliveryDate = booking.deliveryDate;
      });
    } catch (e) {
      print('Error loading booking data: $e');
    }
  }

  @override
  void dispose() {
    _serviceController.dispose();
    _totalAmountController.dispose();
    _paidAmountController.dispose();
    _discountController.dispose();
    _notesController.dispose();
    _customerSearchController.dispose();
    super.dispose();
  }

  Future<void> _searchCustomers(String query) async {
    if (query.isEmpty) {
      setState(() {
        _searchResults = [];
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    try {
      final customers = await _databaseHelper.getAllCustomers();
      setState(() {
        _searchResults = customers
            .where((customer) =>
                customer.name.toLowerCase().contains(query.toLowerCase()) ||
                customer.phone.contains(query))
            .toList();
      });
    } catch (e) {
      setState(() {
        _searchResults = [];
      });
    } finally {
      setState(() {
        _isSearching = false;
      });
    }
  }

  Future<void> _showAddCustomerDialog() async {
    final result = await showDialog<Customer>(
      context: context,
      builder: (context) => QuickAddCustomerDialog(
        initialName: _customerSearchController.text,
      ),
    );

    if (result != null) {
      setState(() {
        _selectedCustomer = result;
        _customerSearchController.text = result.name;
        _searchResults = [];
      });
    }
  }

  Future<void> _createBooking() async {
    if (_selectedCustomer == null ||
        _serviceController.text.isEmpty ||
        _totalAmountController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى ملء جميع الحقول المطلوبة'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    setState(() {
      _isCreatingBooking = true;
    });

    try {
      final totalAmount = double.tryParse(_totalAmountController.text) ?? 0;
      final paidAmount = double.tryParse(_paidAmountController.text) ?? 0;
      final discount = double.tryParse(_discountController.text) ?? 0;

      if (widget.bookingToEdit != null) {
        // تحديث الحجز الموجود
        final updatedBooking = widget.bookingToEdit!.copyWith(
          customerName: _selectedCustomer!.name,
          customerPhone: _selectedCustomer!.phone,
          serviceType: _serviceController.text,
          totalAmount: totalAmount,
          discount: discount,
          deposit: paidAmount,
          paidAmount: paidAmount,
          bookingDate: _selectedBookingDate,
          deliveryDate: _selectedDeliveryDate,
          notes: _notesController.text.isEmpty ? null : _notesController.text,
          updatedAt: DateTime.now(),
        );

        await _databaseHelper.updateBooking(updatedBooking);
        
        widget.onBookingAdded();
        Navigator.of(context).pop();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث الحجز بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      } else {
        // إنشاء حجز جديد
        final newBooking = Booking(
          customerName: _selectedCustomer!.name,
          customerPhone: _selectedCustomer!.phone,
          serviceType: _serviceController.text,
          totalAmount: totalAmount,
          discount: discount,
          deposit: paidAmount, // استخدام المبلغ المدفوع كعربون مؤقتاً
          paidAmount: paidAmount,
          bookingDate: _selectedBookingDate,
          deliveryDate: _selectedDeliveryDate,
          status: BookingStatus.inProgress,
          notes: _notesController.text.isEmpty ? null : _notesController.text,
        );

        await _databaseHelper.insertBooking(newBooking);
        
        widget.onBookingAdded();
        Navigator.of(context).pop();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء الحجز بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      final action = widget.bookingToEdit != null ? 'تحديث' : 'إنشاء';
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء $action الحجز: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    } finally {
      setState(() {
        _isCreatingBooking = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 600,
        constraints: const BoxConstraints(maxHeight: 700),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: AppColors.primaryBlue,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.add_business, color: Colors.white),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      widget.bookingToEdit != null ? 'تعديل الحجز' : 'إنشاء حجز جديد',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Customer search section
                    const Text(
                      'العميل *',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryText,
                      ),
                    ),
                    const SizedBox(height: 8),
                    
                    // Selected customer or search field
                    if (_selectedCustomer != null) ...[
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppColors.veryLightBlue,
                          border: Border.all(color: AppColors.primaryBlue),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.person, color: AppColors.primaryBlue),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    _selectedCustomer!.name,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Text(
                                    _selectedCustomer!.phone,
                                    style: const TextStyle(
                                      color: AppColors.secondaryText,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            IconButton(
                              onPressed: () {
                                setState(() {
                                  _selectedCustomer = null;
                                  _customerSearchController.clear();
                                });
                              },
                              icon: const Icon(Icons.clear),
                            ),
                          ],
                        ),
                      ),
                    ] else ...[
                      TextField(
                        controller: _customerSearchController,
                        decoration: const InputDecoration(
                          hintText: 'ابحث عن العميل بالاسم أو رقم الهاتف',
                          prefixIcon: Icon(Icons.search),
                          border: OutlineInputBorder(),
                        ),
                        onChanged: _searchCustomers,
                      ),
                      
                      // Search results
                      if (_isSearching) ...[
                        const Padding(
                          padding: EdgeInsets.all(16),
                          child: Center(child: CircularProgressIndicator()),
                        ),
                      ] else if (_searchResults.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Container(
                          constraints: const BoxConstraints(maxHeight: 200),
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.mediumGray),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ListView.builder(
                            shrinkWrap: true,
                            itemCount: _searchResults.length,
                            itemBuilder: (context, index) {
                              final customer = _searchResults[index];
                              return ListTile(
                                leading: const Icon(Icons.person),
                                title: Text(customer.name),
                                subtitle: Text(customer.phone),
                                onTap: () {
                                  setState(() {
                                    _selectedCustomer = customer;
                                    _customerSearchController.text = customer.name;
                                    _searchResults = [];
                                  });
                                },
                              );
                            },
                          ),
                        ),
                      ] else if (_customerSearchController.text.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.lightGray,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            children: [
                              const Text(
                                'لم يتم العثور على العميل',
                                style: TextStyle(color: AppColors.secondaryText),
                              ),
                              const SizedBox(height: 8),
                              ElevatedButton.icon(
                                onPressed: _showAddCustomerDialog,
                                icon: const Icon(Icons.person_add),
                                label: const Text('إنشاء عميل جديد'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.success,
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],

                    const SizedBox(height: 20),

                    // Service type
                    const Text(
                      'نوع الخدمة *',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryText,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _serviceController,
                      decoration: const InputDecoration(
                        hintText: 'مثل: تصوير زفاف، تصوير تخرج، تصوير عائلي',
                        border: OutlineInputBorder(),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Dates
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'تاريخ الحجز',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primaryText,
                                ),
                              ),
                              const SizedBox(height: 8),
                              InkWell(
                                onTap: () async {
                                  final date = await showDatePicker(
                                    context: context,
                                    initialDate: _selectedBookingDate,
                                    firstDate: DateTime.now().subtract(const Duration(days: 30)),
                                    lastDate: DateTime.now().add(const Duration(days: 365)),
                                  );
                                  if (date != null) {
                                    setState(() {
                                      _selectedBookingDate = date;
                                    });
                                  }
                                },
                                child: Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: AppColors.mediumGray),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Row(
                                    children: [
                                      const Icon(Icons.calendar_today),
                                      const SizedBox(width: 8),
                                      Text(DateFormat('yyyy/MM/dd').format(_selectedBookingDate)),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'تاريخ التسليم',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primaryText,
                                ),
                              ),
                              const SizedBox(height: 8),
                              InkWell(
                                onTap: () async {
                                  final date = await showDatePicker(
                                    context: context,
                                    initialDate: _selectedDeliveryDate,
                                    firstDate: _selectedBookingDate,
                                    lastDate: DateTime.now().add(const Duration(days: 365)),
                                  );
                                  if (date != null) {
                                    setState(() {
                                      _selectedDeliveryDate = date;
                                    });
                                  }
                                },
                                child: Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: AppColors.mediumGray),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Row(
                                    children: [
                                      const Icon(Icons.calendar_today),
                                      const SizedBox(width: 8),
                                      Text(DateFormat('yyyy/MM/dd').format(_selectedDeliveryDate)),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // Amount fields
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'المبلغ الإجمالي *',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primaryText,
                                ),
                              ),
                              const SizedBox(height: 8),
                              TextField(
                                controller: _totalAmountController,
                                decoration: const InputDecoration(
                                  hintText: '0',
                                  border: OutlineInputBorder(),
                                  suffixText: 'د.ع',
                                ),
                                keyboardType: TextInputType.number,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'الخصم',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primaryText,
                                ),
                              ),
                              const SizedBox(height: 8),
                              TextField(
                                controller: _discountController,
                                decoration: const InputDecoration(
                                  hintText: '0',
                                  border: OutlineInputBorder(),
                                  suffixText: 'د.ع',
                                ),
                                keyboardType: TextInputType.number,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // Paid amount field
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'المبلغ المدفوع',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primaryText,
                                ),
                              ),
                              const SizedBox(height: 8),
                              TextField(
                                controller: _paidAmountController,
                                decoration: const InputDecoration(
                                  hintText: '0',
                                  border: OutlineInputBorder(),
                                  suffixText: 'د.ع',
                                ),
                                keyboardType: TextInputType.number,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(child: Container()), // مساحة فارغة للتوازن
                      ],
                    ),

                    const SizedBox(height: 20),

                    // Notes
                    const Text(
                      'ملاحظات',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryText,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _notesController,
                      decoration: const InputDecoration(
                        hintText: 'ملاحظات إضافية (اختياري)',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
            ),

            // Footer buttons
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: AppColors.lightGray,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(4),
                  bottomRight: Radius.circular(4),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: _isCreatingBooking ? null : _createBooking,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryBlue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: _isCreatingBooking
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(widget.bookingToEdit != null ? 'حفظ التغييرات' : 'إنشاء الحجز'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class QuickAddCustomerDialog extends StatefulWidget {
  final String? initialName;

  const QuickAddCustomerDialog({
    super.key,
    this.initialName,
  });

  @override
  State<QuickAddCustomerDialog> createState() => _QuickAddCustomerDialogState();
}

class _QuickAddCustomerDialogState extends State<QuickAddCustomerDialog> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  bool _isCreating = false;

  @override
  void initState() {
    super.initState();
    if (widget.initialName != null) {
      _nameController.text = widget.initialName!;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _createCustomer() async {
    if (_nameController.text.isEmpty || _phoneController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى ملء الاسم ورقم الهاتف'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    setState(() {
      _isCreating = true;
    });

    try {
      final newCustomer = Customer(
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim(),
      );

      await _databaseHelper.insertCustomer(newCustomer);
      Navigator.of(context).pop(newCustomer);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إنشاء العميل بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء إنشاء العميل: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    } finally {
      setState(() {
        _isCreating = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إنشاء عميل جديد'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'اسم العميل *',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _phoneController,
            decoration: const InputDecoration(
              labelText: 'رقم الهاتف *',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.phone,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isCreating ? null : _createCustomer,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.success,
            foregroundColor: Colors.white,
          ),
          child: _isCreating
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('إنشاء'),
        ),
      ],
    );
  }
}
