import 'package:shared_preferences/shared_preferences.dart';

class CompanySettingsService {
  static const String _companyNameKey = 'company_name';
  static const String _companyPhoneKey = 'company_phone';
  static const String _companyEmailKey = 'company_email';
  static const String _companyAddressKey = 'company_address';
  static const String _companyLogoEmojiKey = 'company_logo_emoji';
  static const String _companyLogoPathKey = 'company_logo_path';

  /// Get all company settings as a Map for use in receipt generation
  static Future<Map<String, dynamic>> getCompanySettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settings = {
        'companyName': prefs.getString(_companyNameKey) ?? 'استوديو الذكريات الجميلة',
        'companyPhone': prefs.getString(_companyPhoneKey) ?? '0555123456',
        'companyEmail': prefs.getString(_companyEmailKey) ?? '<EMAIL>',
        'companyAddress': prefs.getString(_companyAddressKey) ?? 'شارع الرئيسي، المدينة، العراق',
        'companyLogo': prefs.getString(_companyLogoEmojiKey) ?? '📸',
        'companyLogoPath': prefs.getString(_companyLogoPathKey),
      };
      
      return settings;
    } catch (e) {
      print('Error loading company settings: $e');
      return {
        'companyName': 'استوديو الذكريات الجميلة',
        'companyPhone': '0555123456',
        'companyEmail': '<EMAIL>',
        'companyAddress': 'شارع الرئيسي، المدينة، العراق',
        'companyLogo': '📸',
        'companyLogoPath': null,
      };
    }
  }

  /// Save company settings
  static Future<bool> saveCompanySettings({
    required String companyName,
    required String companyPhone,
    required String companyEmail,
    String? companyAddress,
    String? companyLogoEmoji,
    String? companyLogoPath,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_companyNameKey, companyName);
      await prefs.setString(_companyPhoneKey, companyPhone);
      await prefs.setString(_companyEmailKey, companyEmail);
      
      if (companyAddress != null) {
        await prefs.setString(_companyAddressKey, companyAddress);
      }
      
      if (companyLogoEmoji != null) {
        await prefs.setString(_companyLogoEmojiKey, companyLogoEmoji);
      }
      
      if (companyLogoPath != null) {
        await prefs.setString(_companyLogoPathKey, companyLogoPath);
      } else {
        await prefs.remove(_companyLogoPathKey);
      }
      
      return true;
    } catch (e) {
      print('Error saving company settings: $e');
      return false;
    }
  }

  /// Get company name
  static Future<String> getCompanyName() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_companyNameKey) ?? 'استوديو الذكريات الجميلة';
    } catch (e) {
      return 'استوديو الذكريات الجميلة';
    }
  }

  /// Get company logo (emoji or path)
  static Future<Map<String, String?>> getCompanyLogo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return {
        'emoji': prefs.getString(_companyLogoEmojiKey) ?? '📸',
        'path': prefs.getString(_companyLogoPathKey),
      };
    } catch (e) {
      return {
        'emoji': '📸',
        'path': null,
      };
    }
  }

  /// Clear company logo path (keep emoji)
  static Future<bool> clearCompanyLogoPath() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_companyLogoPathKey);
      return true;
    } catch (e) {
      print('Error clearing company logo path: $e');
      return false;
    }
  }

  /// Check if company settings are configured
  static Future<bool> isCompanyConfigured() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final companyName = prefs.getString(_companyNameKey);
      final companyPhone = prefs.getString(_companyPhoneKey);
      
      return companyName != null && 
             companyName.isNotEmpty && 
             companyPhone != null && 
             companyPhone.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
}
