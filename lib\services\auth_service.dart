import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import 'database_helper.dart';

class AuthService {
  static const String _isLoggedInKey = 'isLoggedIn';
  static const String _userIdKey = 'userId';
  static const String _usernameKey = 'username';
  
  // Default admin credentials
  static const String defaultUsername = 'admin';
  static const String defaultPassword = 'admin123';

  static Future<bool> login(String username, String password) async {
    try {
      final databaseHelper = DatabaseHelper();
      
      // First check default admin credentials
      if (username == defaultUsername && password == defaultPassword) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool(_isLoggedInKey, true);
        await prefs.setString(_userIdKey, 'admin');
        await prefs.setString(_usernameKey, username);
        return true;
      }
      
      // Check database for admins
      final admins = await databaseHelper.getAllAdmins();
      for (var admin in admins) {
        if (admin['username'] == username && 
            admin['password'] == password && 
            admin['is_active'] == 1) {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setBool(_isLoggedInKey, true);
          await prefs.setString(_userIdKey, admin['id']);
          await prefs.setString(_usernameKey, admin['username']);
          await prefs.setString('fullName', admin['full_name']);
          await prefs.setString('userRole', admin['role']);
          await prefs.setString('userPermissions', admin['permissions'] ?? '');
          return true;
        }
      }
      
      return false;
    } catch (e) {
      print('Error during login: $e');
      return false;
    }
  }

  static Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }

  static Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isLoggedInKey) ?? false;
  }

  static Future<String?> getCurrentUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userIdKey);
  }

  static Future<String?> getCurrentUsername() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_usernameKey);
  }

  static Future<String?> getCurrentFullName() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('fullName');
  }

  static Future<String?> getCurrentUserRole() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('userRole');
  }

  static Future<List<String>> getCurrentUserPermissions() async {
    final prefs = await SharedPreferences.getInstance();
    final permissionsString = prefs.getString('userPermissions');
    if (permissionsString != null && permissionsString.isNotEmpty) {
      return permissionsString.split(',');
    }
    
    // إذا كان المستخدم هو المدير الرئيسي، اعطه كل الصلاحيات
    final username = await getCurrentUsername();
    if (username == 'admin') {
      return [
        'booking_receipt', 'bookings', 'delivery', 'cashbox', 'services', 'customers', 
        'reports', 'notifications', 'admins', 'todo', 'backup', 'settings'
      ];
    }
    
    return [];
  }

  static Future<bool> hasPermission(String permission) async {
    final permissions = await getCurrentUserPermissions();
    return permissions.contains(permission);
  }

  static Future<User?> getCurrentUser() async {
    final prefs = await SharedPreferences.getInstance();
    final userId = prefs.getString(_userIdKey);
    final username = prefs.getString(_usernameKey);
    
    if (userId != null && username != null) {
      return User(
        id: userId,
        username: username,
        password: '', // Don't store password
        role: 'admin',
        createdAt: DateTime.now(),
        lastLogin: DateTime.now(),
      );
    }
    return null;
  }
}
