import 'package:flutter/material.dart';
import '../services/database_helper.dart';
import '../utils/currency_formatter.dart';
import '../utils/app_colors.dart';
import '../utils/date_formatter.dart';
import 'cashbox_movements_screen.dart';

class CashboxManagementScreen extends StatefulWidget {
  const CashboxManagementScreen({Key? key}) : super(key: key);

  @override
  State<CashboxManagementScreen> createState() => _CashboxManagementScreenState();
}

class _CashboxManagementScreenState extends State<CashboxManagementScreen> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<Map<String, dynamic>> _cashboxes = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCashboxes();
  }

  Future<void> _loadCashboxes() async {
    setState(() => _isLoading = true);
    try {
      final cashboxes = await _databaseHelper.getAllCashboxes();
      
      // Load summary for each cashbox
      List<Map<String, dynamic>> cashboxesWithSummary = [];
      for (var cashbox in cashboxes) {
        final summary = await _databaseHelper.getCashboxSummary(cashbox['id']);
        final currentBalance = await _databaseHelper.getCashboxBalance(cashbox['id']);
        
        // Load movement statistics
        final movements = await _databaseHelper.getCashboxMovements(cashboxId: cashbox['id']);
        final depositCount = movements.where((m) => m['type'] == 'deposit').length;
        final withdrawalCount = movements.where((m) => m['type'] == 'withdrawal').length;
        final transferCount = movements.where((m) => m['type'] == 'transfer').length;
        
        cashboxesWithSummary.add({
          ...cashbox,
          'summary': summary,
          'current_balance': currentBalance,
          'deposit_count': depositCount,
          'withdrawal_count': withdrawalCount,
          'transfer_count': transferCount,
          'total_movements': movements.length,
        });
      }
      
      setState(() {
        _cashboxes = cashboxesWithSummary;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الصناديق: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _showCreateCashboxDialog() {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    final locationController = TextEditingController();
    String selectedCurrency = 'IQD';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.add_box, color: AppColors.primaryBlue),
            SizedBox(width: 12),
            Text('إنشاء صندوق جديد'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: nameController,
                decoration: InputDecoration(
                  labelText: 'اسم الصندوق *',
                  prefixIcon: Icon(Icons.label),
                  border: OutlineInputBorder(),
                  hintText: 'مثال: صندوق الاستقبال',
                ),
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: descriptionController,
                decoration: InputDecoration(
                  labelText: 'الوصف',
                  prefixIcon: Icon(Icons.description),
                  border: OutlineInputBorder(),
                  hintText: 'وصف مختصر للصندوق',
                ),
                maxLines: 2,
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: locationController,
                decoration: InputDecoration(
                  labelText: 'الموقع *',
                  prefixIcon: Icon(Icons.location_on),
                  border: OutlineInputBorder(),
                  hintText: 'مثال: الطابق الأول - قسم التصوير',
                ),
              ),
              SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: selectedCurrency,
                decoration: InputDecoration(
                  labelText: 'العملة',
                  prefixIcon: Icon(Icons.attach_money),
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 'IQD', child: Text('دينار عراقي (IQD)')),
                  DropdownMenuItem(value: 'USD', child: Text('دولار أمريكي (USD)')),
                  DropdownMenuItem(value: 'EUR', child: Text('يورو (EUR)')),
                ],
                onChanged: (value) => selectedCurrency = value!,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('يرجى إدخال اسم الصندوق'),
                    backgroundColor: AppColors.error,
                  ),
                );
                return;
              }

              if (locationController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('يرجى إدخال موقع الصندوق'),
                    backgroundColor: AppColors.error,
                  ),
                );
                return;
              }

              try {
                await _databaseHelper.createCashbox(
                  name: nameController.text.trim(),
                  description: descriptionController.text.trim(),
                  location: locationController.text.trim(),
                  currency: selectedCurrency,
                  createdBy: 'المدير', // TODO: Get from AuthService
                );

                Navigator.pop(context);
                _loadCashboxes();

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('تم إنشاء الصندوق بنجاح'),
                    backgroundColor: AppColors.success,
                  ),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('خطأ في إنشاء الصندوق: $e'),
                    backgroundColor: AppColors.error,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: Colors.white,
            ),
            child: Text('إنشاء'),
          ),
        ],
      ),
    );
  }

  void _showEditCashboxDialog(Map<String, dynamic> cashbox) {
    final nameController = TextEditingController(text: cashbox['name']);
    final descriptionController = TextEditingController(text: cashbox['description'] ?? '');
    final locationController = TextEditingController(text: cashbox['location'] ?? '');
    String selectedCurrency = cashbox['currency'] ?? 'IQD';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.edit, color: AppColors.warning),
            SizedBox(width: 12),
            Text('تعديل الصندوق'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: nameController,
                decoration: InputDecoration(
                  labelText: 'اسم الصندوق *',
                  prefixIcon: Icon(Icons.label),
                  border: OutlineInputBorder(),
                ),
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: descriptionController,
                decoration: InputDecoration(
                  labelText: 'الوصف',
                  prefixIcon: Icon(Icons.description),
                  border: OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: locationController,
                decoration: InputDecoration(
                  labelText: 'الموقع *',
                  prefixIcon: Icon(Icons.location_on),
                  border: OutlineInputBorder(),
                ),
              ),
              SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: selectedCurrency,
                decoration: InputDecoration(
                  labelText: 'العملة',
                  prefixIcon: Icon(Icons.attach_money),
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 'IQD', child: Text('دينار عراقي (IQD)')),
                  DropdownMenuItem(value: 'USD', child: Text('دولار أمريكي (USD)')),
                  DropdownMenuItem(value: 'EUR', child: Text('يورو (EUR)')),
                ],
                onChanged: (value) => selectedCurrency = value!,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('يرجى إدخال اسم الصندوق'),
                    backgroundColor: AppColors.error,
                  ),
                );
                return;
              }

              try {
                await _databaseHelper.updateCashbox(
                  cashboxId: cashbox['id'],
                  name: nameController.text.trim(),
                  description: descriptionController.text.trim(),
                  location: locationController.text.trim(),
                  currency: selectedCurrency,
                );

                Navigator.pop(context);
                _loadCashboxes();

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('تم تحديث الصندوق بنجاح'),
                    backgroundColor: AppColors.success,
                  ),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('خطأ في تحديث الصندوق: $e'),
                    backgroundColor: AppColors.error,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.warning,
              foregroundColor: Colors.white,
            ),
            child: Text('تحديث'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(Map<String, dynamic> cashbox) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning, color: AppColors.error),
            SizedBox(width: 12),
            Text('تأكيد الحذف'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('هل أنت متأكد من حذف الصندوق التالي؟'),
            SizedBox(height: 12),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.veryLightBlue,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    cashbox['name'],
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  if (cashbox['location'] != null) ...[
                    SizedBox(height: 4),
                    Text('الموقع: ${cashbox['location']}'),
                  ],
                ],
              ),
            ),
            SizedBox(height: 12),
            Text(
              'تحذير: سيتم إلغاء تفعيل الصندوق ولن يظهر في القائمة.',
              style: TextStyle(
                color: AppColors.error,
                fontSize: 12,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                await _databaseHelper.deactivateCashbox(cashbox['id']);
                Navigator.pop(context);
                _loadCashboxes();

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('تم حذف الصندوق بنجاح'),
                    backgroundColor: AppColors.success,
                  ),
                );
              } catch (e) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('خطأ في حذف الصندوق: $e'),
                    backgroundColor: AppColors.error,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: Text('حذف'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('إدارة الصناديق'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadCashboxes,
            tooltip: 'تحديث القائمة',
          ),
          IconButton(
            icon: Icon(Icons.add),
            onPressed: _showCreateCashboxDialog,
            tooltip: 'إضافة صندوق جديد',
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.veryLightBlue,
              AppColors.offWhite,
            ],
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _cashboxes.isEmpty
                ? _buildEmptyState()
                : _buildCashboxList(),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_balance_wallet_outlined,
            size: 80,
            color: AppColors.lightGray,
          ),
          SizedBox(height: 16),
          Text(
            'لا توجد صناديق',
            style: TextStyle(
              fontSize: 18,
              color: AppColors.secondaryText,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'ابدأ بإنشاء صندوق جديد',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.secondaryText,
            ),
          ),
          SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showCreateCashboxDialog,
            icon: Icon(Icons.add),
            label: Text('إنشاء صندوق جديد'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryBlue,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCashboxList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _cashboxes.length,
      itemBuilder: (context, index) {
        final cashbox = _cashboxes[index];
        final summary = cashbox['summary'] as Map<String, dynamic>;
        
        return Card(
          elevation: 4,
          margin: const EdgeInsets.only(bottom: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.primaryBlue.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.account_balance_wallet,
                        color: AppColors.primaryBlue,
                        size: 24,
                      ),
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            cashbox['name'],
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppColors.primaryText,
                            ),
                          ),
                          if (cashbox['location'] != null) ...[
                            SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(
                                  Icons.location_on,
                                  size: 16,
                                  color: AppColors.secondaryText,
                                ),
                                SizedBox(width: 4),
                                Text(
                                  cashbox['location'],
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: AppColors.secondaryText,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                    // Actions
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        switch (value) {
                          case 'edit':
                            _showEditCashboxDialog(cashbox);
                            break;
                          case 'delete':
                            _showDeleteConfirmation(cashbox);
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, color: AppColors.warning),
                              SizedBox(width: 8),
                              Text('تعديل'),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, color: AppColors.error),
                              SizedBox(width: 8),
                              Text('حذف'),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                
                // Description
                if (cashbox['description'] != null && cashbox['description'].toString().isNotEmpty) ...[
                  SizedBox(height: 12),
                  Text(
                    cashbox['description'],
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.secondaryText,
                    ),
                  ),
                ],
                
                SizedBox(height: 16),
                
                // Financial Summary
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.veryLightBlue,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      // الرصيد الحالي - أهم معلومة
                      GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => CashboxMovementsScreen(
                                cashboxId: cashbox['id'],
                                cashboxName: cashbox['name'],
                              ),
                            ),
                          );
                        },
                        child: Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                AppColors.primaryBlue,
                                AppColors.info,
                              ],
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.account_balance_wallet,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    'الرصيد الحالي',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Icon(
                                    Icons.arrow_forward_ios,
                                    color: Colors.white70,
                                    size: 14,
                                  ),
                                ],
                              ),
                              SizedBox(height: 6),
                              Text(
                                CurrencyFormatter.format(cashbox['current_balance'] ?? 0.0),
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 4),
                              Text(
                                'اضغط لعرض الحركات',
                                style: TextStyle(
                                  color: Colors.white70,
                                  fontSize: 11,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 12),
                      // الصف الأول - المالية
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _buildSummaryItem(
                            'إجمالي الإيرادات',
                            summary['total_income'] ?? 0.0,
                            AppColors.success,
                            isCompact: true,
                          ),
                          _buildSummaryItem(
                            'إجمالي المصروفات',
                            summary['total_expenses'] ?? 0.0,
                            AppColors.error,
                            isCompact: true,
                          ),
                        ],
                      ),
                      SizedBox(height: 6),
                      // الصف الثاني - الصافي والجلسات
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _buildSummaryItem(
                            'الصافي',
                            summary['net_amount'] ?? 0.0,
                            AppColors.primaryBlue,
                            isCompact: true,
                          ),
                          _buildSummaryItem(
                            'الجلسات النشطة',
                            (summary['active_sessions'] ?? 0).toDouble(),
                            AppColors.info,
                            isCount: true,
                            isCompact: true,
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      // الصف الثالث - إحصائيات العمليات (أصغر)
                      Container(
                        padding: EdgeInsets.symmetric(vertical: 6, horizontal: 8),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.6),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            _buildSmallCountItem('إيداعات', cashbox['deposit_count'] ?? 0, Icons.add_circle_outline, AppColors.success),
                            _buildSmallCountItem('سحوبات', cashbox['withdrawal_count'] ?? 0, Icons.remove_circle_outline, AppColors.error),
                            _buildSmallCountItem('تحويلات', cashbox['transfer_count'] ?? 0, Icons.swap_horiz, AppColors.warning),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                
                SizedBox(height: 12),
                
                // Footer Info
                Row(
                  children: [
                    Icon(
                      Icons.attach_money,
                      size: 14,
                      color: AppColors.secondaryText,
                    ),
                    SizedBox(width: 4),
                    Text(
                      cashbox['currency'] ?? 'IQD',
                      style: TextStyle(
                        fontSize: 11,
                        color: AppColors.secondaryText,
                      ),
                    ),
                    SizedBox(width: 12),
                    Icon(
                      Icons.access_time,
                      size: 14,
                      color: AppColors.secondaryText,
                    ),
                    SizedBox(width: 4),
                    Text(
                      'تم الإنشاء: ${DateFormatter.formatDateTimeShort(DateTime.parse(cashbox['created_at']))}',
                      style: TextStyle(
                        fontSize: 11,
                        color: AppColors.secondaryText,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSummaryItem(String label, double value, Color color, {bool isCount = false, bool isCompact = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isCompact ? 10 : 12,
            color: AppColors.secondaryText,
          ),
        ),
        SizedBox(height: isCompact ? 2 : 4),
        Text(
          isCount ? value.toInt().toString() : CurrencyFormatter.format(value),
          style: TextStyle(
            fontSize: isCompact ? 12 : 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildSmallCountItem(String label, int count, IconData icon, Color color) {
    return Column(
      children: [
        Icon(
          icon,
          size: 16,
          color: color,
        ),
        SizedBox(height: 2),
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 9,
            color: AppColors.secondaryText,
          ),
        ),
      ],
    );
  }
}
